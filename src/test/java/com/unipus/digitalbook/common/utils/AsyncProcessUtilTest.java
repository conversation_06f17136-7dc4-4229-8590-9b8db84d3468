package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BizException;
import jakarta.annotation.Resource;
import jodd.util.ThreadUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AsyncProcessUtil 全面功能测试
 * 测试修复后的功能：参数验证、状态管理、异常处理、并发安全等
 */
@SpringBootTest
class AsyncProcessUtilTest {

    @Resource
    private AsyncProcessUtil<String> asyncProcessUtil;

    // ========== 参数验证测试 ==========

    @Test
    @DisplayName("测试空标识符参数验证")
    void testNullIdentifierValidation() {
        // 测试null标识符
        IllegalArgumentException exception1 = assertThrows(IllegalArgumentException.class, () -> {
            asyncProcessUtil.process(false, null, () -> "test", "default");
        });
        assertEquals("任务标识符不能为空", exception1.getMessage());

        // 测试空字符串标识符
        IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class, () ->
                asyncProcessUtil.process(false, "", () -> "test", "default"));
        assertEquals("任务标识符不能为空", exception2.getMessage());

        // 测试空白字符串标识符
        IllegalArgumentException exception3 = assertThrows(IllegalArgumentException.class, () ->
                asyncProcessUtil.process(false, "   ", () -> "test", "default"));
        assertEquals("任务标识符不能为空", exception3.getMessage());
    }

    @Test
    @DisplayName("测试空任务参数验证")
    void testNullTaskValidation() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                asyncProcessUtil.process(false, "test-id", null, "default"));
        assertEquals("任务不能为空", exception.getMessage());
    }

    // ========== 重复提交防护测试 ==========

    @Test
    @DisplayName("测试重复提交防护")
    void testDuplicateSubmissionPrevention() throws InterruptedException {
        String identifier = "test-duplicate-" + System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(1);

        // 启动一个长时间运行的异步任务
        asyncProcessUtil.process(true, identifier, () -> {
            try {
                latch.await(5, TimeUnit.SECONDS);
                return "完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }, "默认值");

        // 等待任务开始运行
        Thread.sleep(100);

        // 尝试重复提交相同标识符的任务
        BizException exception = assertThrows(BizException.class, () -> {
            asyncProcessUtil.process(false, identifier, () -> "另一个任务", "另一个默认值");
        });

        // 验证异常信息
        assertNotNull(exception.getMessage());

        // 释放第一个任务
        latch.countDown();
    }

    // ========== 异常处理测试 ==========

    @Test
    @DisplayName("测试异步任务异常缓存")
    void testAsyncExceptionCaching() throws InterruptedException {
        String identifier = "test-async-exception-" + System.currentTimeMillis();
        String message = "异步异常消息";
        
        // 启动一个会抛出异常的异步任务
        String result = asyncProcessUtil.process(true, identifier, () -> {
            throw new RuntimeException(message);
        }, "默认值");
        
        assertEquals("默认值", result);
        
        // 等待异步任务完成
        Thread.sleep(1000);
        
        // 获取状态信息
        try {
            asyncProcessUtil.getProcessState(identifier);
        } catch (Exception e) {
            assertEquals(message, e.getMessage());
        }
    }

    @Test
    @DisplayName("测试同步任务异常缓存")
    void testSyncExceptionCaching() {
        String identifier = "test-sync-exception-" + System.currentTimeMillis();
        String message = "同步异常消息";
        
        // 启动一个会抛出异常的同步任务
        assertThrows(IllegalArgumentException.class, () -> {
            asyncProcessUtil.process(false, identifier, () -> {
                throw new IllegalArgumentException(message);
            }, null);
        });
        
        // 获取状态信息
        try {
            asyncProcessUtil.getProcessState(identifier);
        } catch (Exception e) {
            assertEquals(message, e.getMessage());
        }
    }

    // ========== 状态管理测试 ==========

    @Test
    @DisplayName("测试任务运行状态检查")
    void testRunningTaskState() {
        String identifier = "test-running-" + System.currentTimeMillis();

        // 启动一个长时间运行的异步任务
        asyncProcessUtil.process(true, identifier, () -> {
            ThreadUtil.sleep(5000);
            return "完成";
        }, "默认值");
        
        // 验证 isProcessRunning 方法
        assertTrue(asyncProcessUtil.isProcessRunning(identifier));
    }

    @Test
    @DisplayName("测试不存在的任务状态")
    void testNonExistentTask() {
        String identifier = "non-existent-task";
        
        // 获取不存在的任务状态
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        
        assertNull(stateInfo);
        assertFalse(asyncProcessUtil.isProcessRunning(identifier));
    }

    @Test
    @DisplayName("测试状态管理改进 - 运行中状态不被删除")
    void testStateManagementImprovement() throws InterruptedException {
        String identifier = "test-state-management-" + System.currentTimeMillis();
        CountDownLatch taskStarted = new CountDownLatch(1);
        CountDownLatch taskCanFinish = new CountDownLatch(1);

        // 启动异步任务
        asyncProcessUtil.process(true, identifier, () -> {
            taskStarted.countDown();
            try {
                taskCanFinish.await(5, TimeUnit.SECONDS);
                return "完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }, "默认值");

        // 等待任务开始
        assertTrue(taskStarted.await(2, TimeUnit.SECONDS));

        // 第一次检查状态 - 应该是运行中，且不会被删除
        assertTrue(asyncProcessUtil.isProcessRunning(identifier));

        // 第二次检查状态 - 仍然应该是运行中（验证状态没有被删除）
        assertTrue(asyncProcessUtil.isProcessRunning(identifier));

        // 让任务完成
        taskCanFinish.countDown();

        // 等待任务完成
        Thread.sleep(500);

        // 检查完成状态 - 这次应该会删除状态
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        assertEquals("finished", stateInfo.state());

        // 再次检查 - 状态应该已被删除
        assertNull(asyncProcessUtil.getProcessState(identifier));
    }

    @Test
    @DisplayName("测试手动清理状态功能")
    void testClearProcessState() throws InterruptedException {
        String identifier = "test-clear-state-" + System.currentTimeMillis();

        // 启动并完成一个任务
        String result = asyncProcessUtil.process(false, identifier, () -> "测试结果", "默认值");
        assertEquals("测试结果", result);

        // 验证状态存在
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        assertEquals("finished", stateInfo.state());

        // 重新设置状态用于测试清理
        asyncProcessUtil.process(false, identifier + "-2", () -> "另一个结果", "默认值");

        // 测试清理功能
        assertTrue(asyncProcessUtil.clearProcessState(identifier + "-2"));

        // 验证状态已被清理
        assertNull(asyncProcessUtil.getProcessState(identifier + "-2"));

        // 测试清理不存在的状态
        assertFalse(asyncProcessUtil.clearProcessState("non-existent-id"));

        // 测试清理空标识符
        assertFalse(asyncProcessUtil.clearProcessState(null));
        assertFalse(asyncProcessUtil.clearProcessState(""));
        assertFalse(asyncProcessUtil.clearProcessState("   "));
    }

    // ========== 同步/异步执行测试 ==========

    @Test
    @DisplayName("测试同步执行返回正确结果")
    void testSyncExecution() {
        String identifier = "test-sync-" + System.currentTimeMillis();
        String expectedResult = "同步执行结果";

        String result = asyncProcessUtil.process(false, identifier, () -> expectedResult, "默认值");

        assertEquals(expectedResult, result);

        // 验证状态为完成
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        assertEquals("finished", stateInfo.state());
    }

    @Test
    @DisplayName("测试异步执行返回默认值")
    void testAsyncExecution() throws InterruptedException {
        String identifier = "test-async-" + System.currentTimeMillis();
        String defaultResult = "异步默认值";
        AtomicReference<String> taskResult = new AtomicReference<>();

        String result = asyncProcessUtil.process(true, identifier, () -> {
            String actualResult = "异步执行结果";
            taskResult.set(actualResult);
            return actualResult;
        }, defaultResult);

        // 异步执行应该立即返回默认值
        assertEquals(defaultResult, result);

        // 等待异步任务完成
        Thread.sleep(1000);

        // 验证任务实际执行了
        assertEquals("异步执行结果", taskResult.get());

        // 验证状态为完成
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        assertEquals("finished", stateInfo.state());
    }

    // ========== 并发安全测试 ==========

    @Test
    @DisplayName("测试并发访问安全性")
    void testConcurrentAccess() throws InterruptedException {
        String identifier = "test-concurrent-" + System.currentTimeMillis();
        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(threadCount);
        AtomicBoolean hasException = new AtomicBoolean(false);
        AtomicReference<Exception> caughtException = new AtomicReference<>();

        // 启动多个线程同时尝试执行相同标识符的任务
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    startLatch.await();
                    asyncProcessUtil.process(false, identifier, () -> {
                        ThreadUtil.sleep(100);
                        return "结果";
                    }, "默认值");
                } catch (Exception e) {
                    if (e instanceof BizException) {
                        // 预期的重复提交异常
                        hasException.set(true);
                    } else {
                        caughtException.set(e);
                    }
                } finally {
                    finishLatch.countDown();
                }
            }).start();
        }

        // 同时启动所有线程
        startLatch.countDown();

        // 等待所有线程完成
        assertTrue(finishLatch.await(10, TimeUnit.SECONDS));

        // 应该有重复提交异常发生
        assertTrue(hasException.get());

        // 不应该有其他异常
        assertNull(caughtException.get());
    }
}
