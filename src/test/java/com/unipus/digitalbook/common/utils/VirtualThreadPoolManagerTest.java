package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 虚拟线程池管理器测试类
 */
@SpringBootTest
@Slf4j
public class VirtualThreadPoolManagerTest {

    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    @Test
    public void testVirtualThreadExecution() throws InterruptedException {
        int taskCount = 10;
        CountDownLatch latch = new CountDownLatch(taskCount);

        log.info("开始提交{}个虚拟线程任务", taskCount);

        // 提交多个异步任务
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            virtualThreadPoolManager.executeAsync(() -> {
                try {
                    log.info("虚拟线程任务{}开始执行", taskId);
                    // 模拟耗时操作
                    Thread.sleep(2000);
                    log.info("虚拟线程任务{}执行完成", taskId);
                } catch (InterruptedException e) {
                    log.warn("虚拟线程任务{}被中断", taskId);
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        log.info("所有任务是否完成: {}", completed);
    }

    @Test
    public void testCompletableFutureWithVirtualThread() {
        log.info("测试CompletableFuture与虚拟线程池的结合使用");

        CompletableFuture<String> future1 = virtualThreadPoolManager.supplyAsync(() -> {
            try {
                Thread.sleep(1000);
                return "任务1完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "任务1被中断";
            }
        });

        CompletableFuture<String> future2 = virtualThreadPoolManager.supplyAsync(() -> {
            try {
                Thread.sleep(1500);
                return "任务2完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "任务2被中断";
            }
        });

        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(future1, future2);
        
        try {
            allTasks.get(5, TimeUnit.SECONDS);
            log.info("任务1结果: {}", future1.get());
            log.info("任务2结果: {}", future2.get());
        } catch (Exception e) {
            log.error("任务执行异常", e);
        }
    }

    @Test
    public void testBatchProcessing() throws InterruptedException {
        log.info("测试批处理虚拟线程池");

        int batchSize = 5;
        CountDownLatch latch = new CountDownLatch(batchSize);

        for (int i = 0; i < batchSize; i++) {
            final int batchId = i;
            virtualThreadPoolManager.executeBatchAsync(() -> {
                try {
                    log.info("批处理任务{}开始执行", batchId);
                    Thread.sleep(1000);
                    log.info("批处理任务{}执行完成", batchId);
                } catch (InterruptedException e) {
                    log.warn("批处理任务{}被中断", batchId);
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        boolean completed = latch.await(5, TimeUnit.SECONDS);
        log.info("所有批处理任务是否完成: {}", completed);
    }

    @Test
    public void testMDCContextPropagation() throws InterruptedException {
        log.info("测试MDC上下文传递");

        // 设置MDC上下文
        String requestId = "test-request-123";
        MDC.put("requestId", requestId);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> capturedRequestId = new AtomicReference<>();

        // 执行异步任务
        virtualThreadPoolManager.executeAsync(() -> {
            // 在虚拟线程中获取MDC值
            String mdcRequestId = MDC.get("requestId");
            capturedRequestId.set(mdcRequestId);
            log.info("虚拟线程中获取到的requestId: {}", mdcRequestId);
            latch.countDown();
        });

        // 等待异步任务完成
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // 验证MDC上下文是否正确传递
        assertEquals(requestId, capturedRequestId.get());
        log.info("MDC上下文传递测试通过");

        // 清理MDC
        MDC.clear();
    }

    @Test
    public void testMDCContextInSupplyAsync() throws Exception {
        log.info("测试supplyAsync中的MDC上下文传递");

        // 设置MDC上下文
        String requestId = "test-supply-456";
        MDC.put("requestId", requestId);

        // 执行异步任务并获取结果
        CompletableFuture<String> future = virtualThreadPoolManager.supplyAsync(() -> {
            String mdcRequestId = MDC.get("requestId");
            log.info("supplyAsync虚拟线程中获取到的requestId: {}", mdcRequestId);
            return mdcRequestId;
        });

        // 获取结果
        String result = future.get(5, TimeUnit.SECONDS);

        // 验证MDC上下文是否正确传递
        assertEquals(requestId, result);
        log.info("supplyAsync MDC上下文传递测试通过");

        // 清理MDC
        MDC.clear();
    }

    @Test
    public void testMDCContextInBatchAsync() throws InterruptedException {
        log.info("测试批处理异步任务中的MDC上下文传递");

        // 设置MDC上下文
        String requestId = "test-batch-789";
        MDC.put("requestId", requestId);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> capturedRequestId = new AtomicReference<>();

        // 执行批处理异步任务
        virtualThreadPoolManager.executeBatchAsync(() -> {
            String mdcRequestId = MDC.get("requestId");
            capturedRequestId.set(mdcRequestId);
            log.info("批处理虚拟线程中获取到的requestId: {}", mdcRequestId);
            latch.countDown();
        });

        // 等待异步任务完成
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // 验证MDC上下文是否正确传递
        assertEquals(requestId, capturedRequestId.get());
        log.info("批处理异步任务MDC上下文传递测试通过");

        // 清理MDC
        MDC.clear();
    }

    @Test
    public void testMDCContextWithNullValue() throws InterruptedException {
        log.info("测试空MDC上下文的处理");

        // 确保MDC为空
        MDC.clear();

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> capturedRequestId = new AtomicReference<>();

        // 执行异步任务
        virtualThreadPoolManager.executeAsync(() -> {
            String mdcRequestId = MDC.get("requestId");
            capturedRequestId.set(mdcRequestId);
            log.info("空MDC上下文时虚拟线程中获取到的requestId: {}", mdcRequestId);
            latch.countDown();
        });

        // 等待异步任务完成
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // 验证MDC值为null
        assertNull(capturedRequestId.get());
        log.info("空MDC上下文处理测试通过");
    }
}
