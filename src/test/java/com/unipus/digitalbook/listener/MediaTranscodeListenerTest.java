package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 媒体转码监听器测试
 * 测试Redis处理标识和异步处理逻辑
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local-test")
public class MediaTranscodeListenerTest {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MediaTranscodeListener mediaTranscodeListener;

    private static final String TEST_TOPIC = "dev-ipublish-mediaTranscode";

    @Test
    public void testSendMessage() {
        try {
            MediaTranscodeMessage message = new MediaTranscodeMessage();
            message.setUrl("https://test.example.com/test-video.mp4");
            message.setHash("test-hash-123");
            message.setRetryCount(0);

            String messageJson = JsonUtil.toJsonString(message);
            kafkaTemplate.send(TEST_TOPIC, messageJson);
            
            log.info("测试消息已发送到Kafka: {}", messageJson);
            
            // 等待一段时间让消息被处理
            Thread.sleep(2000);
            
        } catch (Exception e) {
            log.error("测试发送消息失败", e);
        }
    }

    @Test
    public void testRedisProcessingFlag() {
        try {
            String testKey = "test:media:transcode:processing:test-key";
            
            // 测试设置处理标识
            stringRedisTemplate.opsForValue().set(testKey, "processing", 600, TimeUnit.SECONDS);
            log.info("设置Redis处理标识: {}", testKey);
            
            // 检查标识是否存在
            Boolean exists = stringRedisTemplate.hasKey(testKey);
            log.info("Redis处理标识存在: {}", exists);
            
            // 清除标识
            stringRedisTemplate.delete(testKey);
            log.info("清除Redis处理标识: {}", testKey);
            
            // 再次检查
            exists = stringRedisTemplate.hasKey(testKey);
            log.info("清除后Redis处理标识存在: {}", exists);
            
        } catch (Exception e) {
            log.error("测试Redis处理标识失败", e);
        }
    }

    @Test
    public void testDuplicateMessageHandling() {
        try {
            MediaTranscodeMessage message = new MediaTranscodeMessage();
            message.setUrl("https://test.example.com/duplicate-test-video.mp4");
            message.setHash("duplicate-hash-456");
            message.setRetryCount(0);

            String messageJson = JsonUtil.toJsonString(message);
            
            // 发送第一条消息
            kafkaTemplate.send(TEST_TOPIC, messageJson);
            log.info("发送第一条测试消息: {}", messageJson);
            
            // 立即发送第二条相同的消息（模拟重复消费）
            kafkaTemplate.send(TEST_TOPIC, messageJson);
            log.info("发送第二条相同测试消息: {}", messageJson);
            
            // 等待处理
            Thread.sleep(5000);
            
        } catch (Exception e) {
            log.error("测试重复消息处理失败", e);
        }
    }

    @Test
    public void testRetryMessage() {
        try {
            MediaTranscodeMessage message = new MediaTranscodeMessage();
            message.setUrl("https://test.example.com/retry-test-video.mp4");
            message.setHash("retry-hash-789");
            message.setRetryCount(1); // 设置重试次数

            String messageJson = JsonUtil.toJsonString(message);
            kafkaTemplate.send(TEST_TOPIC, messageJson);
            
            log.info("发送重试测试消息: {}", messageJson);
            
            // 等待处理
            Thread.sleep(3000);
            
        } catch (Exception e) {
            log.error("测试重试消息失败", e);
        }
    }

    @Test
    public void testProcessingKeyGeneration() {
        try {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = MediaTranscodeListener.class.getDeclaredMethod("getProcessingKey", String.class, String.class);
            method.setAccessible(true);
            
            String key1 = (String) method.invoke(mediaTranscodeListener, "https://test.com/video1.mp4", "hash1");
            String key2 = (String) method.invoke(mediaTranscodeListener, "https://test.com/video2.mp4", "hash2");
            String key3 = (String) method.invoke(mediaTranscodeListener, "https://test.com/video1.mp4", "hash1"); // 相同参数
            
            log.info("处理标识Key1: {}", key1);
            log.info("处理标识Key2: {}", key2);
            log.info("处理标识Key3: {}", key3);
            
            // 验证相同参数生成相同key
            assert key1.equals(key3) : "相同参数应该生成相同的key";
            assert !key1.equals(key2) : "不同参数应该生成不同的key";
            
            log.info("处理标识Key生成测试通过");
            
        } catch (Exception e) {
            log.error("测试处理标识Key生成失败", e);
        }
    }
}
