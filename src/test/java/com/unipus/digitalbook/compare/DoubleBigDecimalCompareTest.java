package com.unipus.digitalbook.compare;

import java.math.BigDecimal;

public class DoubleBigDecimalCompareTest {

        public static void main(String[] args) {
            int iterations = 1_000_000;

            long start = System.nanoTime();
            double d = 1.1;
            for (int i = 0; i < iterations; i++) {
                d *= 1.1;
            }
            long end = System.nanoTime();
            System.out.println("Double 运算时间: " + (end - start) / 1_000_000.0 + " ms");

            start = System.nanoTime();
            BigDecimal bd = new BigDecimal("1.1");
            BigDecimal increment = new BigDecimal("1.1");
            for (int i = 0; i < iterations; i++) {
                bd = bd.multiply(increment);
            }
            end = System.nanoTime();
            System.out.println("BigDecimal 运算时间: " + (end - start) / 1_000_000.0 + " ms");
        }

}
