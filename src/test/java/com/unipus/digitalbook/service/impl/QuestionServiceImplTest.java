package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.VocabularyQuestion;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class QuestionServiceImplTest {
    @Resource
    private QuestionService questionService;

    @Test
    void testVocabularyJudge() {
        List<UserAnswer> userAnswers = new ArrayList<>();
        UserAnswer ua = new UserAnswer();
        ua.setBizAnswerId("d22d861a-0f8b-11f0-801c-7fd676f208ba");
        userAnswers.add(ua);
        VocabularyQuestion vocabularyQuestion = new VocabularyQuestion();
        double judge = vocabularyQuestion.judge(userAnswers.getFirst());
        Assert.isTrue(judge > 0, "judge is null");
    }
}