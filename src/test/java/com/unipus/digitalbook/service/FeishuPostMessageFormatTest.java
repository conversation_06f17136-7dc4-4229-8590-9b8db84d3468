package com.unipus.digitalbook.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unipus.digitalbook.model.dto.feishu.FeishuPostMessage;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 飞书富文本消息格式测试
 */
public class FeishuPostMessageFormatTest {
    
    @Test
    public void testPostMessageFormat() {
        System.out.println("=== 测试富文本消息格式 ===");
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 测试1：简单的富文本消息
            FeishuPostMessage postMessage = buildSimplePostMessage();
            String json = mapper.writeValueAsString(postMessage);
            System.out.println("简单富文本消息JSON格式:");
            System.out.println(json);
            System.out.println();
            
            // 测试2：带链接的富文本消息
            FeishuPostMessage linkMessage = buildPostMessageWithLink();
            String linkJson = mapper.writeValueAsString(linkMessage);
            System.out.println("带链接富文本消息JSON格式:");
            System.out.println(linkJson);
            System.out.println();
            
            // 测试3：复杂富文本消息
            FeishuPostMessage complexMessage = buildComplexPostMessage();
            String complexJson = mapper.writeValueAsString(complexMessage);
            System.out.println("复杂富文本消息JSON格式:");
            System.out.println(complexJson);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private FeishuPostMessage buildSimplePostMessage() {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle("📢 富文本消息标题");
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：标题
        List<FeishuPostMessage.ContentItem> titleRow = new ArrayList<>();
        FeishuPostMessage.ContentItem titleItem = new FeishuPostMessage.ContentItem();
        titleItem.setTag("text");
        titleItem.setText("📢 富文本消息标题");
        titleRow.add(titleItem);
        contentList.add(titleRow);
        
        // 第二行：内容
        List<FeishuPostMessage.ContentItem> contentRow = new ArrayList<>();
        FeishuPostMessage.ContentItem contentItem = new FeishuPostMessage.ContentItem();
        contentItem.setTag("text");
        contentItem.setText("这是一条富文本消息的内容 - " + System.currentTimeMillis());
        contentRow.add(contentItem);
        contentList.add(contentRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
    
    private FeishuPostMessage buildPostMessageWithLink() {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle("🔗 带链接的富文本消息");
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：标题
        List<FeishuPostMessage.ContentItem> titleRow = new ArrayList<>();
        FeishuPostMessage.ContentItem titleItem = new FeishuPostMessage.ContentItem();
        titleItem.setTag("text");
        titleItem.setText("🔗 带链接的富文本消息");
        titleRow.add(titleItem);
        contentList.add(titleRow);
        
        // 第二行：内容
        List<FeishuPostMessage.ContentItem> contentRow = new ArrayList<>();
        FeishuPostMessage.ContentItem contentItem = new FeishuPostMessage.ContentItem();
        contentItem.setTag("text");
        contentItem.setText("这是一条带链接的富文本消息 - " + System.currentTimeMillis());
        contentRow.add(contentItem);
        contentList.add(contentRow);
        
        // 第三行：链接
        List<FeishuPostMessage.ContentItem> linkRow = new ArrayList<>();
        FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
        linkItem.setTag("a");
        linkItem.setText("点击查看详情");
        linkItem.setHref("https://www.example.com");
        linkRow.add(linkItem);
        contentList.add(linkRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
    
    private FeishuPostMessage buildComplexPostMessage() {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle("🎯 复杂富文本消息");
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：标题
        List<FeishuPostMessage.ContentItem> titleRow = new ArrayList<>();
        FeishuPostMessage.ContentItem titleItem = new FeishuPostMessage.ContentItem();
        titleItem.setTag("text");
        titleItem.setText("🎯 复杂富文本消息");
        titleRow.add(titleItem);
        contentList.add(titleRow);
        
        // 第二行：@用户和文本
        List<FeishuPostMessage.ContentItem> userRow = new ArrayList<>();
        FeishuPostMessage.ContentItem atItem = new FeishuPostMessage.ContentItem();
        atItem.setTag("at");
        atItem.setUserId("user123");
        atItem.setUserName("张三");
        userRow.add(atItem);
        
        FeishuPostMessage.ContentItem textItem = new FeishuPostMessage.ContentItem();
        textItem.setTag("text");
        textItem.setText("，这是一条复杂的富文本消息 - " + System.currentTimeMillis());
        userRow.add(textItem);
        contentList.add(userRow);
        
        // 第三行：链接
        List<FeishuPostMessage.ContentItem> linkRow = new ArrayList<>();
        FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
        linkItem.setTag("a");
        linkItem.setText("查看详情");
        linkItem.setHref("https://www.example.com");
        linkRow.add(linkItem);
        contentList.add(linkRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
} 