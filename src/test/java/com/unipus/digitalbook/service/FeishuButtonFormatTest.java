package com.unipus.digitalbook.service;

import com.alibaba.fastjson.JSON;
import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 测试飞书按钮格式
 */
public class FeishuButtonFormatTest {
    
    @Test
    public void testButtonFormat() {
        System.out.println("=== 测试飞书按钮格式 ===");
        
        try {
            // 构建带按钮的卡片消息
            FeishuCardMessage cardMessage = buildCorrectButtonFormat();
            String json = JSON.toJSONString(cardMessage);
            System.out.println("带按钮的卡片消息JSON格式:");
            System.out.println(json);
            System.out.println();
            
            // 验证JSON格式是否正确
            assert json.contains("\"schema\":\"2.0\"");
            assert json.contains("\"tag\":\"button\"");
            assert json.contains("\"type\":\"primary_filled\"");
            assert json.contains("\"behaviors\"");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private FeishuCardMessage buildCorrectButtonFormat() {
        FeishuCardMessage cardMessage = new FeishuCardMessage();
        
        // 设置卡片头部
        FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
        FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
        cardTitle.setContent("错误通知");
        cardTitle.setTag("plain_text");
        header.setTitle(cardTitle);
        cardMessage.setHeader(header);
        
        // 设置卡片主体
        FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
        List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
        
        // 添加错误信息元素
        FeishuCardMessage.CardElement errorElement = new FeishuCardMessage.CardElement();
        errorElement.setTag("markdown");
        errorElement.setContent("**错误信息：**\n系统发生异常");
        errorElement.setTextAlign("left");
        errorElement.setMargin("0px 0px 0px 0px");
        elements.add(errorElement);
        
        // 添加分隔线
        FeishuCardMessage.CardElement dividerElement = new FeishuCardMessage.CardElement();
        dividerElement.setTag("hr");
        dividerElement.setMargin("8px 0px 0px 0px");
        elements.add(dividerElement);
        
        // 添加按钮元素
        FeishuCardMessage.CardElement buttonElement = new FeishuCardMessage.CardElement();
        buttonElement.setTag("button");
        
        // 按钮文本
        FeishuCardMessage.CardText buttonText = new FeishuCardMessage.CardText();
        buttonText.setTag("plain_text");
        buttonText.setContent("查看详细日志");
        buttonElement.setText(buttonText);
        
        // 按钮属性
        buttonElement.setType("primary_filled");
        buttonElement.setWidth("fill");
        buttonElement.setSize("large");
        
        // 按钮图标
        FeishuCardMessage.CardIcon icon = new FeishuCardMessage.CardIcon();
        icon.setTag("standard_icon");
        icon.setToken("search");
        buttonElement.setIcon(icon);
        
        // 按钮行为
        List<FeishuCardMessage.CardBehavior> behaviors = new ArrayList<>();
        FeishuCardMessage.CardBehavior behavior = new FeishuCardMessage.CardBehavior();
        behavior.setType("open_url");
        behavior.setDefaultUrl("https://grafana.example.com/");
        behavior.setPcUrl("");
        behavior.setIosUrl("");
        behavior.setAndroidUrl("");
        behaviors.add(behavior);
        buttonElement.setBehaviors(behaviors);
        
        buttonElement.setMargin("8px 8px 8px 8px");
        elements.add(buttonElement);

//        body.setElements(elements);
        cardMessage.setBody(body);
        
        return cardMessage;
    }
} 