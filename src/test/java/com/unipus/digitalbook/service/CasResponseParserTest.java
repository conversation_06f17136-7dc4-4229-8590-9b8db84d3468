package com.unipus.digitalbook.service;

import com.unipus.digitalbook.service.remote.restful.sso.CasResponseParser;
import com.unipus.digitalbook.service.remote.restful.sso.response.ServiceResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class CasResponseParserTest {
    @InjectMocks
    private CasResponseParser parser;
    private String successXml;
    private String failureXml;
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        successXml = """
            <cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>
                <cas:authenticationSuccess>
                    <cas:user>17011216797</cas:user>
                    <cas:attributes>
                        <cas:role>-1</cas:role>
                        <cas:mobile>17011216797</cas:mobile>
                        <cas:userid>6937269034542ab8e413c1725d6eb117</cas:userid>
                        <cas:phone>17011216797</cas:phone>
                        <cas:school>2635</cas:school>
                        <cas:nickname>u17011216797</cas:nickname>
                        <cas:perms>admin</cas:perms>
                        <cas:fullname>测试数据</cas:fullname>
                        <cas:tempPassWordLogin>N</cas:tempPassWordLogin>
                        <cas:username>17011216797</cas:username>
                    </cas:attributes>
                </cas:authenticationSuccess>
            </cas:serviceResponse>
            """;

        failureXml = """
            <cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>
                <cas:authenticationFailure code='INVALID_TICKET'>
                    INVALID_TICKET
                </cas:authenticationFailure>
            </cas:serviceResponse>
            """;
    }


    @Test
    void testParseSuccessResponse() {
        ServiceResponse response = parser.parseResponse(successXml);

        assertTrue(response.isSuccess());
        assertTrue(response.getAuthenticationSuccess().isPresent());
        assertTrue(response.getAuthenticationFailure().isEmpty());

        response.getAuthenticationSuccess().ifPresent(success -> {
            assertEquals("17011216797", success.getUser());

            assertTrue(success.getAttributes().isPresent());
            success.getAttributes().ifPresent(attrs -> {
                System.out.printf(attrs.toString());
                assertEquals("-1", attrs.getRole());
                assertEquals("17011216797", attrs.getMobile());
                assertEquals("6937269034542ab8e413c1725d6eb117", attrs.getUserid());
                assertEquals("17011216797", attrs.getPhone());
                assertEquals("2635", attrs.getSchool());
                assertEquals("u17011216797", attrs.getNickname());
                assertEquals("admin", attrs.getPerms());
                assertEquals("测试数据", attrs.getFullname());
                assertEquals("N", attrs.getTempPassWordLogin());
                assertEquals("17011216797", attrs.getUsername());
            });
        });
    }

    @Test
    void testParseFailureResponse() {
        ServiceResponse response = parser.parseResponse(failureXml);

        assertFalse(response.isSuccess());
        assertTrue(response.getAuthenticationSuccess().isEmpty());
        assertTrue(response.getAuthenticationFailure().isPresent());

        response.getAuthenticationFailure().ifPresent(failure -> {
            assertEquals("INVALID_TICKET", failure.getCode());
            assertEquals("INVALID_TICKET", failure.getMessage().trim());
        });
    }

    @Test
    void testParseInvalidXml() {
        String invalidXml = "<invalid>xml</invalid>";
        assertThrows(RuntimeException.class, () -> parser.parseResponse(invalidXml));
    }

    @Test
    void testParseEmptyResponse() {
        assertThrows(RuntimeException.class, () -> parser.parseResponse(""));
    }

    @Test
    void testParseNullResponse() {
        assertThrows(RuntimeException.class, () -> parser.parseResponse(null));
    }

    @Test
    void testParsePartialSuccessResponse() {
        String partialXml = """
            <cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>
                <cas:authenticationSuccess>
                    <cas:user>17011216797</cas:user>
                </cas:authenticationSuccess>
            </cas:serviceResponse>
            """;

        ServiceResponse response = parser.parseResponse(partialXml);

        assertTrue(response.isSuccess());
        assertTrue(response.getAuthenticationSuccess().isPresent());

        response.getAuthenticationSuccess().ifPresent(success -> {
            assertEquals("17011216797", success.getUser());
            assertTrue(success.getAttributes().isEmpty());
        });
    }

    @Test
    void testParsePartialFailureResponse() {
        String partialXml = """
            <cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>
                <cas:authenticationFailure code='INVALID_TICKET'>
                </cas:authenticationFailure>
            </cas:serviceResponse>
            """;

        ServiceResponse response = parser.parseResponse(partialXml);

        assertFalse(response.isSuccess());
        assertTrue(response.getAuthenticationFailure().isPresent());

        response.getAuthenticationFailure().ifPresent(failure -> {
            assertEquals("INVALID_TICKET", failure.getCode());
            assertTrue(failure.getMessage() == null || failure.getMessage().trim().isEmpty());
        });
    }

    @Test
    void testProcessResponse_Success() {
        // 为了测试processResponse方法，我们需要捕获输出或验证预期的行为
        assertDoesNotThrow(() -> parser.processResponse(successXml));
    }

    @Test
    void testProcessResponse_Failure() {
        assertDoesNotThrow(() -> parser.processResponse(failureXml));
    }
}
