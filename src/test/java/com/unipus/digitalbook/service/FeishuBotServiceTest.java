package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import com.unipus.digitalbook.service.impl.FeishuBotServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 飞书机器人服务测试类
 */
@SpringBootTest
public class FeishuBotServiceTest {

    @Test
    public void testCollapsiblePanelStructure() {
        // 测试折叠面板结构是否正确
        FeishuCardMessage.CollapsiblePanel panel = new FeishuCardMessage.CollapsiblePanel();
        
        // 设置基本属性
        panel.setElementId("test_panel");
        panel.setDirection("vertical");
        panel.setVerticalSpacing("8px");
        panel.setHorizontalSpacing("8px");
        panel.setVerticalAlign("top");
        panel.setHorizontalAlign("left");
        panel.setPadding("8px 8px 8px 8px");
        panel.setMargin("8px 0px 0px 0px");
        panel.setExpanded(false);
        panel.setBackgroundColor("grey");
        
        // 设置头部
        FeishuCardMessage.CollapsibleHeader header = new FeishuCardMessage.CollapsibleHeader();
        FeishuCardMessage.CardTitle headerTitle = new FeishuCardMessage.CardTitle();
        headerTitle.setTag("markdown");
        headerTitle.setContent("**测试面板**");
        header.setTitle(headerTitle);
        header.setBackgroundColor("grey");
        header.setVerticalAlign("center");
        header.setPadding("4px 0px 4px 8px");
        header.setPosition("top");
        header.setWidth("auto");
        
        // 设置头部图标
        FeishuCardMessage.CardIcon headerIcon = new FeishuCardMessage.CardIcon();
        headerIcon.setTag("standard_icon");
        headerIcon.setToken("info");
        headerIcon.setColor("blue");
        headerIcon.setSize("16px 16px");
        header.setIcon(headerIcon);
        header.setIconPosition("follow_text");
        header.setIconExpandedAngle(-180);
        
        panel.setHeader(header);
        
        // 设置边框
        FeishuCardMessage.CollapsibleBorder border = new FeishuCardMessage.CollapsibleBorder();
        border.setColor("grey");
        border.setCornerRadius("5px");
        panel.setBorder(border);
        
        // 设置内容
        java.util.List<Object> elements = new java.util.ArrayList<>();
        FeishuCardMessage.CardElement contentElement = new FeishuCardMessage.CardElement();
        contentElement.setTag("markdown");
        contentElement.setContent("这是测试内容");
        contentElement.setTextAlign("left");
        contentElement.setMargin("0px 0px 0px 0px");
        elements.add(contentElement);
        
        panel.setElements(elements);
        
        // 验证结构
        assertEquals("collapsible_panel", panel.getTag());
        assertEquals("test_panel", panel.getElementId());
        assertEquals("vertical", panel.getDirection());
        assertEquals(false, panel.isExpanded());
        assertEquals("grey", panel.getBackgroundColor());
        assertNotNull(panel.getHeader());
        assertNotNull(panel.getBorder());
        assertNotNull(panel.getElements());
        assertEquals(1, panel.getElements().size());
        
        // 验证头部结构
        FeishuCardMessage.CollapsibleHeader testHeader = panel.getHeader();
        assertNotNull(testHeader.getTitle());
        assertEquals("markdown", testHeader.getTitle().getTag());
        assertEquals("**测试面板**", testHeader.getTitle().getContent());
        assertNotNull(testHeader.getIcon());
        assertEquals("standard_icon", testHeader.getIcon().getTag());
        assertEquals("info", testHeader.getIcon().getToken());
        assertEquals("blue", testHeader.getIcon().getColor());
        assertEquals("16px 16px", testHeader.getIcon().getSize());
        
        // 验证边框结构
        FeishuCardMessage.CollapsibleBorder testBorder = panel.getBorder();
        assertEquals("grey", testBorder.getColor());
        assertEquals("5px", testBorder.getCornerRadius());
        
        // 验证内容元素
        FeishuCardMessage.CardElement testElement = (FeishuCardMessage.CardElement) panel.getElements().get(0);
        assertEquals("markdown", testElement.getTag());
        assertEquals("这是测试内容", testElement.getContent());
        assertEquals("left", testElement.getTextAlign());
    }
    
    @Test
    public void testCardIconProperties() {
        // 测试CardIcon的新属性
        FeishuCardMessage.CardIcon icon = new FeishuCardMessage.CardIcon();
        icon.setTag("standard_icon");
        icon.setToken("info");
        icon.setColor("blue");
        icon.setImgKey("img_v2_test");
        icon.setSize("16px 16px");
        
        assertEquals("standard_icon", icon.getTag());
        assertEquals("info", icon.getToken());
        assertEquals("blue", icon.getColor());
        assertEquals("img_v2_test", icon.getImgKey());
        assertEquals("16px 16px", icon.getSize());
    }
} 