package com.unipus.digitalbook.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unipus.digitalbook.model.dto.feishu.FeishuPostMessage;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书富文本消息正确格式测试
 */
public class FeishuPostMessageCorrectFormatTest {
    
    @Test
    public void testCorrectPostMessageFormat() {
        System.out.println("=== 测试正确的富文本消息格式 ===");
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 构建正确的富文本消息格式 - 按照你提供的格式
            FeishuPostMessage postMessage = buildCorrectPostMessage();
            String json = mapper.writeValueAsString(postMessage);
            System.out.println("富文本消息JSON格式:");
            System.out.println(json);
            System.out.println();
            
            // 构建完整的消息格式
            FeishuMessage message = new FeishuMessage();
            message.setMsgType("post");
            // 创建正确的content结构：{ "post": { ... } }
            Map<String, Object> content = new HashMap<>();
            content.put("post", postMessage.getPost());
            message.setContent(content);
            String messageJson = mapper.writeValueAsString(message);
            System.out.println("完整消息JSON格式:");
            System.out.println(messageJson);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private FeishuPostMessage buildCorrectPostMessage() {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle("项目更新通知");
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();
        
        // 第一行：文本、链接和@用户在同一行
        List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
        FeishuPostMessage.ContentItem textItem = new FeishuPostMessage.ContentItem();
        textItem.setTag("text");
        textItem.setText("项目有更新: ");
        firstRow.add(textItem);
        
        FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
        linkItem.setTag("a");
        linkItem.setText("请查看");
        linkItem.setHref("http://www.example.com/");
        firstRow.add(linkItem);
        
        FeishuPostMessage.ContentItem atItem = new FeishuPostMessage.ContentItem();
        atItem.setTag("at");
        atItem.setUserId("ou_18eac8********17ad4f02e8bbbb");
        firstRow.add(atItem);
        contentList.add(firstRow);
        
        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }
    
    // 简化的消息类
    static class FeishuMessage {
        private String msgType;
        private Object content;
        
        public String getMsgType() { return msgType; }
        public void setMsgType(String msgType) { this.msgType = msgType; }
        public Object getContent() { return content; }
        public void setContent(Object content) { this.content = content; }
    }
} 