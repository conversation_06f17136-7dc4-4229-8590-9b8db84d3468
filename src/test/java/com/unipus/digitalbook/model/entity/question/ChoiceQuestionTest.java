package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class ChoiceQuestionTest {

    private ChoiceQuestion question;

    @BeforeEach
    public void setUp() {
        // 初始化 ChoiceQuestion 对象并设置默认分数
        question = new ChoiceQuestion();
        question.setScore(BigDecimal.valueOf(10.0));
    }

    // 辅助方法：创建正确答案列表
    private List<QuestionAnswer> createCorrectAnswers(String... answers) {
        List<QuestionAnswer> correctAnswers = new ArrayList<>();
        for (String answer : answers) {
            QuestionAnswer qca = new QuestionAnswer();
            correctAnswers.add(qca);
        }
        return correctAnswers;
    }

    // 辅助方法：创建用户答案列表
    private List<UserAnswer> createUserAnswers(String... answers) {
        List<UserAnswer> userAnswers = new ArrayList<>();
        for (String answer : answers) {
            UserAnswer ua = new UserAnswer();
            ua.setAnswer(answer);
            userAnswers.add(ua);
        }
        return userAnswers;
    }

    // 测试用例 1：用户选择所有正确答案，无错误答案
    @Test
    public void testJudgeAllCorrectNoWrong() {
        question.setAnswers(createCorrectAnswers("A", "B"));
        List<UserAnswer> userAnswers = createUserAnswers("[\"A\", \"B\"]");
        double accuracy = question.judge(userAnswers.getFirst());
        assertEquals(1, accuracy, "所有正确答案被选中，应返回 1");
    }

    //     测试用例 2：用户选择部分正确答案，无错误答案
    @Test
    public void testJudgeSomeCorrectNoWrong() {
        question.setAnswers(createCorrectAnswers("A", "B", "C"));
        List<UserAnswer> userAnswers = createUserAnswers("[\"A\", \"B\"]");
        double accuracy = question.judge(userAnswers.getFirst());
        // 当前实现：wrongCount == 0，返回 1.0
        assertEquals((double) 2 / 3, accuracy, "部分正确答案被选中，无错误答案，当前实现返回 0.6");
    }

    // 测试用例 3：用户选择部分正确答案和部分错误答案
    @Test
    public void testJudgeSomeCorrectSomeWrong() {
        question.setAnswers(createCorrectAnswers("A", "B"));
        List<UserAnswer> userAnswers = createUserAnswers("[\"A\", \"C\"]");
        double accuracy = question.judge(userAnswers.getFirst());
        assertEquals(0, accuracy, "有错误答案被选中，应返回 0.0");
    }

    // 测试用例 4：用户未选择任何答案
    @Test
    public void testJudgeNoAnswers() {
        question.setAnswers(createCorrectAnswers("A", "B"));
        List<UserAnswer> userAnswers = createUserAnswers();
        double accuracy = question.judge(userAnswers.getFirst());
        // 当前实现：wrongCount == 0，返回 1.0
        assertEquals(0, accuracy, "用户未作答，当前实现返回 1.0");
    }

    //    // 测试用例 5：用户只选择错误答案
    @Test
    public void testJudgeOnlyWrongAnswers() {
        question.setAnswers(createCorrectAnswers("A", "B"));
        List<UserAnswer> userAnswers = createUserAnswers("[\"C\", \"D\"]");
        double accuracy = question.judge(userAnswers.getFirst());
        assertEquals(0, accuracy, "只选择错误答案，应返回 0.0");
    }

    // 测试用例 6：正确答案为空，用户未选择任何答案（边缘情况）
    @Test
    public void testJudgeEmptyCorrectAndUserAnswers() {
        question.setAnswers(createCorrectAnswers());
        List<UserAnswer> userAnswers = createUserAnswers();
        double accuracy = question.judge(userAnswers.getFirst());
        assertEquals(1, accuracy, "正确答案和用户答案均为空，当前实现返回 1.0");
    }

    // 测试用例 7：正确答案为空，用户选择答案（边缘情况）
    @Test
    public void testJudgeEmptyCorrectWithUserAnswers() {
        question.setAnswers(createCorrectAnswers());
        List<UserAnswer> userAnswers = createUserAnswers("[\"A\"]");
        double accuracy = question.judge(userAnswers.getFirst());
        assertEquals(0, accuracy, "正确答案为空，用户选择答案，应返回 0.0");
    }


    // 测试 score 方法：accuracy = 1.0
    @Test
    public void testScoreFullAccuracy() {
        double accuracy = 1.0;
        BigDecimal score = question.doScore(accuracy);
        assertEquals(BigDecimal.valueOf(10.0), score, "accuracy = 1.0，应返回满分 10");
    }

    // 测试 score 方法：accuracy = 0.0
    @Test
    public void testScoreZeroAccuracy() {
        double accuracy = 0.0;
        BigDecimal score = question.doScore(accuracy);
        assertEquals(BigDecimal.ZERO, score, "accuracy = 0.0，应返回 0");
    }

    // 测试 score 方法：accuracy = 0.5（部分正确）
    @Test
    public void testScorePartialAccuracy() {
        double accuracy = 0.5;
        BigDecimal score = question.doScore(accuracy);
        assertEquals(0,BigDecimal.valueOf(5.0).compareTo(score) , "accuracy = 0.5，应返回半分 5");
    }

    // 测试 score 方法：accuracy 为负数（边缘情况）
    @Test
    public void testScoreNegativeAccuracy() {
        double accuracy = -0.1;
        BigDecimal score = question.doScore(accuracy);
        assertEquals(0,BigDecimal.valueOf(5.0).compareTo(score) , "accuracy < 0，应返回半分 5");
    }

    // 测试 score 方法：accuracy 超过 1（边缘情况）
    @Test
    public void testScoreExceedAccuracy() {
        double accuracy = 1.5;
        BigDecimal score = question.doScore(accuracy);
        assertEquals(0,BigDecimal.valueOf(5.00).compareTo(score) , "accuracy > 1，应返回半分 5");
    }
}