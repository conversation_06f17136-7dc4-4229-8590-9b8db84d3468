spring:
  application:
    name: DigitalBook
  profiles:
    active: local,base
  # 启用虚拟线程
  threads:
    virtual:
      enabled: true
server:
  port: 8889
  servlet:
    context-path: /api

kafka:
  consumer:
    enable: false

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.unipus.digitalbook.model.po
  type-handlers-package: com.unipus.digitalbook.conf.mybatis.type.handler


management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
      base-path: /NVQW4YLHMVWWK3TU
  server:
    port: 8888
