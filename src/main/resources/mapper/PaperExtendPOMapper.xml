<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperExtendPOMapper">

    <resultMap id="BaseResultMap" type="PaperExtendPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="paper_version_id" property="paperVersionId" jdbcType="BIGINT"/>
        <result column="paper_id" property="paperId" jdbcType="CHAR"/>
        <result column="paper_name" property="paperName" jdbcType="VARCHAR"/>
        <result column="paper_content" property="paperContent" jdbcType="LONGVARCHAR"/>
        <result column="paper_description" property="paperDescription" jdbcType="LONGVARCHAR"/>
        <result column="version_number" property="versionNumber" jdbcType="CHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, paper_version_id, paper_id, paper_name, paper_content, paper_description, version_number,
        create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 插入或更新试卷扩展信息 -->
    <insert id="insertOrUpdate" parameterType="PaperExtendPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO paper_extend(
            paper_version_id,
            paper_id,
            paper_name,
            paper_content,
            paper_description,
            version_number,
            create_by,
            update_by,
            enable
        ) VALUES (
            #{paperVersionId},
            #{paperId},
            #{paperName},
            #{paperContent},
            #{paperDescription},
            #{versionNumber},
            #{createBy},
            #{updateBy},
            #{enable}
        ) ON DUPLICATE KEY UPDATE
            <if test="paperName != null">paper_name = #{paperName},</if>
            <if test="paperContent != null">paper_content = #{paperContent},</if>
            <if test="paperDescription != null">paper_description = #{paperDescription},</if>
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="enable != null">enable = #{enable}</if>
    </insert>

    <!-- 根据ID/PaperId/VersionNumber 更新试卷扩展信息 -->
    <update id="updateDefaultVersion" parameterType="PaperExtendPO">
        UPDATE paper_extend
        <set>
            <if test="paperVersionId != null">paper_version_id = #{paperVersionId},</if>
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="paperName != null">paper_name = #{paperName},</if>
            <if test="paperContent != null">paper_content = #{paperContent},</if>
            <if test="paperDescription != null">paper_description = #{paperDescription},</if>
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="enable != null">enable = #{enable},</if>
            update_time = NOW()
        </set>
        WHERE enable = true
            AND version_number = '0'
            AND paper_id = #{paperId}
    </update>

    <!-- 根据试卷ID和版本号查询试卷扩展信息 -->
    <select id="selectByPaperIdAndVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM paper_extend
        WHERE paper_id = #{paperId} AND version_number = #{versionNumber} AND enable = 1
    </select>

    <!-- 查询试卷的最新版本 -->
    <select id="selectDefaultVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM paper_extend
        WHERE paper_id = #{paperId} AND version_number = '0' AND enable = 1
        LIMIT 1
    </select>

    <!-- 根据试卷ID查询所有版本 -->
    <select id="selectByPaperId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM paper_extend
        WHERE paper_id = #{paperId} AND enable = 1
        ORDER BY version_number DESC
    </select>

    <!-- 根据ID逻辑删除(设置enable为0) -->
    <update id="deleteDefaultVersion">
        UPDATE paper_extend
        SET enable = 0, update_time = NOW()
        WHERE paper_id = #{paperId} AND version_number = '0'
    </update>

    <!-- 通过试卷ID查询【试卷->大题->小题】小题数量/【试卷->题库->大题】大题数量(题库中的大题只有一道小题) 的SQL片段 -->
    <sql id="Question_Count_SQL">
        (SELECT COUNT(1)
         FROM question_group bigQuestion
         LEFT JOIN question_group smallQuestion
            ON smallQuestion.parent_id = bigQuestion.id AND smallQuestion.enable = true
         WHERE bigQuestion.enable = true
           AND bigQuestion.parent_id = ${paperId}
           AND smallQuestion.id IS NOT NULL
         )
    </sql>

    <!-- 试卷信息查询基础SQL -->
    <sql id="Paper_Detail_Column">
        qg.id as id,
        qg.biz_group_id AS paperId,
        pe.paper_name AS paperName,
        qg.type AS questionGroupType,
        pe.paper_description AS description,
        <if test="withContent != null and withContent">pe.paper_content AS content, </if>
        <include refid="Question_Count_SQL"><property name="paperId" value="qg.id"/></include> AS questionCount,
        qg.score AS totalScore,
        qg.create_time AS createTime,
        qg.create_by AS createBy,
        qg.version_number AS versionNumber
    </sql>

    <!-- 根据条件查询最新试卷信息列表 -->
    <select id="selectLatestPaperList" resultType="PaperPO">
        SELECT
            <include refid="Paper_Detail_Column"/>
        FROM question_group qg
        JOIN paper_extend pe ON qg.id = pe.paper_version_id
        WHERE qg.enable = true
            AND qg.version_number = '0'
            <if test="questionGroupType != null">AND qg.type = #{questionGroupType}</if>
            <if test="paperName != null">AND pe.paper_name LIKE CONCAT('%', #{paperName}, '%')</if>
            <if test="paperIds != null and paperIds.size() > 0">
                AND qg.biz_group_id IN
                <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">#{paperId}</foreach>
            </if>
        ORDER BY qg.create_time DESC
    </select>

    <!-- 根据试卷主键ID列表查询最新试卷信息列表 -->
    <select id="selectPaperListByPrimaryId" resultType="PaperPO">
        SELECT
            <include refid="Paper_Detail_Column"/>
        FROM question_group qg
        JOIN paper_extend pe ON qg.id = pe.paper_version_id
        WHERE qg.enable = true
            AND qg.id IN
            <foreach collection="paperPrimaryIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>

    <!-- 根据条件查询指定试卷详情信息 -->
    <select id="selectPaperDetail" resultType="PaperPO">
        SELECT
            <include refid="Paper_Detail_Column"/>
        FROM question_group qg
        JOIN paper_extend pe ON qg.id = pe.paper_version_id
        WHERE qg.enable = true
            AND qg.version_number = #{versionNumber}
            AND qg.biz_group_id = #{paperId}
        ORDER BY qg.create_time DESC
        LIMIT 1
    </select>

    <!-- 根据PaperId/VersionNumber复制扩展信息 -->
    <insert id="insertBySelect">
        INSERT INTO paper_extend(
            paper_version_id,
            paper_id,
            paper_name,
            paper_content,
            paper_description,
            version_number,
            create_by,
            update_by,
            enable
        ) SELECT
            #{paperVersionId} AS paper_version_id,
            paper_id,
            paper_name,
            paper_content,
            paper_description,
            #{targetVersion} AS version_number,
            #{userId} AS create_by,
            #{userId} AS update_by,
            enable
        FROM paper_extend
        WHERE paper_id = #{paperId} AND version_number = #{baseVersion} AND enable = 1
    </insert>

</mapper>