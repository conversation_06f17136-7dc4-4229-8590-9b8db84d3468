<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperScoreTemplatePOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="attribute_type" jdbcType="INTEGER" property="attributeType" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, type, status, create_time, update_time, create_by, update_by, enable, attribute_type, parent_id, version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from paper_score_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from paper_score_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO">
    insert into paper_score_template (id, name, type, 
      status, create_time, update_time, 
      create_by, update_by, enable, attribute_type, parent_id, version
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT}, #{attributeType,jdbcType=INTEGER},
      #{parentId,jdbcType=BIGINT}, #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into paper_score_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="attributeType != null">
        attribute_type,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="attributeType != null">
        #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO">
    update paper_score_template
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
      <if test="attributeType != null">
        attribute_type = #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO">
    update paper_score_template
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      enable = #{enable,jdbcType=BIT},
      attribute_type = #{attributeType,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countByName" resultType="long">
    select count(*) from paper_score_template where enable = 1 and attribute_type = 0 and name = #{name,jdbcType=VARCHAR}
    <if test="id != null">
      and id <![CDATA[ <> ]]> #{id,jdbcType=BIGINT}
    </if>
  </select>

  <update id="updateStatus">
    update paper_score_template
    set
      status = #{status,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectTemplateList" resultMap="BaseResultMap">
    select * from paper_score_template
    where enable = 1 and attribute_type = 0
    <if test="name != null">
      and name like concat('%',#{name,jdbcType=VARCHAR},'%')
    </if>
    <if test="type != null">
      and type = #{type,jdbcType=INTEGER}
    </if>
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
    ORDER BY create_time DESC
    <if test="page != null">
      LIMIT #{page.offset}, #{page.limit}
    </if>
  </select>

  <select id="countTemplateList" resultType="long">
    select count(*) from paper_score_template
    where enable = 1 and attribute_type = 0
    <if test="name != null">
      and name like concat('%',#{name,jdbcType=VARCHAR},'%')
    </if>
    <if test="type != null">
      and type = #{type,jdbcType=INTEGER}
    </if>
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByIdList" resultMap="BaseResultMap">
    select * from paper_score_template
    where id in
    <foreach collection="idList" item="id" open="(" close=")" separator=",">
     #{id}
    </foreach>
  </select>

  <select id="selectEffectTemplateByIdAndType" resultMap="BaseResultMap">
    select * from paper_score_template
    where status = 1 and attribute_type = 1 and type = #{type,jdbcType=INTEGER} and parent_id = #{id}
    order by version desc limit 1
  </select>
</mapper>