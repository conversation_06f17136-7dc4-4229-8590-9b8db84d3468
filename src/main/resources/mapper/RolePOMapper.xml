<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.RolePOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.role.RolePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="desc" column="desc" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="enable" column="enable" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="RoleSearchConditions">
        FROM role
        WHERE role.enable = true and role.id > 0
        <if test="name != null and name != ''">
            AND role.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="status != null">
            AND role.status = #{status}
        </if>
    </sql>
    <sql id="Base_Column_List">
        id,name,`desc`,
        status,enable,create_by,update_by,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByIds" resultType="com.unipus.digitalbook.model.po.role.RolePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role
        where  id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and enable = true
    </select>

    <select id="search" resultType="com.unipus.digitalbook.model.po.role.RolePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        <include refid="RoleSearchConditions" />
        order by role.create_time desc
        limit #{offset}, #{limit};
    </select>

    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="RoleSearchConditions" />
    </select>
    <select id="selectByName" resultType="com.unipus.digitalbook.model.po.role.RolePO">
        select
        <include refid="Base_Column_List" />
        from role
        where name = #{name} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from role
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RolePO" useGeneratedKeys="true">
        insert into role
        ( id,name,`desc`,status,enable,create_by,update_by,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{name,jdbcType=VARCHAR},#{desc,jdbcType=VARCHAR}
               ,#{status,jdbcType=TINYINT},#{enable,jdbcType=TINYINT}
               ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT}
               ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RolePO" useGeneratedKeys="true">
        insert into role
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="name != null">name,</if>
                <if test="desc != null">`desc`,</if>
                <if test="status != null">status,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="desc != null">#{desc,jdbcType=VARCHAR},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="enable != null">#{enable,jdbcType=TINYINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.role.RolePO">
        update role
        <set>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="desc != null">
                    `desc` = #{desc,jdbcType=VARCHAR},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=TINYINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.role.RolePO">
        update role
        set 
            name =  #{name,jdbcType=VARCHAR},
            `desc` =  #{desc,jdbcType=VARCHAR},
            status =  #{status,jdbcType=TINYINT},
            enable =  #{enable,jdbcType=TINYINT},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateStatus">
        update role
        set status = #{status,jdbcType=TINYINT}, update_by = #{updateBy,jdbcType=BIGINT}
        where  id = #{id,jdbcType=BIGINT}
    </update>
    <update id="logicalDelete">
        update role
        set enable = false, update_by = #{updateBy,jdbcType=BIGINT}
        where  id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
