<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.AssistantVersionPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.assistant.AssistantVersionPO">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" javaType="java.lang.Integer"/>
        <result property="type" column="type" javaType="java.lang.Integer"/>
        <result property="bookId" column="book_id" jdbcType="CHAR"/>
        <result property="chapterId" column="chapter_id" jdbcType="CHAR"/>
        <result property="blockIds" column="block_ids" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="templateJson" column="template_json" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="cmsId" column="cms_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,config_type,type,book_id,chapter_id,block_ids,template_id,template_json,cms_id,version,
        create_time,update_time,create_by,update_by,enable
    </sql>

    <select id="findByIdAndVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from assistant_version
        where id = #{id,jdbcType=CHAR} and version = #{version,jdbcType=VARCHAR}
    </select>

    <select id="findByBookIdAndChapterIdAndVersion" resultMap="BaseResultMap"
            resultType="com.unipus.digitalbook.model.po.assistant.AssistantVersionPO">
        select * from assistant_version
        where book_id = #{bookId, jdbcType=CHAR}
        and chapter_id = #{chapterId, jdbcType=CHAR}
        and version = #{version,jdbcType=VARCHAR}
    </select>

    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO assistant_version
        (id,name,config_type,type,book_id,chapter_id,block_ids,template_id,template_json,cms_id,version,
        create_time,update_time,create_by,update_by,enable)
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
        #{item.id,jdbcType=CHAR}, #{item.name,jdbcType=VARCHAR}, #{item.configType,jdbcType=INTEGER}, #{item.type,jdbcType=INTEGER},
        #{item.bookId,jdbcType=CHAR}, #{item.chapterId,jdbcType=CHAR}, #{item.blockIds,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR},
        #{item.templateJson,jdbcType=VARCHAR}, #{item.cmsId,jdbcType=VARCHAR}, #{item.version,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT}, #{item.enable,jdbcType=BIT}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        config_type = VALUES(config_type),
        type = VALUES(type),
        template_id = VALUES(template_id),
        template_json = VALUES(template_json),
        update_time = VALUES(update_time),
        update_by = VALUES(update_by)
    </insert>

</mapper>