<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookUserActivityPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookUserActivityPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="activityType" column="activity_type" jdbcType="INTEGER"/>
            <result property="activityTime" column="activity_time" jdbcType="TIMESTAMP"/>
            <result property="bookId" column="book_id" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="INTEGER"/>
            <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,activity_type,
        activity_time,book_id,create_time,
        update_time,create_by,update_by,
        enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_user_activity
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByUserIdAndBookIds" resultType="com.unipus.digitalbook.model.po.book.BookUserActivityInfoPO">
        SELECT
        book_id as bookId,
        user_id as userId,
        MAX(CASE WHEN activity_type = 1 THEN activity_time ELSE NULL END) as joinEditTime,
        MAX(CASE WHEN activity_type = 2 THEN activity_time ELSE NULL END) as joinPreviewTime,
        MAX(CASE WHEN activity_type = 3 THEN activity_time ELSE NULL END) as editTime
        FROM book_user_activity
        WHERE
        user_id = #{userId}
        AND enable = true
        AND book_id IN
        <foreach item="item" index="index" collection="bookIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY book_id
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from book_user_activity
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookUserActivityPO" useGeneratedKeys="true">
        insert into book_user_activity
        ( id,user_id,activity_type
        ,activity_time,book_id,create_time
        ,update_time,create_by,update_by
        ,enable)
        values (#{id,jdbcType=BIGINT},#{userId,jdbcType=BIGINT},#{activityType,jdbcType=INTEGER}
        ,#{activityTime,jdbcType=TIMESTAMP},#{bookId,jdbcType=CHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=INTEGER},#{updateBy,jdbcType=INTEGER}
        ,#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookUserActivityPO" useGeneratedKeys="true">
        insert into book_user_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="userId != null">user_id,</if>
                <if test="activityType != null">activity_type,</if>
                <if test="activityTime != null">activity_time,</if>
                <if test="bookId != null">book_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
                <if test="activityType != null">#{activityType,jdbcType=INTEGER},</if>
                <if test="activityTime != null">#{activityTime,jdbcType=TIMESTAMP},</if>
                <if test="bookId != null">#{bookId,jdbcType=CHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=INTEGER},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=INTEGER},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into book_user_activity
        ( user_id, activity_type, activity_time, book_id, create_by, update_by, enable)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId,jdbcType=BIGINT}, #{item.activityType,jdbcType=INTEGER},
            #{item.activityTime,jdbcType=TIMESTAMP}, #{item.bookId,jdbcType=CHAR},
            #{item.createBy,jdbcType=INTEGER}, #{item.updateBy,jdbcType=INTEGER},
            #{item.enable,jdbcType=BIT})
        </foreach>
        on duplicate key update
        activity_time = values(activity_time),
        update_by = values(update_by),
        enable = values(enable)
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookUserActivityPO">
        update book_user_activity
        <set>
                <if test="userId != null">
                    user_id = #{userId,jdbcType=BIGINT},
                </if>
                <if test="activityType != null">
                    activity_type = #{activityType,jdbcType=INTEGER},
                </if>
                <if test="activityTime != null">
                    activity_time = #{activityTime,jdbcType=TIMESTAMP},
                </if>
                <if test="bookId != null">
                    book_id = #{bookId,jdbcType=CHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=INTEGER},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=INTEGER},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookUserActivityPO">
        update book_user_activity
        set 
            user_id =  #{userId,jdbcType=BIGINT},
            activity_type =  #{activityType,jdbcType=INTEGER},
            activity_time =  #{activityTime,jdbcType=TIMESTAMP},
            book_id =  #{bookId,jdbcType=CHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=INTEGER},
            update_by =  #{updateBy,jdbcType=INTEGER},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
