<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.WordPracticeInstancePOMapper">
    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.WordPracticeInstancePO">
        <id property="id" column="id"/>
        <result property="practiceId" column="practice_id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="enable" column="enable"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,practice_id,instance_id,`name`,`status`,create_time,
        update_time,create_by,update_by,`enable`
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice_instance
        where id = #{id}
    </select>

    <select id="selectEditStatusInstance" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice_instance
        where practice_id = #{practiceId} and status = 2
        order by create_time desc
        limit 1
    </select>

    <select id="selectPublishStatusInstance" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice_instance
        where practice_id = #{practiceId} and status = 1
        order by create_time desc
        limit 1
    </select>

    <select id="selectListByPracticeIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice_instance
        where practice_id in
        <foreach collection="practiceIds" item="practiceId" open="(" separator="," close=")">
            #{practiceId}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from word_practice_instance
        where id = #{id}
    </delete>

    <delete id="deleteByPracticeId">
        delete
        from word_practice_instance
        where practice_id = #{practiceId}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.WordPracticeInstancePO" useGeneratedKeys="true">
        insert into word_practice_instance
        (id, practice_id, instance_id, name, status, create_time,
         update_time, create_by, update_by, enable)
        values (#{id}, #{practiceId}, #{instanceId}, #{name}, #{status}, #{createTime},
                #{updateTime}, #{createBy}, #{updateBy}, #{enable})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.WordPracticeInstancePO" useGeneratedKeys="true">
        insert into word_practice_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="practiceId != null">practice_id,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="practiceId != null">#{practiceId},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="enable != null">#{enable},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.WordPracticeInstancePO">
        update word_practice_instance
        <set>
            <if test="practiceId != null">
                practice_id = #{practiceId},
            </if>
            <if test="instanceId != null">
                instance_id = #{instanceId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.WordPracticeInstancePO">
        update word_practice_instance
        set practice_id = #{practiceId},
            instance_id = #{instanceId},
            name        = #{name},
            status      = #{status},
            create_time = #{createTime},
            update_time = #{updateTime},
            create_by   = #{createBy},
            update_by   = #{updateBy},
            enable      = #{enable}
        where id = #{id}
    </update>
</mapper>
