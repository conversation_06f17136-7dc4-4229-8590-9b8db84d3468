<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.OrganizationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.OrganizationPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_type" jdbcType="INTEGER" property="orgType" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>

  <resultMap id="userOrgResultMap" type="com.unipus.digitalbook.model.po.User2OrgPO">
    <id property="userId" column="user_id"/>
    <collection property="organizationList" ofType="com.unipus.digitalbook.model.po.OrganizationPO">
      <id property="id" column="org_id"/>
      <result property="orgName" column="org_name"/>
      <result property="orgType" column="org_type"/>
      <result property="parentId" column="parent_id"/>
      <result property="status" column="org_status"/>
      <result property="createTime" column="org_create_time"/>
      <result property="updateTime" column="org_update_time"/>
      <result property="createBy" column="org_create_by"/>
      <result property="updateBy" column="org_update_by"/>
      <result property="enable" column="org_enable"/>
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, org_name, org_type, parent_id, `status`, create_time, update_time, create_by, 
    update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from organization
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from organization
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.OrganizationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into organization (org_name, org_type, parent_id, 
      `status`, create_time, update_time, 
      create_by, update_by, `enable`
      )
    values (#{orgName,jdbcType=VARCHAR}, #{orgType,jdbcType=INTEGER}, #{parentId,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.OrganizationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgName != null">
        org_name,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.OrganizationPO">
    <!--
    @mbg.generated
    -->
    update organization
    <set>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.OrganizationPO">
    <!--
    @mbg.generated
    -->
    update organization
    set org_name = #{orgName,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAllOrg" resultType="java.lang.Long">
    SELECT id
    FROM organization
    WHERE enable = true
  </select>

  <update id="batchChangeOrganizationStatus">
    UPDATE organization
    SET status = #{status},
    update_time = CURRENT_TIMESTAMP,
    update_by = #{opUserId}
    WHERE id IN
    <foreach collection="idList" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="selectAllByCreateTimeDesc" resultMap="BaseResultMap">
    SELECT id, org_name, org_type, parent_id, status, create_time
    FROM organization
    WHERE enable = true
    ORDER BY parent_id, create_time DESC, org_name
  </select>

  <select id="searchByOrgName" resultType="long">
    SELECT id FROM organization
    WHERE org_name = #{orgName}
    LIMIT 1
  </select>

  <select id="searchByOrgNameWithPath" resultType="com.unipus.digitalbook.model.entity.Organization">
    WITH RECURSIVE ancestor_path AS (
      SELECT
        id, org_name, parent_id, CAST(null AS CHAR(100))  AS path, 1 AS org_level, id AS original_id
      FROM organization
      WHERE
        enable = true
    <if test="searchTerm != null and searchTerm != ''">
      AND (
      <choose>
        <when test="useLikeSearch">
          org_name LIKE CONCAT('%', #{searchTerm}, '%')
        </when>
        <otherwise>
          MATCH(org_name) AGAINST (#{searchTerm} IN BOOLEAN MODE)
        </otherwise>
      </choose>
      )
    </if>
        <if test="status != null">
          AND status = #{status}
        </if>
      UNION ALL
      SELECT
        o.id, o.org_name, o.parent_id,
        CONCAT_WS('/', o.org_name, ap.path) AS path , (ap.org_level + 1) as org_level, ap.original_id
      FROM organization o
      JOIN ancestor_path ap ON o.id = ap.parent_id
    )
    SELECT
      ap.original_id  AS id,
      org.org_name      AS orgName,
      org.org_type      AS orgType,
      org.parent_id     AS parentId,
      org.status        AS status,
      ap.path           AS parentPath,
      ap.org_level      AS level,
      org.create_time   AS createTime,
      org.update_time   AS updateTime
    FROM (SELECT original_id, MAX(org_level) AS max_org_level FROM ancestor_path GROUP BY original_id ) AS sub
    JOIN ancestor_path ap ON sub.original_id = ap.original_id AND ap.org_level = sub.max_org_level
    JOIN organization org ON org.id = ap.original_id
    ORDER BY org.create_time desc, org.parent_id, org.org_name
    LIMIT #{offset}, #{limit}
  </select>

  <select id="countByOrgNameAndStatus" resultType="java.lang.Integer">
    SELECT count(id)
    FROM organization
    WHERE
      enable = true
    <if test="searchTerm != null and searchTerm != ''">
      AND (
      <choose>
        <when test="useLikeSearch">
          org_name LIKE CONCAT('%', #{searchTerm}, '%')
        </when>
        <otherwise>
          MATCH(org_name) AGAINST (#{searchTerm} IN BOOLEAN MODE)
        </otherwise>
      </choose>
      )
    </if>
    <if test="status != null">
      AND status = #{status}
    </if>
  </select>

  <select id="getUserOrganizations" resultMap="userOrgResultMap">
    SELECT
    r.user_id,
    o.id as org_id,
    o.org_name,
    o.org_type,
    o.parent_id,
    o.status as org_status,
    o.create_time as org_create_time,
    o.update_time as org_update_time,
    o.create_by as org_create_by,
    o.update_by as org_update_by,
    o.enable as org_enable
    FROM org_user_relation r
    INNER JOIN organization o ON r.org_id = o.id
    WHERE r.user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    <if test="orgId != null">
      AND r.org_id = #{orgId}
    </if>
    AND r.status = 1
    AND r.enable = 1
    AND o.enable = 1
    AND o.status = 1
    ORDER BY r.user_id, r.create_time DESC, o.id
  </select>

  <select id="selectByIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from organization
    where id in
    <foreach item="id" collection="idList" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>


  <select id="checkSubOrgEnableStatus" resultType="java.lang.Integer">
    SELECT
        COUNT(1)
    FROM
        organization
    WHERE
        parent_id IN
        <foreach collection="orgIds" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
        AND status = 1
        AND enable = 1
  </select>


  <!-- 查询指定机构ID列表的上级机构中是否存在禁用状态的机构 -->
  <select id="checkParentOrgDisableStatus" resultType="java.lang.Integer">
    SELECT
        COUNT(1)
    FROM
        organization parent
    INNER JOIN
        organization child ON parent.id = child.parent_id
    WHERE
        child.id IN
        <foreach collection="orgIds" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
        AND child.enable = 1
        AND parent.enable = 1
        AND parent.status = 0
  </select>

</mapper>