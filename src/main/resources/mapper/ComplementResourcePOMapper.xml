<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ComplementResourcePOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.complement.ComplementResourcePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="resourceId" column="resource_id" jdbcType="CHAR"/>
            <result property="bookId" column="book_id" jdbcType="CHAR"/>
            <result property="resourceType" column="resource_type" jdbcType="INTEGER"/>
            <result property="suffix" column="suffix" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="resourceUrl" column="resource_url" jdbcType="VARCHAR"/>
            <result property="size" column="size" jdbcType="BIGINT"/>
            <result property="duration" column="duration" jdbcType="BIGINT"/>
            <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
            <result property="visibleStatus" column="visible_status" jdbcType="INTEGER"/>
            <result property="versionNumber" column="version_number" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,book_id,resource_type,
        suffix,name,resource_url,
        size,duration,upload_time,visible_status,version_number,
        create_time,cover_url,update_time,
        create_by,update_by,enable
    </sql>
    <sql id="Search_Conditions">
        where enable = true
        and book_id = #{bookId,jdbcType=CHAR} and version_number = '0'
        <if test="resourceType != null">
            and resource_type = #{resourceType,jdbcType=INTEGER}
        </if>
        <if test="visibleStatus != null and visibleStatus >= 0">
            and visible_status = #{visibleStatus,jdbcType=INTEGER}
        </if>
</sql>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from complement_resource
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <select id="search" resultType="com.unipus.digitalbook.model.po.complement.ComplementResourcePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from complement_resource
        <include refid="Search_Conditions" />
        order by upload_time desc
        limit #{page.offset}, #{page.limit}
    </select>

    <select id="count" resultType="int">
        select count(*)
        from complement_resource
        <include refid="Search_Conditions" />
    </select>
    <select id="selectResourceByBookIdAndVersion"
            resultType="com.unipus.digitalbook.model.po.complement.ComplementResourcePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from complement_resource
        where book_id = #{bookId} and version_number = #{version} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from complement_resource
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.complement.ComplementResourcePO" useGeneratedKeys="true">
        insert into complement_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="resourceId != null">resource_id,</if>
                <if test="bookId != null">book_id,</if>
                <if test="resourceType != null">resource_type,</if>
                <if test="suffix != null">suffix,</if>
                <if test="name != null">name,</if>
                <if test="resourceUrl != null">resource_url,</if>
                <if test="size != null">size,</if>
                <if test="duration != null">duration,</if>
                <if test="uploadTime != null">upload_time,</if>
                <if test="visibleStatus != null">visible_status,</if>
                <if test="versionNumber != null">version_number,</if>
                <if test="createTime != null">create_time,</if>
                <if test="coverUrl != null">cover_url,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="resourceId != null">#{resourceId,jdbcType=CHAR},</if>
                <if test="bookId != null">#{bookId,jdbcType=CHAR},</if>
                <if test="resourceType != null">#{resourceType,jdbcType=INTEGER},</if>
                <if test="suffix != null">#{suffix,jdbcType=VARCHAR},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="resourceUrl != null">#{resourceUrl,jdbcType=VARCHAR},</if>
                <if test="size != null">#{size,jdbcType=BIGINT},</if>
                <if test="duration != null">#{duration,jdbcType=BIGINT},</if>
                <if test="uploadTime != null">#{uploadTime,jdbcType=TIMESTAMP},</if>
                <if test="visibleStatus != null">#{visibleStatus,jdbcType=INTEGER},</if>
                <if test="versionNumber != null">#{versionNumber,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="coverUrl != null">#{coverUrl,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="generateVersionResource">
        INSERT INTO complement_resource (
            resource_id, book_id, resource_type, suffix, name, resource_url,
            size, duration, upload_time, visible_status, version_number,
            create_time, cover_url, update_time,
            create_by, update_by, enable
        )
        SELECT
            resource_id, book_id, resource_type, suffix, name, resource_url,
            size,duration, upload_time, visible_status, #{version},
            create_time, cover_url, update_time,
            create_by, update_by, enable
        FROM complement_resource
        WHERE version_number = '0' and book_id = #{bookId} and enable = true;

    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.complement.ComplementResourcePO">
        update complement_resource
        <set>
                <if test="resourceId != null">
                    resource_id = #{resourceId,jdbcType=CHAR},
                </if>
                <if test="bookId != null">
                    book_id = #{bookId,jdbcType=CHAR},
                </if>
                <if test="resourceType != null">
                    resource_type = #{resourceType,jdbcType=INTEGER},
                </if>
                <if test="suffix != null">
                    suffix = #{suffix,jdbcType=VARCHAR},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="resourceUrl != null">
                    resource_url = #{resourceUrl,jdbcType=VARCHAR},
                </if>
                <if test="size != null">
                    size = #{size,jdbcType=BIGINT},
                </if>
                <if test="duration != null">
                    duration = #{duration,jdbcType=BIGINT},
                </if>
                <if test="uploadTime != null">
                    upload_time = #{uploadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="visibleStatus != null">
                    visible_status = #{visibleStatus,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="coverUrl != null">
                    cover_url = #{coverUrl,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="logicalDelete">
        update complement_resource
        set enable = false, update_by = #{opUserId, jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateVisibleStatus">
        update complement_resource
        set visible_status = #{visibleStatus,jdbcType=INTEGER}, update_by = #{opUserId, jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByResourceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from complement_resource
        where resource_id = #{resourceId} and version_number = '0' and enable = true
    </select>
    <select id="selectByBookVersionId"
            resultMap="BaseResultMap">
        select
            r.id as id,
            r.book_id as book_id,
            r.resource_type as resource_type,
            r.suffix as suffix,
            r.name as name,
            r.resource_url as resource_url,
            r.size as size,
            r.duration as duration,
            r.upload_time as upload_time,
            r.visible_status as visible_status,
            r.version_number as version_number,
            r.create_time as create_time,
            r.cover_url as cover_url,
            r.update_time as update_time,
            r.resource_id as resource_id,
            r.create_by as create_by,
            r.update_by as update_by

        from complement_resource r
        left join book_version_info_version_relation bv on r.id = bv.info_id
        where  bv.book_version_id = #{bookVersionId,jdbcType=BIGINT}
        and bv.content_type = 4
        and r.enable = true
        and bv.enable = true
    </select>
</mapper>
