<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperScoreTemplateDetailPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="paper_score_template_id" jdbcType="BIGINT" property="paperScoreTemplateId" />
    <result column="min_score" jdbcType="INTEGER" property="minScore" />
    <result column="max_score" jdbcType="INTEGER" property="maxScore" />
    <result column="evaluate_phrases" jdbcType="INTEGER" property="evaluatePhrases" />
    <result column="evaluate_text" jdbcType="VARCHAR" property="evaluateText" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, paper_score_template_id, min_score, max_score, evaluate_phrases, evaluate_text, 
    create_time, update_time, create_by, update_by, enable
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from paper_score_template_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from paper_score_template_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO">
    insert into paper_score_template_detail (id, paper_score_template_id, min_score, 
      max_score, evaluate_phrases, evaluate_text, 
      create_time, update_time, create_by, 
      update_by, enable)
    values (#{id,jdbcType=BIGINT}, #{paperScoreTemplateId,jdbcType=BIGINT}, #{minScore,jdbcType=INTEGER}, 
      #{maxScore,jdbcType=INTEGER}, #{evaluatePhrases,jdbcType=INTEGER}, #{evaluateText,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO">
    insert into paper_score_template_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paperScoreTemplateId != null">
        paper_score_template_id,
      </if>
      <if test="minScore != null">
        min_score,
      </if>
      <if test="maxScore != null">
        max_score,
      </if>
      <if test="evaluatePhrases != null">
        evaluate_phrases,
      </if>
      <if test="evaluateText != null">
        evaluate_text,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        enable,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paperScoreTemplateId != null">
        #{paperScoreTemplateId,jdbcType=BIGINT},
      </if>
      <if test="minScore != null">
        #{minScore,jdbcType=INTEGER},
      </if>
      <if test="maxScore != null">
        #{maxScore,jdbcType=INTEGER},
      </if>
      <if test="evaluatePhrases != null">
        #{evaluatePhrases,jdbcType=INTEGER},
      </if>
      <if test="evaluateText != null">
        #{evaluateText,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO">
    update paper_score_template_detail
    <set>
      <if test="paperScoreTemplateId != null">
        paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT},
      </if>
      <if test="minScore != null">
        min_score = #{minScore,jdbcType=INTEGER},
      </if>
      <if test="maxScore != null">
        max_score = #{maxScore,jdbcType=INTEGER},
      </if>
      <if test="evaluatePhrases != null">
        evaluate_phrases = #{evaluatePhrases,jdbcType=INTEGER},
      </if>
      <if test="evaluateText != null">
        evaluate_text = #{evaluateText,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO">
    update paper_score_template_detail
    set paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT},
      min_score = #{minScore,jdbcType=INTEGER},
      max_score = #{maxScore,jdbcType=INTEGER},
      evaluate_phrases = #{evaluatePhrases,jdbcType=INTEGER},
      evaluate_text = #{evaluateText,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      enable = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 批量插入 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO paper_score_template_detail (paper_score_template_id, min_score,
    max_score, evaluate_phrases, evaluate_text,
    create_by, update_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.paperScoreTemplateId,jdbcType=BIGINT}, #{item.minScore,jdbcType=INTEGER},
      #{item.maxScore,jdbcType=INTEGER}, #{item.evaluatePhrases,jdbcType=INTEGER}, #{item.evaluateText,jdbcType=VARCHAR},
      #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="selectByPaperScoreTemplateId" parameterType="long" resultMap="BaseResultMap">
    select * from paper_score_template_detail where enable = 1 and paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT} order by max_score asc
  </select>

  <update id="deleteByIdList">
    update paper_score_template_detail set enable = 0, update_by = #{updateBy,jdbcType=BIGINT} where id in
    <foreach collection="idList" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <update id="deleteByPaperScoreTemplateId">
    update paper_score_template_detail set enable = 0, update_by = #{updateBy,jdbcType=BIGINT}
    where enable = 1 and paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT}
  </update>
</mapper>