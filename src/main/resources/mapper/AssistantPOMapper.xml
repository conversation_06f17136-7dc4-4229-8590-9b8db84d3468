<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.AssistantPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.assistant.AssistantPO">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" javaType="java.lang.Integer"/>
        <result property="type" column="type" javaType="java.lang.Integer"/>
        <result property="bookId" column="book_id" jdbcType="CHAR"/>
        <result property="chapterId" column="chapter_id" jdbcType="CHAR"/>
        <result property="blockIds" column="block_ids" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="templateJson" column="template_json" jdbcType="VARCHAR"/>
        <result property="cmsId" column="cms_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,config_type,type,book_id,chapter_id,block_ids,template_id,template_json,cms_id,
        create_time,update_time,create_by,update_by,enable
    </sql>

    <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from assistant
        where id = #{id,jdbcType=CHAR}
    </select>

    <update id="disableById" parameterType="java.lang.String">
        update assistant set enable = false where id = #{id,jdbcType=CHAR}
    </update>

    <select id="findByBookId" resultMap="BaseResultMap" resultType="com.unipus.digitalbook.model.po.assistant.AssistantPO">
        select
        <include refid="Base_Column_List" />
        from assistant
        where book_id = #{bookId, jdbcType=CHAR} and enable = true
    </select>

    <select id="findByBookIdAndChapterId" resultMap="BaseResultMap" resultType="com.unipus.digitalbook.model.po.assistant.AssistantPO">
        select
        <include refid="Base_Column_List" />
        from assistant
        where book_id = #{bookId, jdbcType=CHAR} and chapter_id = #{chapterId, jdbcType=CHAR} and enable = true
    </select>

    <insert id="upsertAssistant" parameterType="com.unipus.digitalbook.model.po.assistant.AssistantPO">
        INSERT INTO assistant
        (id,name,config_type,type,book_id,chapter_id,block_ids,template_id,template_json,cms_id,create_time,update_time,create_by,update_by,enable)
        VALUES (#{id,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{configType,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
        #{bookId,jdbcType=CHAR}, #{chapterId,jdbcType=CHAR}, #{blockIds,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR},
        #{templateJson,jdbcType=VARCHAR}, #{cmsId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
        ON DUPLICATE KEY UPDATE
        name = #{name,jdbcType=VARCHAR},
        config_type = #{configType,jdbcType=INTEGER},
        type = #{type,jdbcType=INTEGER},
        template_id = #{templateId,jdbcType=VARCHAR},
        template_json = #{templateJson,jdbcType=VARCHAR},
        create_by = #{createBy,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=BIGINT},
        enable = #{enable,jdbcType=BIT},
        cms_id = #{cmsId,jdbcType=VARCHAR}
    </insert>

    <update id="updateAssistant" parameterType="com.unipus.digitalbook.model.po.assistant.AssistantPO">
        UPDATE assistant
        SET name = #{name,jdbcType=VARCHAR},
        config_type = #{configType,jdbcType=INTEGER},
        type = #{type,jdbcType=INTEGER},
        template_id = #{templateId,jdbcType=VARCHAR},
        template_json = #{templateJson,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=BIGINT},
        enable = #{enable,jdbcType=BIT},
        cms_id = #{cmsId,jdbcType=VARCHAR}
        WHERE id = #{id, jdbcType=CHAR}
    </update>

    <select id="findByConfigTypeAndTemplateId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from assistant
        WHERE config_type = #{configType, jdbcType=INTEGER} and template_id = #{templateId, jdbcType=VARCHAR} and enable = true
        ORDER BY update_time DESC
        <if test="page != null">
            LIMIT #{page.offset}, #{page.limit}
        </if>
    </select>

    <select id="countByConfigTypeAndTemplateId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        from assistant
        WHERE config_type = #{configType, jdbcType=INTEGER} and template_id = #{templateId, jdbcType=VARCHAR} and enable = true
    </select>

</mapper>