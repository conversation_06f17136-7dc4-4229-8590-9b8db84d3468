<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ReadingTimeDurationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="open_id" jdbcType="CHAR" property="openId" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="chapter_id" jdbcType="CHAR" property="chapterId" />
    <result column="duration" jdbcType="BIGINT" property="duration" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, open_id, book_id, chapter_id, duration, tenant_id, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_duration
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from reading_time_duration
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into reading_time_duration (open_id, book_id, chapter_id,
    duration, tenant_id, create_time, update_time)
    values (#{openId,jdbcType=CHAR}, #{bookId,jdbcType=CHAR}, #{chapterId,jdbcType=CHAR},
    #{duration,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
    #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into reading_time_duration
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="openId != null">
        open_id,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="chapterId != null">
        chapter_id,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="openId != null">
        #{openId,jdbcType=CHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null">
        #{chapterId,jdbcType=CHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    update reading_time_duration
    <set>
      <if test="openId != null">
        open_id = #{openId,jdbcType=CHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null">
        chapter_id = #{chapterId,jdbcType=CHAR},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    update reading_time_duration
    set open_id = #{openId,jdbcType=CHAR},
        book_id = #{bookId,jdbcType=CHAR},
        chapter_id = #{chapterId,jdbcType=CHAR},
        duration = #{duration,jdbcType=BIGINT},
        tenant_id = #{tenantId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 根据用户ID、书籍ID和章节ID查询或创建阅读时长记录 -->
  <select id="selectByopenIdAndBookIdAndChapterId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_duration
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    and chapter_id = #{chapterId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
  </select>
  
  <!-- 根据用户ID和书籍ID查询所有章节的阅读时长 -->
  <select id="selectByopenIdAndBookId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_duration
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
    order by update_time desc
  </select>
  
  <!-- 增加阅读时长 -->
  <update id="addDuration">
    update reading_time_duration
    set duration = duration + #{additionalDuration,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    and chapter_id = #{chapterId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
  </update>
  
  <!-- 统计用户总阅读时长 -->
  <select id="sumDurationByopenId" resultType="java.lang.Long">
    select COALESCE(SUM(duration), 0)
    from reading_time_duration
    where open_id = #{openId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
  </select>
  
  <!-- 统计用户在某本书的总阅读时长 -->
  <select id="sumDurationByopenIdAndBookId" resultType="java.lang.Long">
    select COALESCE(SUM(duration), 0)
    from reading_time_duration
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
  </select>

  <!-- 批量插入或更新（MySQL的ON DUPLICATE KEY UPDATE语法） -->
  <insert id="insertOrUpdate" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeDurationPO">
    INSERT INTO reading_time_duration
    (id, open_id, book_id, chapter_id, duration, tenant_id, create_time, update_time)
    VALUES (#{id,jdbcType=BIGINT}, #{openId,jdbcType=CHAR}, #{bookId,jdbcType=CHAR},
            #{chapterId,jdbcType=CHAR}, #{duration,jdbcType=BIGINT},
            #{tenantId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
    ON DUPLICATE KEY UPDATE duration    = duration + VALUES(duration),
                            update_time = VALUES(update_time)
  </insert>

  <select id="selectByUserAndBookAndChapter" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM reading_time_duration
    WHERE open_id = #{openId,jdbcType=CHAR}
    AND book_id = #{bookId,jdbcType=CHAR}
    AND chapter_id = #{chapterId,jdbcType=CHAR}
  </select>


</mapper>