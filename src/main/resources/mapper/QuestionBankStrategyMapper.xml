<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.unipus.digitalbook.dao.QuestionBankStrategyMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.paper.QuestionBankStrategyPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="bank_id" property="bankId" jdbcType="CHAR"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="questions_per_round" property="questionsPerRound" jdbcType="INTEGER"/>
        <result column="question_score" property="questionScore" jdbcType="INTEGER"/>
        <result column="version_number" property="versionNumber" jdbcType="CHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, bank_id, bank_name, questions_per_round, question_score, version_number,
        create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 插入或更新单条记录 -->
    <insert id="insertOrUpdate" parameterType="com.unipus.digitalbook.model.po.paper.QuestionBankStrategyPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO question_bank_strategy (
        bank_id, bank_name, questions_per_round, question_score, version_number, create_by, update_by
        ) VALUES (
        #{bankId}, #{bankName}, #{questionsPerRound}, #{questionScore}, #{versionNumber}, #{createBy}, #{updateBy}
        )
        ON DUPLICATE KEY UPDATE
        bank_name = VALUES(bank_name),
        questions_per_round = VALUES(questions_per_round),
        question_score = VALUES(question_score),
        version_number = VALUES(version_number),
        update_by = VALUES(update_by),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO question_bank_strategy (
        bank_id, bank_name, questions_per_round, question_score, version_number, create_by, update_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.bankId}, #{item.bankName}, #{item.questionsPerRound}, #{item.questionScore},
            #{item.versionNumber}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        bank_name = VALUES(bank_name),
        questions_per_round = VALUES(questions_per_round),
        question_score = VALUES(question_score),
        version_number = VALUES(version_number),
        update_by = VALUES(update_by),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 批量查询 -->
    <select id="batchSelect" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM question_bank_strategy
        WHERE enable = true
        <if test="bankIds != null and bankIds.size() > 0">
            AND (
            <foreach item="bankId" index="index" collection="bankIds" separator=" OR ">
                bank_id = #{bankId}
            </foreach>
            )
        </if>
        <if test="versionNumber != null">
            AND version_number = #{versionNumber}
        </if>
    </select>

    <!-- 根据bankId和版本号查询策略 -->
    <select id="selectByBankIdAndVersion" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM question_bank_strategy
        WHERE bank_id = #{bankId}
        AND version_number = #{versionNumber}
        AND enable = true
        LIMIT 1
    </select>

</mapper>