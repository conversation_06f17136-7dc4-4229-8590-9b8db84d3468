<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookVersionChapterVersionRelationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_version_id" jdbcType="BIGINT" property="bookVersionId" />
    <result column="chapter_version_id" jdbcType="BIGINT" property="chapterVersionId" />
    <result column="chapter_number" jdbcType="INTEGER" property="chapterNumber" />
    <result column="chapter_name" jdbcType="VARCHAR" property="chapterName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, book_version_id, chapter_version_id, chapter_number, chapter_name,
    create_time, update_time, create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from book_version_chapter_version_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from book_version_chapter_version_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_version_chapter_version_relation (book_version_id, chapter_version_id, chapter_number, 
      chapter_name, create_time,
      update_time, create_by, update_by, 
      `enable`)
    values (#{bookVersionId,jdbcType=BIGINT}, #{chapterVersionId,jdbcType=BIGINT}, #{chapterNumber,jdbcType=INTEGER}, 
      #{chapterName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_version_chapter_version_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookVersionId != null">
        book_version_id,
      </if>
      <if test="chapterVersionId != null">
        chapter_version_id,
      </if>
      <if test="chapterNumber != null">
        chapter_number,
      </if>
      <if test="chapterName != null">
        chapter_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookVersionId != null">
        #{bookVersionId,jdbcType=BIGINT},
      </if>
      <if test="chapterVersionId != null">
        #{chapterVersionId,jdbcType=BIGINT},
      </if>
      <if test="chapterNumber != null">
        #{chapterNumber,jdbcType=INTEGER},
      </if>
      <if test="chapterName != null">
        #{chapterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into book_version_chapter_version_relation (
      book_version_id,
      chapter_version_id,
      chapter_name,
      chapter_number,
      create_by
    ) values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.bookVersionId},
        #{item.chapterVersionId},
        #{item.chapterName},
        #{item.chapterNumber},
        #{item.createBy}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO">
    <!--
    @mbg.generated
    -->
    update book_version_chapter_version_relation
    <set>
      <if test="bookVersionId != null">
        book_version_id = #{bookVersionId,jdbcType=BIGINT},
      </if>
      <if test="chapterVersionId != null">
        chapter_version_id = #{chapterVersionId,jdbcType=BIGINT},
      </if>
      <if test="chapterNumber != null">
        chapter_number = #{chapterNumber,jdbcType=INTEGER},
      </if>
      <if test="chapterName != null">
        chapter_name = #{chapterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO">
    <!--
    @mbg.generated
    -->
    update book_version_chapter_version_relation
    set book_version_id = #{bookVersionId,jdbcType=BIGINT},
      chapter_version_id = #{chapterVersionId,jdbcType=BIGINT},
      chapter_number = #{chapterNumber,jdbcType=INTEGER},
      chapter_name = #{chapterName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBookVersionId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
        from book_version_chapter_version_relation
    where enable = true
    and book_version_id = #{bookVersionId,jdbcType=BIGINT}
  </select>
   <select id="selectChapterVersionByBookVersionId" resultType="BookVersionChapterVersionRelationPO">
    select
        bvcvr.id as id,
        bvcvr.book_version_id as bookVersionId,
        bvcvr.chapter_version_id as chapterVersionId,
        bvcvr.chapter_number as chapterNumber,
        bvcvr.chapter_name as chapterName,
        bvcvr.create_time as createTime,
        bvcvr.update_time as updateTime,
        bvcvr.create_by as createBy,
        bvcvr.update_by as updateBy,
        bvcvr.enable as enable,
        chapter.name as chapterName
    from book_version_chapter_version_relation bvcvr
    join chapter_version cv on cv.id = bvcvr.chapter_version_id
    join chapter on chapter.id = cv.chapter_id and chapter.enable = true
    where bvcvr.enable = true
    and bvcvr.book_version_id = #{bookVersionId,jdbcType=BIGINT}
  </select>

  <select id="selectFirstByChapterVersionId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_version_chapter_version_relation
    where enable = true
    and chapter_version_id = #{chapterVersionId,jdbcType=BIGINT}
    order by create_time desc limit 1
  </select>
</mapper>