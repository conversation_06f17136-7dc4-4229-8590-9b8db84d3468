<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookKnowledgeResourceInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookId" column="book_id" jdbcType="VARCHAR"/>
        <result property="chapterId" column="chapter_id" jdbcType="VARCHAR"/>
        <result property="knowledgeId" column="knowledge_id" jdbcType="VARCHAR"/>
        <result property="sourceUrl" column="source_url" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="knowledgeResourceId" column="knowledge_resource_id" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_id,chapter_id,
        knowledge_id,source_url,type,
        knowledge_resource_id,enable,create_time,
        update_time,create_by,update_by
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByPrimaryKeyList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_info
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByBookIdAndSourceUrl" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_info
        where enable=1 and  book_id=#{bookId,jdbcType=VARCHAR} and source_url=#{sourceUrl,jdbcType=VARCHAR}
    </select>

    <select id="selectListByThirdResourceIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_info
        where `enable`=1 and knowledge_id=#{knowledgeId} and knowledge_resource_id in
        <foreach collection="thirdIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from book_knowledge_resource_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_info
        ( id, book_id, chapter_id
        , knowledge_id, source_url, type
        , knowledge_resource_id, enable, create_time
        , update_time, create_by, update_by)
        values ( #{id,jdbcType=BIGINT}, #{bookId,jdbcType=VARCHAR}, #{chapterId,jdbcType=VARCHAR}
               , #{knowledgeId,jdbcType=VARCHAR}, #{sourceUrl,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}
               , #{knowledgeResourceId,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="knowledgeId != null">knowledge_id,</if>
            <if test="sourceUrl != null">source_url,</if>
            <if test="type != null">type,</if>
            <if test="knowledgeResourceId != null">knowledge_resource_id,</if>
            <if test="enable != null">enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bookId != null">#{bookId,jdbcType=VARCHAR},</if>
            <if test="chapterId != null">#{chapterId,jdbcType=VARCHAR},</if>
            <if test="knowledgeId != null">#{knowledgeId,jdbcType=VARCHAR},</if>
            <if test="sourceUrl != null">#{sourceUrl,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="knowledgeResourceId != null">#{knowledgeResourceId,jdbcType=VARCHAR},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO">
        update book_knowledge_resource_info
        <set>
            <if test="bookId != null">
                book_id = #{bookId,jdbcType=VARCHAR},
            </if>
            <if test="chapterId != null">
                chapter_id = #{chapterId,jdbcType=VARCHAR},
            </if>
            <if test="knowledgeId != null">
                knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="knowledgeResourceId != null">
                knowledge_resource_id = #{knowledgeResourceId,jdbcType=VARCHAR},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO">
        update book_knowledge_resource_info
        set book_id               = #{bookId,jdbcType=VARCHAR},
            chapter_id            = #{chapterId,jdbcType=VARCHAR},
            knowledge_id          = #{knowledgeId,jdbcType=VARCHAR},
            source_url            = #{sourceUrl,jdbcType=VARCHAR},
            type                  = #{type,jdbcType=INTEGER},
            knowledge_resource_id = #{knowledgeResourceId,jdbcType=VARCHAR},
            enable                = #{enable,jdbcType=BIT},
            create_time           = #{createTime,jdbcType=TIMESTAMP},
            update_time           = #{updateTime,jdbcType=TIMESTAMP},
            create_by             = #{createBy,jdbcType=BIGINT},
            update_by             = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectCompleteInfoList"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO"
            resultType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO">
        select
        *
        from
        (
        select
        case
        when main.source_url != detail.source_url then null
        else main.id
        end as id,
        case
        when main.source_url = detail.source_url then null
        else main.id
        end as parentId,
        detail.book_id as bookId,
        detail.chapter_id as chapterId,
        detail.type as type,
        midia.start_time as startTime,
        midia.end_time as endTime,
        knowledge.id as bookKnowledgeId,
        case
        when detail.type = 2 then midia.multimedia_key
        when detail.type = 3 then question.question_key
        else null
        end as multimediaKey,
        detail.source_url as sourceUrl,
        case
        when main.id is null then 0
        else 1
        end as mainStatus,
        midia.start_picture_url as startPictureUrl,
        detail.knowledge_id as knowledgeId,
        detail.location as location,
        detail.status as status,
        main.knowledge_resource_id as knowledgeSourceId,
        detail.create_time as createTime,
        detail.update_time as updateTime,
        detail.create_by as createBy,
        detail.update_by as updateBy,
        case
        when detail.type = 2 then midia.multimedia_index
        when detail.type = 3 then question.question_index
        else null
        end as multimediaIndex,
        case
        when detail.type = 2 then midia.multimedia_index
        when detail.type = 3 then question.question_id
        else null
        end as multimediaName
        from
        (
        select
        *
        from
        book_knowledge_resource_detail_info a
        where
        enable = 1
        and a.book_id = #{bookId}
        and a.chapter_id = #{chapterId}
        <if test="type != null">AND type = #{type}</if>
        and a.knowledge_id = #{knowledgeId}

        ) as detail
        left join book_knowledge_resource_info main on
        main.id = detail.resource_id
        left join book_knowledge_info knowledge on
        knowledge.knowledge_id = detail.knowledge_id
        left join book_knowledge_resource_media_info midia on
        midia.resource_id = detail.resource_id
        left join book_knowledge_resource_question_info question on
        question.resource_id = detail.resource_id
        ) knowledge_resource
        <where>
            <if test="multimediaKey != null">AND multimediaKey = #{multimediaKey}</if>
        </where>
    </select>
</mapper>
