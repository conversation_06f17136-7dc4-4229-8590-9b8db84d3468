<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QuestionAnswerPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.QuestionAnswerPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="questionId" column="question_id" jdbcType="CHAR"/>
            <result property="correctAnswer" column="correct_answer" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="correctAnswerId" column="correct_answer_id" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,question_id,correct_answer,sort_order,correct_answer_id,create_time,update_time,
        create_by,update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_answer
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByQuestionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from question_answer
        where question_id = #{questionId,jdbcType=BIGINT} and enable = true
    </select>
    <select id="selectByQuestionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from question_answer
        where question_id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from question_answer
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <update id="deleteByIds">
        update question_answer
        set enable = false, update_by = #{opsUserId,jdbcType=BIGINT}
        where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionAnswerPO" useGeneratedKeys="true">
        insert into question_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="questionId != null">question_id,</if>
                <if test="correctAnswer != null">correct_answer,</if>
                <if test="sortOrder != null">sort_order,</if>
                <if test="correctAnswerId != null">correct_answer_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="questionId != null">#{questionId,jdbcType=CHAR},</if>
                <if test="correctAnswer != null">#{correctAnswer,jdbcType=VARCHAR},</if>
                <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
                <if test="correctAnswerId != null">#{correctAnswerId,jdbcType=CHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate">
        insert into question_answer (question_id, correct_answer,sort_order, correct_answer_id, create_by, update_by, enable)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.questionId,jdbcType=BIGINT}, #{item.correctAnswer,jdbcType=VARCHAR},
             #{item.sortOrder,jdbcType=INTEGER},
             #{item.correctAnswerId,jdbcType=CHAR},
             #{item.createBy,jdbcType=BIGINT}
            , #{item.updateBy,jdbcType=BIGINT}, #{item.enable,jdbcType=BIT})
        </foreach>
        on duplicate key update
        id = LAST_INSERT_ID(id),
        correct_answer = COALESCE(VALUES(correct_answer), question_answer.correct_answer),
        sort_order = COALESCE(VALUES(sort_order), question_answer.sort_order),
        correct_answer_id = COALESCE(VALUES(correct_answer_id), question_answer.correct_answer_id),
        enable = COALESCE(VALUES(enable), question_answer.enable),
        update_by = COALESCE(VALUES(update_by), question_answer.update_by)
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.QuestionAnswerPO">
        update question_answer
        <set>
                <if test="questionId != null">
                    question_id = #{questionId,jdbcType=CHAR},
                </if>
                <if test="correctAnswer != null">
                    correct_answer = #{correctAnswer,jdbcType=VARCHAR},
                </if>
                <if test="sortOrder != null">
                    sort_order = #{sort_order,jdbcType=INTEGER},
                </if>
                <if test="correctAnswerId != null">
                    correct_answer_id = #{correct_answer_id,jdbcType=CHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
