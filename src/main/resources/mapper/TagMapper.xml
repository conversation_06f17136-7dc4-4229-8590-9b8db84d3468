<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.unipus.digitalbook.dao.TagPOMapper">

    <!-- 定义 resultMap -->
    <resultMap id="TagResultMap" type="TagPO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="tag_type" property="tagType"/>
        <result column="tag_name" property="tagName"/>
        <result column="full_path_id" property="fullPathId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="enable" property="enable"/>
        <result column="level" property="level"/>
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, parent_id, tag_type, full_path_id, tag_name, create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 插入操作 -->
    <insert id="insertTag" parameterType="TagPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tag (parent_id, tag_type, tag_name, create_by, update_by)
        VALUES (#{parentId}, #{tagType}, #{tagName}, #{createBy}, #{updateBy})
    </insert>
    <insert id="insertOrUpdateTag" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO tag (parent_id, tag_type, tag_name, full_path_id, create_by, update_by)
        VALUES (#{parentId}, #{tagType}, #{tagName},#{fullPathId}, #{createBy}, #{updateBy})
        ON DUPLICATE KEY UPDATE
            id = LAST_INSERT_ID(id),
            parent_id = #{parentId},
            tag_type = #{tagType},
            tag_name = #{tagName},
            full_path_id = #{fullPathId},
            update_by = #{updateBy},
            update_time = NOW()
    </insert>

    <!-- 更新操作 -->
    <update id="updateTag" parameterType="TagPO">
        UPDATE tag
        SET parent_id = #{parentId},
        tag_type = #{tagType},
        tag_name = #{tagName},
        update_by = #{updateBy},
        enable = #{enable}
        WHERE id = #{id}
    </update>

    <!-- 查询操作，使用 resultMap -->
    <select id="selectTagById" parameterType="Long" resultMap="TagResultMap">
        SELECT  <include refid="Base_Column_List"/> FROM tag WHERE id = #{id} AND enable = 1
    </select>

    <!-- 根据条件查询标签 -->
    <select id="selectTagByCondition" resultMap="TagResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tag
        WHERE enable = 1
        <if test="tagType != null">
            AND tag_type = #{tagType}
        </if>
        <if test="tagName != null">
            AND tag_name = #{tagName}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        ORDER BY id, tag_type, tag_name
    </select>

    <!-- 查询操作，使用 resultMap -->
    <select id="selectTagsByIds" resultMap="TagResultMap">
        SELECT  <include refid="Base_Column_List"/>
        FROM tag
        WHERE enable = 1
        <if test="tagIds != null">
            AND id in <foreach collection="tagIds" item="id" open="(" close=")" separator=","> #{id}  </foreach>
        </if>
        <if test="tagType != null">
            AND tag_type = #{tagType}
        </if>
    </select>

    <!-- 根据标签id递归查询全部标签（包含所有层级的子标签） -->
    <select id="recursionQueryTagsByIds" resultMap="TagResultMap">
        WITH RECURSIVE tcte AS (
            SELECT t1.*, 1 AS level
            FROM tag t1
            WHERE enable = 1
            AND id IN <foreach collection="tagIds" item="id" open="(" close=")" separator=","> #{id} </foreach>
        UNION ALL
            SELECT t2.*, tcte.level + 1 AS level
            FROM tag t2
            INNER JOIN tcte ON t2.parent_id = tcte.id
            WHERE t2.enable = 1
        ),
        ranked_tags AS (
            SELECT
                tc.*, ROW_NUMBER() OVER (PARTITION BY tc.id ORDER BY tc.level DESC) AS rn
            FROM tcte tc
            WHERE tc.level > 0 <if test="tagType != null">AND tc.tag_type = #{tagType} </if>
        )
        SELECT ranked_tags.*
        FROM ranked_tags
        WHERE rn = 1
        ORDER BY level;
    </select>
    <select id="selectByIds" resultMap="TagResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM tag
        WHERE id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>
    <select id="selectFullPathIdsByIds" resultType="java.lang.String">
        SELECT
            full_path_id
        FROM tag
        WHERE id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>
    <select id="selectByNames" resultMap="TagResultMap">
        SELECT
            <include refid="Base_Column_List"/>
            FROM tag
        WHERE tag_name IN
        <foreach collection="tagNames" item="tagName" open="(" separator="," close=")">
            #{tagName}
        </foreach>
    </select>

</mapper>