<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookSettingPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookSettingPO">
        <id property="bookId" column="book_id" jdbcType="CHAR"/>
        <result property="assistantOpened" column="assistant_opened" jdbcType="BIT"/>
        <result property="greeting" column="greeting" jdbcType="VARCHAR"/>
        <result property="assistantVersion" column="assistant_version" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        book_id,assistant_opened,greeting,assistant_version,create_time,update_time,create_by,update_by,enable
    </sql>

    <select id="selectByBookId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_setting
        where book_id = #{bookId,jdbcType=CHAR}
    </select>

    <insert id="upsertAssistantOpened" parameterType="com.unipus.digitalbook.model.po.book.BookSettingPO">
        INSERT INTO book_setting (book_id,assistant_opened,assistant_version,create_time,update_time,create_by,update_by,enable)
        VALUES (#{bookId,jdbcType=CHAR}, #{assistantOpened,jdbcType=BIT}, #{assistantVersion,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
        ON DUPLICATE KEY UPDATE
        <if test="assistantVersion != null">
            assistant_version = #{assistantVersion,jdbcType=VARCHAR},
        </if>
        assistant_opened = #{assistantOpened,jdbcType=BIT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=BIGINT}
    </insert>

    <insert id="upsertGreeting" parameterType="com.unipus.digitalbook.model.po.book.BookSettingPO">
        INSERT INTO book_setting (book_id,greeting,create_time,update_time,create_by,update_by,enable)
        VALUES (#{bookId,jdbcType=CHAR}, #{greeting,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
        ON DUPLICATE KEY UPDATE
        greeting = #{greeting,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=BIGINT}
    </insert>

    <select id="selectByBookIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_setting
        where book_id in
        <foreach collection="bookIds" item="bookId" open="(" close=")" separator=",">
            #{bookId}
        </foreach>
    </select>

</mapper>