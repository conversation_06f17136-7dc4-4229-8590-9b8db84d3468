<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.TenantSubscribePOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.tenant.TenantSubscribePO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="messageTopic" column="message_topic" jdbcType="VARCHAR"/>
        <result property="kafkaTopic" column="kafka_topic" jdbcType="VARCHAR"/>
        <result property="kafkaBootstrapServers" column="kafka_bootstrap_servers" jdbcType="VARCHAR"/>
        <result property="httpUrl" column="http_url" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="BOOLEAN"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,message_topic,kafka_topic,kafka_bootstrap_servers,http_url,
        enable,create_by,update_by,create_time,update_time
    </sql>

    <select id="selectByTenantIdAndMessageTopic" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tenant_subscribe
        where enable = true
        and tenant_id = #{tenantId,jdbcType=BIGINT}
        and message_topic = #{messageTopic,jdbcType=VARCHAR}
    </select>

    <select id="selectAll" resultType="com.unipus.digitalbook.model.po.tenant.TenantSubscribePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tenant_subscribe
        where enable = true
    </select>

</mapper>
