<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.unipus.digitalbook.dao.AsyncFailLogPOMapper">

    <!-- 定义结果映射 -->
    <resultMap id="AsyncFailLogPOMap" type="com.unipus.digitalbook.model.po.AsyncFailLogPO">
        <id property="id" column="id" javaType="java.lang.Long"/>
        <result property="op" column="op" javaType="java.lang.String"/>
        <result property="type" column="type" javaType="java.lang.String"/>
        <result property="object" column="object" javaType="java.lang.String"/>
        <result property="failReason" column="fail_reason" javaType="java.lang.String"/>
        <result property="status" column="status" javaType="java.lang.Integer"/>
        <result property="createTime" column="create_time" javaType="java.util.Date"/>
        <result property="retryFailTime" column="retry_fail_time" javaType="java.util.Date"/>
        <result property="successTime" column="success_time" javaType="java.util.Date"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, op, `type`, object, fail_reason, status, create_time, retry_fail_time, success_time
    </sql>
    <!-- 插入操作 -->
    <insert id="insertAsyncFailLog" parameterType="com.unipus.digitalbook.model.po.AsyncFailLogPO">
        INSERT INTO async_fail_log (op, type, object, fail_reason, status, create_time, retry_fail_time, success_time)
        VALUES (#{op}, #{type}, #{object}, #{failReason}, #{status}, #{createTime}, #{retryFailTime}, #{successTime})
    </insert>

    <!-- 根据ID查询操作 -->
    <select id="selectAsyncFailLog" parameterType="map" resultMap="AsyncFailLogPOMap">
        SELECT id, op, type, object, fail_reason, status, create_time, retry_fail_time, success_time
        FROM async_fail_log
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="op != null">
            AND op = #{op}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>
    <select id="selectFailCountByOperation" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM async_fail_log where op = #{operation} and status != 0
    </select>
    <select id="getByOperation" resultMap="AsyncFailLogPOMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM async_fail_log where op = #{operation} and status != 0 limit 100
    </select>

</mapper>
