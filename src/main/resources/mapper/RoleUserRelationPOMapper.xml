<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.RoleUserRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.role.RoleUserRelationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,role_id,
        org_id,enable,create_by,
        update_by,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role_user_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByUserIdAndOrgId" resultType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role_user_relation where user_id = #{userId}
        and enable = true
        <if test="orgId != null">
            and org_id = #{orgId}
        </if>
    </select>
    <select id="selectIdByRoleId" resultType="java.lang.Long">
        select
        id
        from role_user_relation where role_id = #{roleId}
        and enable = true
    </select>

    <select id="selectRoleMapByUserId" resultType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO">
            select
            ru.id as id,
            ru.user_id as userId,
            ru.role_id as roleId,
            ru.org_id as orgId
        from role_user_relation ru
        inner join role r on ru.role_id = r.id
        where
        ru.user_id = #{userId}
        and ru.enable = true
        and r.status = 1
        and r.enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from role_user_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO" useGeneratedKeys="true">
        insert into role_user_relation
        ( id,user_id,role_id
        ,org_id,enable,create_by
        ,update_by,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{userId,jdbcType=BIGINT},#{roleId,jdbcType=BIGINT}
        ,#{orgId,jdbcType=BIGINT},#{enable,jdbcType=TINYINT},#{createBy,jdbcType=BIGINT}
        ,#{updateBy,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO" useGeneratedKeys="true">
        insert into role_user_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="userId != null">user_id,</if>
                <if test="roleId != null">role_id,</if>
                <if test="orgId != null">org_id,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
                <if test="roleId != null">#{roleId,jdbcType=BIGINT},</if>
                <if test="orgId != null">#{orgId,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=TINYINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO">
        update role_user_relation
        <set>
                <if test="userId != null">
                    user_id = #{userId,jdbcType=BIGINT},
                </if>
                <if test="roleId != null">
                    role_id = #{roleId,jdbcType=BIGINT},
                </if>
                <if test="orgId != null">
                    org_id = #{orgId,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=TINYINT},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.role.RoleUserRelationPO">
        update role_user_relation
        set 
            user_id =  #{userId,jdbcType=BIGINT},
            role_id =  #{roleId,jdbcType=BIGINT},
            org_id =  #{orgId,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=TINYINT},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="bathLogicalDelete">
        update role_user_relation set enable = false, update_by = #{updateBy} where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="batchInsertOrUpdate">
        insert into role_user_relation (user_id, role_id, org_id, enable, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.roleId},#{item.orgId},#{item.enable},#{item.createBy},#{item.updateBy})
        </foreach>
        on duplicate key update
        enable = values(enable), update_by = values(update_by),
        create_by = CASE WHEN enable = true THEN VALUES(create_by) ELSE create_by END,
        create_time = CASE WHEN enable = true THEN NOW() ELSE create_time END
    </update>
</mapper>
