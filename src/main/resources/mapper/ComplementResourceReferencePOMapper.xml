<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ComplementResourceReferencePOMapper">

    <resultMap id="BookMediaReferencePOResultMap" type="com.unipus.digitalbook.model.po.complement.ComplementResourceReferencePO">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="complementResourceId" column="complement_resource_id" jdbcType="CHAR"/>
        <result property="position" column="position" jdbcType="VARCHAR"/>
        <result property="chapterId" column="chapter_id" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, complement_resource_id, position, chapter_id, create_time, update_time, create_by, update_by, enable
    </sql>

    <insert id="insertReference" parameterType="com.unipus.digitalbook.model.po.complement.ComplementResourceReferencePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO complement_resource_reference (complement_resource_id, position, chapter_id, create_by, update_by, enable)
        VALUES (#{complementResourceId}, #{position}, #{chapterId}, #{createBy}, #{updateBy}, #{enable})
    </insert>

    <update id="updateReference" parameterType="com.unipus.digitalbook.model.po.complement.ComplementResourceReferencePO">
        UPDATE complement_resource_reference
        SET
        <if test="complementResourceId!= null and complementResourceId !=''">
            complement_resource_id = #{complementResourceId},
        </if>
        <if test="position!= null and position !=''">
            position = #{position},
        </if>
        <if test="chapterId!= null and chapterId != ''">
            chapter_id = #{chapterId},
        </if>
        <if test="enable!= null">
            enable = #{enable},
        </if>
            update_by = #{updateBy}
        WHERE
            id = #{id}
    </update>

    <select id="selectReference" parameterType="String" resultMap="BookMediaReferencePOResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM complement_resource_reference
        WHERE
            enable = 1
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="complementResourceId!= null and complementResourceId !=''">
            and complement_resource_id = #{complementResourceId}
        </if>
        <if test="position!= null and position !=''">
            and position = #{position}
        </if>
        <if test="chapterId!= null and chapterId != ''">
            and chapter_id = #{chapterId}
        </if>
    </select>

</mapper>
