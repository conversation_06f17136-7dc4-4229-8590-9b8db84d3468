<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.TagReferencePOMapper">
    <!-- 定义 resultMap -->
    <resultMap id="TagReferenceResultMap" type="com.unipus.digitalbook.model.po.tag.TagReferencePO">
        <id column="id" property="id"/>
        <result column="resource_id" property="resourceId"/>
        <result column="resource_version" property="resourceVersion"/>
        <result column="resource_type" property="resourceType"/>
        <result column="tag_id" property="tagId"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="enable" property="enable"/>
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, resource_id, resource_version, resource_type, tag_id, sort_order, create_time, update_time, create_by, update_by, enable
    </sql>

    <insert id="insertOrUpdate" parameterType="TagReferencePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tag_reference ( resource_id, resource_version, resource_type, tag_id, create_by, update_by )
        VALUES ( #{resourceId}, #{resourceVersion}, #{resourceType}, #{tagId}, #{createBy}, #{updateBy} )
        ON DUPLICATE KEY UPDATE
        tag_id = VALUES(tag_id),
        update_by = VALUES(update_by)
    </insert>

    <insert id="batchInsertOrUpdate">
        INSERT INTO tag_reference ( resource_id, resource_version, resource_type, tag_id, sort_order, create_by, update_by  )
        VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.resourceId}, #{item.resourceVersion},  #{item.resourceType},  #{item.tagId}, #{item.sortOrder}, #{item.createBy}, #{item.updateBy} )
        </foreach>
        ON DUPLICATE KEY UPDATE
        tag_id = VALUES(tag_id),
        sort_order = VALUES(sort_order),
        update_by = VALUES(update_by),
        enable = true
    </insert>
    <update id="deleteByIds">
        UPDATE tag_reference
        SET enable = false
        WHERE id IN
        <foreach item="item" collection="ids" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <!-- 批量查询 -->
    <select id="selectByResourceIds" resultMap="TagReferenceResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tag_reference
        WHERE enable = 1
        AND resource_id IN
        <foreach item="resourceId" index="index" collection="resourceIds" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </select>
    <select id="selectByResourceIdAndVersion" resultMap="TagReferenceResultMap">
        SELECT <include refid="Base_Column_List"/>
            FROM tag_reference
        WHERE enable = 1
        AND resource_id IN
        <foreach item="resourceId" index="index" collection="resourceIds" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
        AND resource_version = #{version}
        AND resource_type = #{resourceType}
    </select>

    <!-- 更新 -->
    <update id="update" parameterType="com.unipus.digitalbook.model.po.tag.TagReferencePO">
        UPDATE tag_reference
        SET
            update_by = #{updateBy},
            enable = #{enable}
        WHERE
            id = #{id}
    </update>

</mapper>