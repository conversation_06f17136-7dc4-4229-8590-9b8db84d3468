<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QuestionPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.QuestionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bizQuestionId" column="biz_question_id" jdbcType="CHAR"/>
            <result property="versionNumber" column="version_number" jdbcType="CHAR"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="questionType" column="question_type" jdbcType="INTEGER"/>
            <result property="questionText" column="question_text" jdbcType="VARCHAR"/>
            <result property="score" column="score" jdbcType="DECIMAL"/>
            <result property="isJudgment" column="is_judgment" jdbcType="DECIMAL"/>
            <result property="isScoring" column="is_scoring" jdbcType="DECIMAL"/>
            <result property="sortOrder" column="sort_order" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_question_id,version_number,
        group_id,question_type,question_text,score,is_judgment,is_scoring, sort_order,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByGroupIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from question
        where  group_id in
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        and enable = true
    </select>
    <select id="selectLatestVersionByBizQuestionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from question
        where  biz_question_id = #{bizQuestionId}
        and enable = true
        order by version_number desc
        limit 1
    </select>
    <select id="selectByGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question
        where group_id = #{groupId}
        and enable = true
    </select>
    <select id="selectByBizQuestionIdsAndVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question
        where biz_question_id in
        <foreach collection="bizQuestionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and version_number = #{versionNumber}
        and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from question
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <update id="deleteByIds">
        update
        question
        set enable = false and update_by = #{opsUserId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionPO" useGeneratedKeys="true">
        INSERT INTO question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizQuestionId != null">biz_question_id,</if>
            <if test="versionNumber != null">version_number,</if>
            <if test="groupId != null">group_id,</if>
            <if test="questionType != null">question_type,</if>
            <if test="questionText != null">question_text,</if>
            <if test="score != null">score,</if>
            <if test="isJudgment != null">is_judgment,</if>
            <if test="isScoring != null">is_scoring,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bizQuestionId != null">#{bizQuestionId,jdbcType=CHAR},</if>
            <if test="versionNumber != null">#{versionNumber,jdbcType=CHAR},</if>
            <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
            <if test="questionType != null">#{questionType,jdbcType=INTEGER},</if>
            <if test="questionText != null">#{questionText,jdbcType=VARCHAR},</if>
            <if test="score != null">#{score,jdbcType=DECIMAL},</if>
            <if test="isJudgment != null">#{isJudgment,jdbcType=BIT},</if>
            <if test="isScoring != null">#{isScoring,jdbcType=BIT},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            id = LAST_INSERT_ID(id),
            <if test="groupId != null">group_id = VALUES(group_id),</if>
            <if test="questionType != null">question_type = VALUES(question_type),</if>
            <if test="questionText != null">question_text = VALUES(question_text),</if>
            <if test="score != null">score = VALUES(score),</if>
            <if test="isJudgment != null">is_judgment = VALUES(is_judgment),</if>
            <if test="isScoring != null">is_scoring = VALUES(is_scoring),</if>
            <if test="sortOrder != null">sort_order = VALUES(sort_order),</if>
            <if test="updateBy != null">update_by = VALUES(update_by),</if>
            <if test="enable != null">enable = VALUES(enable),</if>
            update_time = NOW(),
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.List">
        INSERT INTO question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, biz_question_id, version_number, group_id, question_type, question_text,is_judgment, is_scoring, sort_order, create_by, update_by
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.bizQuestionId,jdbcType=CHAR},
            #{item.versionNumber,jdbcType=CHAR},
            #{item.groupId,jdbcType=BIGINT},
            #{item.questionType,jdbcType=INTEGER},
            #{item.questionText,jdbcType=VARCHAR},
            #{item.score,jdbcType=DECIMAL},
            #{item.isJudgment,jdbcType=BIT},
            #{item.isScoring,jdbcType=BIT},
            #{item.sortOrder,jdbcType=INTEGER},
            #{item.createBy,jdbcType=BIGINT},
            #{item.updateBy,jdbcType=BIGINT}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        group_id = COALESCE(VALUES(group_id), question.group_id),
        question_type = COALESCE(VALUES(question_type), question.question_type),
        question_text = COALESCE(VALUES(question_text), question.question_text),
        score = COALESCE(VALUES(score), question.score),
        is_judgment = COALESCE(VALUES(is_judgment), question.is_judgment),
        is_scoring = COALESCE(VALUES(is_scoring), question.is_scoring),
        sort_order = COALESCE(VALUES(sort_order), question.sort_order),
        update_by = COALESCE(VALUES(update_by), question.update_by),
        enable = COALESCE(VALUES(enable), question.enable);
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.QuestionPO">
        update question
        <set>
                <if test="bizQuestionId != null">
                    biz_question_id = #{bizQuestionId,jdbcType=CHAR},
                </if>
                <if test="versionNumber != null">
                    version_number = #{versionNumber,jdbcType=CHAR},
                </if>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="questionType != null">
                    question_type = #{questionType,jdbcType=INTEGER},
                </if>
                <if test="questionText != null">
                    question_type = #{questionText,jdbcType=VARCHAR},
                </if>
                <if test="score != null">
                    score = #{score,jdbcType=DECIMAL},
                </if>
                <if test="isScoring != null">
                    is_scoring = #{isScoring,jdbcType=BIT},
                </if>
                <if test="isJudgment != null">
                    is_judgment = #{isJudgment,jdbcType=BIT},
                </if>
                <if test="sortOrder != null">
                    sort_order = #{sortOrder,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
