<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.TenantChannelPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.tenant.TenantChannelPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="messageTopic" column="message_topic" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="INTEGER"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
        <result property="headerGenerator" column="header_generator" jdbcType="VARCHAR"/>
        <result property="requestBodyConverter" column="request_body_converter" jdbcType="VARCHAR"/>
        <result property="responseBodyConverter" column="response_body_converter" jdbcType="VARCHAR"/>
        <result property="httpMethod" column="http_method" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="BOOLEAN"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,message_topic,channel,priority,header_generator,http_method,
        request_body_converter,response_body_converter,enable,create_by,update_by,create_time,update_time
    </sql>

    <select id="selectByTenantIdAndMessageTopic" resultMap="BaseResultMap" resultType="com.unipus.digitalbook.model.po.tenant.TenantChannelPO">
        select
        <include refid="Base_Column_List" />
        from tenant_channel
        where enable = true
        and tenant_id = #{tenantId,jdbcType=BIGINT}
        and message_topic = #{messageTopic,jdbcType=VARCHAR}
    </select>

</mapper>
