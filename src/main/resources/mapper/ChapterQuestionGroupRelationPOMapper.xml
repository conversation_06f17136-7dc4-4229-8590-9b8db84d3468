<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChapterQuestionGroupRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="versionChapterId" column="version_chapter_id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,version_chapter_id,group_id,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from chapter_question_group_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectQuestionIdsByVersionChapterId" resultType="java.lang.Long">
        select group_id from chapter_question_group_relation
        where version_chapter_id = #{versionChapterId,jdbcType=BIGINT}
        and enable = true
    </select>
    <select id="selectQuestionIdByVersionChapterIdAndBizGroupId" resultType="java.lang.Long">
        SELECT c.group_id
        FROM chapter_question_group_relation c
        JOIN (
            SELECT id FROM question_group WHERE biz_group_id = #{bizGroupId}
        ) q ON c.group_id = q.id
        WHERE c.version_chapter_id = #{versionChapterId}
          AND c.enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from chapter_question_group_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO" useGeneratedKeys="true">
        insert into chapter_question_group_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="versionChapterId != null">version_chapter_id,</if>
                <if test="groupId != null">group_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="versionChapterId != null">#{versionChapterId,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into chapter_question_group_relation (version_chapter_id, group_id, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.versionChapterId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO">
        update chapter_question_group_relation
        <set>
                <if test="versionChapterId != null">
                    version_chapter_id = #{versionChapterId,jdbcType=BIGINT},
                </if>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
