<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChoiceQuestionOptionPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="questionId" column="question_id" jdbcType="BIGINT"/>
            <result property="optionId" column="option_id" jdbcType="CHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,question_id,option_id,
        name,content,sort_order,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from choice_question_option
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByQuestionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from choice_question_option
        where  question_id = #{questionId,jdbcType=BIGINT} and enable = true
    </select>
    <select id="selectByQuestionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from choice_question_option
        where  question_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from choice_question_option
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <update id="deleteByIds">
        update choice_question_option
        set enable = false,
            update_by = #{opsUserId,jdbcType=BIGINT},
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO" useGeneratedKeys="true">
        insert into choice_question_option
        ( id,question_id,option_id
        ,name,content,sort_order,
        ,create_time,update_time,create_by
        ,update_by,enable)
        values (#{id,jdbcType=BIGINT},#{questionId,jdbcType=BIGINT},#{optionId,jdbcType=CHAR}
        ,#{name,jdbcType=VARCHAR},#{content,jdbcType=VARCHAR}, #{sortOrder,jdbcType=INTEGER}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT}
        ,#{updateBy,jdbcType=BIGINT},#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO"
            useGeneratedKeys="true">
        INSERT INTO choice_question_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="optionId != null">option_id,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="questionId != null">#{questionId,jdbcType=BIGINT},</if>
            <if test="optionId != null">#{optionId,jdbcType=CHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            <if test="questionId != null">question_id = VALUES(question_id),</if>
            <if test="optionId != null">option_id = VALUES(option_id),</if>
            <if test="name != null">name = VALUES(name),</if>
            <if test="content != null">content = VALUES(content),</if>
            <if test="sortOrder != null">sort_order = VALUES(sort_order),</if>
            <if test="updateTime != null">update_time = VALUES(update_time),</if>
            <if test="updateBy != null">update_by = VALUES(update_by),</if>
            <if test="enable != null">enable = VALUES(enable),</if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO choice_question_option (
         question_id, option_id, name, content, sort_order, create_by, update_by
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.questionId},
            #{item.optionId},
            #{item.name},
            #{item.content},
            #{item.sortOrder},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        id = LAST_INSERT_ID(id),
        name = COALESCE(VALUES(name), choice_question_option.name),
        content = COALESCE(VALUES(content), choice_question_option.content),
        sort_order = COALESCE(VALUES(sort_order), choice_question_option.sort_order),
        update_by = COALESCE(VALUES(update_by), choice_question_option.update_by),
        enable = COALESCE(VALUES(enable), choice_question_option.enable);
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO">
        update choice_question_option
        <set>
                <if test="questionId != null">
                    question_id = #{questionId,jdbcType=BIGINT},
                </if>
                <if test="optionId != null">
                    option_id = #{optionId,jdbcType=CHAR},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=VARCHAR},
                </if>
                <if test="sortOrder != null">
                    sort_order = #{sortOrder,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
