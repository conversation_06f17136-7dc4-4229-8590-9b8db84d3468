<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QuestionThirdMappingPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="bizGroupId" column="biz_group_id" jdbcType="CHAR"/>
            <result property="versionNumber" column="version_number" jdbcType="VARCHAR"/>
            <result property="thirdId" column="third_id" jdbcType="VARCHAR"/>
            <result property="thirdVersion" column="third_version" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,tenant_id,
        biz_group_id,version_number,third_id,
        third_version,create_time,update_time,
        create_by,update_by
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_third_mapping
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from question_third_mapping
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO" useGeneratedKeys="true">
        insert into question_third_mapping
        ( id,group_id,tenant_id
        ,biz_group_id,version_number,third_id
        ,third_version,create_time,update_time
        ,create_by,update_by)
        values (#{id,jdbcType=BIGINT},#{groupId,jdbcType=BIGINT},#{tenantId,jdbcType=BIGINT}
        ,#{bizGroupId,jdbcType=CHAR},#{versionNumber,jdbcType=VARCHAR},#{thirdId,jdbcType=VARCHAR}
        ,#{thirdVersion,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO" useGeneratedKeys="true">
        insert into question_third_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="groupId != null">group_id,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="bizGroupId != null">biz_group_id,</if>
                <if test="versionNumber != null">version_number,</if>
                <if test="thirdId != null">third_id,</if>
                <if test="thirdVersion != null">third_version,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=BIGINT},</if>
                <if test="bizGroupId != null">#{bizGroupId,jdbcType=CHAR},</if>
                <if test="versionNumber != null">#{versionNumber,jdbcType=VARCHAR},</if>
                <if test="thirdId != null">#{thirdId,jdbcType=VARCHAR},</if>
                <if test="thirdVersion != null">#{thirdVersion,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO">
        update question_third_mapping
        <set>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=BIGINT},
                </if>
                <if test="bizGroupId != null">
                    biz_group_id = #{bizGroupId,jdbcType=CHAR},
                </if>
                <if test="versionNumber != null">
                    version_number = #{versionNumber,jdbcType=VARCHAR},
                </if>
                <if test="thirdId != null">
                    third_id = #{thirdId,jdbcType=VARCHAR},
                </if>
                <if test="thirdVersion != null">
                    third_version = #{thirdVersion,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO">
        update question_third_mapping
        set 
            group_id =  #{groupId,jdbcType=BIGINT},
            tenant_id =  #{tenantId,jdbcType=BIGINT},
            biz_group_id =  #{bizGroupId,jdbcType=CHAR},
            version_number =  #{versionNumber,jdbcType=VARCHAR},
            third_id =  #{thirdId,jdbcType=VARCHAR},
            third_version =  #{thirdVersion,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
