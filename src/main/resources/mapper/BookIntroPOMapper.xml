<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookIntroPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookIntroPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookId" column="book_id" jdbcType="CHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="versionNumber" column="version_number" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_id,description,version_number,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_intro
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from book_intro
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookIntroPO" useGeneratedKeys="true">
        insert into book_intro
        ( id,book_id,description, version_number
        ,create_time,update_time,create_by
        ,update_by,enable)
        values (#{id,jdbcType=BIGINT},#{bookId,jdbcType=CHAR},#{description,jdbcType=VARCHAR}, #{versionNumber,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT}
        ,#{updateBy,jdbcType=BIGINT},#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookIntroPO" useGeneratedKeys="true">
        insert into book_intro
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="description != null">description,</if>
            <if test="versionNumber != null">version_number,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bookId != null">#{bookId,jdbcType=CHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="versionNumber != null">#{versionNumber,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookIntroPO">
        update book_intro
        <set>
            <if test="bookId != null">
                book_id = #{bookId,jdbcType=CHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="versionNumber != null">
                version_number = #{versionNumber,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookIntroPO">
        update book_intro
        set
        book_id =  #{bookId,jdbcType=CHAR},
        description =  #{description,jdbcType=VARCHAR},
        version_number = #{versionNumber,jdbcType=VARCHAR},
        create_time =  #{createTime,jdbcType=TIMESTAMP},
        update_time =  #{updateTime,jdbcType=TIMESTAMP},
        create_by =  #{createBy,jdbcType=BIGINT},
        update_by =  #{updateBy,jdbcType=BIGINT},
        enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByBookId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_intro
        where book_id = #{bookId,jdbcType=CHAR} and enable = true and version_number = '0'
    </select>

    <select id="selectByBookIdAndVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_intro
        where book_id = #{bookId} and version_number = #{version} and enable = true
    </select>
    <select id="selectByBookVersion" resultMap="BaseResultMap">
        select
            bi.id as id,
            bi.book_id as book_id,
            bi.description as description,
            bi.version_number as version_number,
            bi.create_time as create_time,
            bi.update_time as update_time,
            bi.create_by as create_by,
            bi.update_by as update_by,
            bi.enable as enable
        from book_intro bi
        left join book_version_info_version_relation bv on bi.id = bv.info_id
        where bi.enable = true
          and bv.enable = true
          and bv.book_version_id = #{bookVersionId}
          and bv.content_type = 2
    </select>

    <insert id="generateVersion">
        insert into book_intro
        ( book_id,description, version_number
        ,create_time,update_time,create_by
        ,update_by,enable)
        select book_id,description, #{version}
        ,create_time,update_time,create_by
        ,update_by,enable from book_intro
        where book_id = #{bookId} and version_number = '0' and enable = true
    </insert>

    <select id="selectListByBookIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM book_intro
        WHERE version_number = '0' AND enable = true AND book_id IN
        <foreach collection="bookIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectListByBookIdAndVersionList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM book_intro
        WHERE enable = true AND
        <foreach collection="list" item="item" open="(" close=")" separator=" OR ">
            (book_id = #{item.bookId} AND version_number = #{item.versionNumber})
        </foreach>
    </select>

    <select id="selectListByBookVersionIds" resultMap="BaseResultMap">
        select
            bi.id as id,
            bi.book_id as book_id,
            bi.description as description,
            bi.version_number as version_number,
            bi.create_time as create_time,
            bi.update_time as update_time,
            bi.create_by as create_by,
            bi.update_by as update_by,
            bi.enable as enable
        from book_intro bi
        left join book_version_info_version_relation bv on bi.id = bv.info_id
        where bi.enable = true
          and bv.enable = true
          and bv.content_type = 2
          and bv.book_version_id in
            <foreach collection="bookVersionIds" item="bookVersionId" open="(" close=")" separator=",">
                #{bookVersionId}
            </foreach>

    </select>

</mapper>
