<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QuestionGroupSettingPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="CHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,content,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_group_setting
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_group_setting
        where  group_id = #{groupId,jdbcType=BIGINT} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from question_group_setting
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO" useGeneratedKeys="true">
        insert into question_group_setting
        ( id,group_id,content
        ,create_time,update_time,create_by
        ,update_by,enable)
        values (#{id,jdbcType=BIGINT},#{groupId,jdbcType=BIGINT},#{content,jdbcType=OTHER}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT}
        ,#{updateBy,jdbcType=BIGINT},#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO" useGeneratedKeys="true">
        insert into question_group_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="groupId != null">group_id,</if>
                <if test="content != null">content,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
                <if test="content != null">#{content,jdbcType=OTHER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" useGeneratedKeys="true" keyProperty="id">
        insert into question_group_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
            <if test="content != null">#{content,jdbcType=OTHER},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
        <trim prefix="on duplicate key update " suffixOverrides=",">
            <if test="content != null">content = #{content,jdbcType=OTHER},</if>
            <if test="updateBy != null">update_by = #{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">enable = #{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO">
        update question_group_setting
        <set>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=OTHER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
