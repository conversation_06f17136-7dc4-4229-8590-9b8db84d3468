<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookBasicPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookBasicPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="chinese_name" jdbcType="VARCHAR" property="chineseName" />
    <result column="english_name" jdbcType="VARCHAR" property="englishName" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="series_id" jdbcType="BIGINT" property="seriesId" />
    <result column="course" jdbcType="VARCHAR" property="course" />
    <result column="course_nature" jdbcType="VARCHAR" property="courseNature" />
    <result column="applicable_major" jdbcType="VARCHAR" property="applicableMajor" />
    <result column="applicable_grade" jdbcType="VARCHAR" property="applicableGrade" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="pc_cover_url" jdbcType="VARCHAR" property="pcCoverUrl" />
    <result column="app_horizontal_cover_url" jdbcType="VARCHAR" property="appHorizontalCoverUrl" />
    <result column="app_vertical_cover_url" jdbcType="VARCHAR" property="appVerticalCoverUrl" />
    <result column="light_color" jdbcType="CHAR" property="lightColor" />
    <result column="dark_color" jdbcType="CHAR" property="darkColor" />
    <result column="version_number" jdbcType="VARCHAR" property="versionNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="digital_flag" jdbcType="BIT" property="digitalFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, book_id, chinese_name, english_name, `language`, business_type, series_id,
    course, course_nature, applicable_major, applicable_grade, contact_phone, contact_email,
    pc_cover_url, app_horizontal_cover_url, app_vertical_cover_url, light_color, dark_color,
    version_number, create_time, update_time, create_by, update_by, `enable`, digital_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_basic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from book_basic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.book.BookBasicPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_basic (book_id, chinese_name, english_name,
    `language`, business_type, series_id,
    course, course_nature,
    applicable_major, applicable_grade, contact_phone,
    contact_email, pc_cover_url, app_horizontal_cover_url,
    app_vertical_cover_url, light_color, dark_color,
    version_number, create_time, update_time,
    create_by, update_by, `enable`, digital_flag
    )
    values (#{bookId,jdbcType=CHAR}, #{chineseName,jdbcType=VARCHAR}, #{englishName,jdbcType=VARCHAR},
    #{language,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{seriesId,jdbcType=BIGINT},
    #{course,jdbcType=VARCHAR}, #{courseNature,jdbcType=VARCHAR},
    #{applicableMajor,jdbcType=VARCHAR}, #{applicableGrade,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR},
    #{contactEmail,jdbcType=VARCHAR}, #{pcCoverUrl,jdbcType=VARCHAR}, #{appHorizontalCoverUrl,jdbcType=VARCHAR},
    #{appVerticalCoverUrl,jdbcType=VARCHAR}, #{lightColor,jdbcType=CHAR}, #{darkColor,jdbcType=CHAR},
    #{versionNumber,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT}, #{digitalFlag,jdbcType=BIT}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.book.BookBasicPO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_basic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        book_id,
      </if>
      <if test="chineseName != null">
        chinese_name,
      </if>
      <if test="englishName != null">
        english_name,
      </if>
      <if test="language != null">
        `language`,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="course != null">
        course,
      </if>
      <if test="courseNature != null">
        course_nature,
      </if>
      <if test="applicableMajor != null">
        applicable_major,
      </if>
      <if test="applicableGrade != null">
        applicable_grade,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="pcCoverUrl != null">
        pc_cover_url,
      </if>
      <if test="appHorizontalCoverUrl != null">
        app_horizontal_cover_url,
      </if>
      <if test="appVerticalCoverUrl != null">
        app_vertical_cover_url,
      </if>
      <if test="lightColor != null">
        light_color,
      </if>
      <if test="darkColor != null">
        dark_color,
      </if>
      <if test="versionNumber != null">
        version_number,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="digitalFlag != null">
        digital_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="chineseName != null">
        #{chineseName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=BIGINT},
      </if>
      <if test="course != null">
        #{course,jdbcType=VARCHAR},
      </if>
      <if test="courseNature != null">
        #{courseNature,jdbcType=VARCHAR},
      </if>
      <if test="applicableMajor != null">
        #{applicableMajor,jdbcType=VARCHAR},
      </if>
      <if test="applicableGrade != null">
        #{applicableGrade,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="pcCoverUrl != null">
        #{pcCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="appHorizontalCoverUrl != null">
        #{appHorizontalCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="appVerticalCoverUrl != null">
        #{appVerticalCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="lightColor != null">
        #{lightColor,jdbcType=CHAR},
      </if>
      <if test="darkColor != null">
        #{darkColor,jdbcType=CHAR},
      </if>
      <if test="versionNumber != null">
        #{versionNumber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="digitalFlag != null">
        #{digitalFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookBasicPO">
    update book_basic
    <set>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="chineseName != null">
        chinese_name = #{chineseName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        english_name = #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        `language` = #{language,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=BIGINT},
      </if>
      <if test="course != null">
        course = #{course,jdbcType=VARCHAR},
      </if>
      <if test="courseNature != null">
        course_nature = #{courseNature,jdbcType=VARCHAR},
      </if>
      <if test="applicableMajor != null">
        applicable_major = #{applicableMajor,jdbcType=VARCHAR},
      </if>
      <if test="applicableGrade != null">
        applicable_grade = #{applicableGrade,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="pcCoverUrl != null">
        pc_cover_url = #{pcCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="appHorizontalCoverUrl != null">
        app_horizontal_cover_url = #{appHorizontalCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="appVerticalCoverUrl != null">
        app_vertical_cover_url = #{appVerticalCoverUrl,jdbcType=VARCHAR},
      </if>
      <if test="lightColor != null">
        light_color = #{lightColor,jdbcType=CHAR},
      </if>
      <if test="darkColor != null">
        dark_color = #{darkColor,jdbcType=CHAR},
      </if>
      <if test="versionNumber != null">
        version_number = #{versionNumber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="digitalFlag != null">
        digital_flag = #{digitalFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookBasicPO">
    update book_basic
    set book_id = #{bookId,jdbcType=CHAR},
    chinese_name = #{chineseName,jdbcType=VARCHAR},
    english_name = #{englishName,jdbcType=VARCHAR},
    `language` = #{language,jdbcType=VARCHAR},
    business_type = #{businessType,jdbcType=VARCHAR},
    series_id = #{seriesId,jdbcType=BIGINT},
    course = #{course,jdbcType=VARCHAR},
    course_nature = #{courseNature,jdbcType=VARCHAR},
    applicable_major = #{applicableMajor,jdbcType=VARCHAR},
    applicable_grade = #{applicableGrade,jdbcType=VARCHAR},
    contact_phone = #{contactPhone,jdbcType=VARCHAR},
    contact_email = #{contactEmail,jdbcType=VARCHAR},
    pc_cover_url = #{pcCoverUrl,jdbcType=VARCHAR},
    app_horizontal_cover_url = #{appHorizontalCoverUrl,jdbcType=VARCHAR},
    app_vertical_cover_url = #{appVerticalCoverUrl,jdbcType=VARCHAR},
    light_color = #{lightColor,jdbcType=CHAR},
    dark_color = #{darkColor,jdbcType=CHAR},
    version_number = #{versionNumber,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=BIGINT},
    update_by = #{updateBy,jdbcType=BIGINT},
    `enable` = #{enable,jdbcType=BIT},
    digital_flag = #{digitalFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBookId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_basic
    where book_id = #{bookId,jdbcType=CHAR} and enable = true and version_number = '0'
  </select>

  <select id="selectByBookName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_basic
    where enable = true and version_number = '0'
    <if test="bookName != null and bookName != ''">
      and chinese_name like concat('%',#{bookName,jdbcType=VARCHAR},'%')
    </if>
    order by create_time desc
  </select>

    <select id="selectByBookIdAndSeriesIdAndName"
            resultType="com.unipus.digitalbook.model.po.book.BookBasicPO" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from book_basic
      where enable = true and version_number = '0'
      and book_id in
      <foreach collection="bookIds" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
      <if test="seriesId != null">
        and series_id = #{seriesId}
      </if>
      <if test="bookName != null and bookName != ''">
        and (
        chinese_name like concat('%',#{bookName,jdbcType=VARCHAR},'%')
        or english_name like concat('%',#{bookName,jdbcType=VARCHAR},'%')
        )
      </if>
    </select>

  <select id="selectByBookIdAndVersion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_basic
    where book_id = #{bookId} and version_number = #{version} and enable = true
  </select>

    <select id="selectByBookVersion" resultMap="BaseResultMap">
      select
        bb.id as id,
        bb.book_id as book_id,
        bb.chinese_name as chinese_name,
        bb.english_name as english_name,
        bb.language as language,
        bb.business_type as business_type,
        bb.series_id as series_id,
        bb.course as course,
        bb.course_nature as course_nature,
        bb.applicable_major as applicable_major,
        bb.applicable_grade as applicable_grade,
        bb.contact_phone as contact_phone,
        bb.contact_email as contact_email,
        bb.pc_cover_url as pc_cover_url,
        bb.app_horizontal_cover_url as app_horizontal_cover_url,
        bb.app_vertical_cover_url as app_vertical_cover_url,
        bb.light_color as light_color,
        bb.dark_color as dark_color,
        bb.version_number as version_number,
        bb.create_time as create_time,
        bb.update_time as update_time,
        bb.create_by as create_by,
        bb.update_by as update_by,
        bb.enable as enable,
        bb.digital_flag as digital_flag
      from book_basic bb
             left join book_version_info_version_relation bv on bb.id = bv.info_id
      where bv.book_version_id = #{bookVersionId}
        and bv.content_type = 1
        and bb.enable = true
        and bv.enable = true
    </select>

  <insert id="generateVersion">
    insert into book_basic
    ( book_id, chinese_name, english_name,
    `language`, business_type, series_id,
    course, course_nature,
    applicable_major, applicable_grade, contact_phone,
    contact_email, pc_cover_url, app_horizontal_cover_url,
    app_vertical_cover_url, light_color, dark_color,
    version_number, create_time, update_time,
    create_by, update_by, `enable`, digital_flag
    )
    select book_id, chinese_name, english_name,
    `language`, business_type, series_id,
    course, course_nature,
    applicable_major, applicable_grade, contact_phone,
    contact_email, pc_cover_url, app_horizontal_cover_url,
    app_vertical_cover_url, light_color, dark_color,
    #{version}, create_time, update_time,
    create_by, update_by, `enable`, digital_flag from book_basic
    where book_id = #{bookId} and version_number = '0' and enable = true
  </insert>

  <select id="selectListByBookIdAndVersionList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM book_basic
    WHERE enable = true AND
    <foreach collection="list" item="item" open="(" close=")" separator=" OR ">
      (book_id = #{item.bookId} AND version_number = #{item.versionNumber})
    </foreach>
  </select>

  <select id="selectListByBookVersionIds" resultMap="BaseResultMap">
    select
      bb.id as id,
      bb.book_id as book_id,
      bb.chinese_name as chinese_name,
      bb.english_name as english_name,
      bb.language as language,
      bb.business_type as business_type,
      bb.series_id as series_id,
      bb.course as course,
      bb.course_nature as course_nature,
      bb.applicable_major as applicable_major,
      bb.applicable_grade as applicable_grade,
      bb.contact_phone as contact_phone,
      bb.contact_email as contact_email,
      bb.pc_cover_url as pc_cover_url,
      bb.app_horizontal_cover_url as app_horizontal_cover_url,
      bb.app_vertical_cover_url as app_vertical_cover_url,
      bb.light_color as light_color,
      bb.dark_color as dark_color,
      bb.version_number as version_number,
      bb.create_time as create_time,
      bb.update_time as update_time,
      bb.create_by as create_by,
      bb.update_by as update_by,
      bb.enable as enable,
      bb.digital_flag as digital_flag
    from book_basic bb
    left join book_version_info_version_relation bv on bb.id = bv.info_id
    where bb.enable = true
        and bv.enable = true
        and bv.content_type = 1
        and bv.book_version_id in
          <foreach collection="bookVersionIds" item="bookVersionId" open="(" close=")" separator=",">
            #{bookVersionId}
          </foreach>
  </select>

</mapper>