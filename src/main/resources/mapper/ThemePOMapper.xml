<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ThemePOMapper">

    <resultMap id="ThemeResultMap" type="com.unipus.digitalbook.model.po.book.ThemePO">
        <id property="id" column="id"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, content, create_time, update_time, create_by, update_by, enable
    </sql>

    <select id="selectByIds" parameterType="java.util.List" resultMap="ThemeResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM theme
        WHERE enable = true
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectList" resultType="com.unipus.digitalbook.model.po.book.ThemePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM theme
        WHERE
            enable = 1
        <if test="id!= null">
            AND id = #{id}
        </if>
        <if test="name != null and name != ''">
            AND name like CONCAT('%', #{name}, '%')
        </if>
    </select>

    <insert id="insert" parameterType="com.unipus.digitalbook.model.po.book.ThemePO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO theme (id, name, content, create_by, update_by, enable)
        VALUES (
        #{id},
        #{name},
        #{content, jdbcType=VARCHAR},
        #{createBy},
        #{updateBy},
        #{enable})
    </insert>

    <update id="update" parameterType="com.unipus.digitalbook.model.po.book.ThemePO">
        UPDATE theme
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="content != null">
                content = #{content, jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
        </set>
        WHERE
        id = #{id}
    </update>
</mapper>