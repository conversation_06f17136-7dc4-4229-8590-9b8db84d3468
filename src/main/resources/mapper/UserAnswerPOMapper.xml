<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserAnswerPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.UserAnswerPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizQuestionId" column="biz_question_id" jdbcType="CHAR"/>
        <result property="questionVersionNumber" column="question_version_number" jdbcType="CHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="envPartition" column="env_partition" jdbcType="VARCHAR"/>
        <result property="openId" column="open_id" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="DECIMAL"/>
        <result property="answer" column="answer" jdbcType="VARCHAR"/>
        <result property="bizAnswerId" column="biz_answer_id" jdbcType="CHAR"/>
        <result property="evaluation" column="evaluation" jdbcType="VARCHAR"/>
        <result property="batchId" column="batch_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="pass" column="pass" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_question_id,question_version_number,tenant_id,env_partition,open_id,score,answer,
        biz_answer_id,evaluation,batch_id,status,`pass`,create_time,update_time,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_answer
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_answer
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.UserAnswerPO" useGeneratedKeys="true">
        insert into user_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizQuestionId != null">biz_question_id,</if>
            <if test="questionVersionNumber != null">question_version_number,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="envPartition != null">env_partition,</if>
            <if test="openId != null">open_id,</if>
            <if test="score != null">score,</if>
            <if test="answer != null">answer,</if>
            <if test="bizAnswerId != null">biz_answer_id,</if>
            <if test="evaluation != null">evaluation,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="status != null">status,</if>
            <if test="pass != null">`pass`,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bizQuestionId != null">#{bizQuestionId,jdbcType=CHAR},</if>
            <if test="questionVersionNumber != null">#{questionVersionNumber,jdbcType=CHAR},</if>
            <if test="tenantId != null">#{tenantId,jdbcType=BIGINT},</if>
            <if test="envPartition != null">#{env_partition,jdbcType=VARCHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="score != null">#{score,jdbcType=DECIMAL},</if>
            <if test="answer != null">#{answer,jdbcType=VARCHAR},</if>
            <if test="bizAnswerId != null">#{bizAnswerId,jdbcType=CHAR},</if>
            <if test="evaluation != null">#{evaluation,jdbcType=VARCHAR},</if>
            <if test="batchId != null">#{batchId,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="pass != null">#{pass,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.UserAnswerPO">
        update user_answer
        <set>
            <if test="bizQuestionId != null">
                biz_question_id = #{bizQuestionId,jdbcType=CHAR},
            </if>
            <if test="questionVersionNumber != null">
                question_version_number = #{questionVersionNumber,jdbcType=CHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="envPartition != null">
                env_partition = #{envPartition,jdbcType=BIGINT},
            </if>
            <if test="openId != null">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=DECIMAL},
            </if>
            <if test="answer != null">
                answer = #{answer,jdbcType=VARCHAR},
            </if>
            <if test="bizAnswerId != null">
                biz_answer_id = #{bizAnswerId,jdbcType=CHAR},
            </if>
            <if test="evaluation != null">
                evaluation = #{evaluation,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `pass` = #{status,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchPassUserAnswer">
        update user_answer
        set pass = true
        where id in
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT
        ua.id,
        ua.biz_question_id,
        ua.question_version_number,
        ua.tenant_id,
        ua.env_partition,
        ua.open_id,
        ua.score,
        ua.answer,
        ua.biz_answer_id,
        ua.evaluation,
        ua.batch_id,
        ua.status,
        ua.pass,
        ua.create_time,
        ua.update_time,
        ua.enable
        FROM
            user_answer ua
        JOIN (
            SELECT
                batch_id,
                MAX(create_time) as latest_time
            FROM
                user_answer
            WHERE
                enable = true
                AND `pass` = true
                AND biz_question_id in
                <foreach collection="bizQuestionIdList" item="bizQuestionId" open="(" separator="," close=")">
                    #{bizQuestionId}
                </foreach>
                <if test="questionVersionNumber != null and questionVersionNumber != ''">
                    AND question_version_number = #{questionVersionNumber}
                </if>
                <if test="openId != null and openId != ''">
                    AND open_id = #{openId}
                </if>
                <if test="tenantId != null">
                    AND tenant_id = #{tenantId}
                </if>
            GROUP BY
                batch_id
            ORDER BY
                latest_time DESC
            LIMIT 1
        ) latest ON ua.batch_id = latest.batch_id
        WHERE
            ua.enable = true
            AND ua.pass  = true
            AND ua.biz_question_id in
            <foreach collection="bizQuestionIdList" item="bizQuestionId" open="(" separator="," close=")">
                #{bizQuestionId}
            </foreach>
            <if test="questionVersionNumber != null and questionVersionNumber != ''">
                AND question_version_number = #{questionVersionNumber}
            </if>
            <if test="openId != null and openId != ''">
                AND open_id = #{openId}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
        ORDER BY
            ua.create_time DESC
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_answer
        WHERE enable = true
        AND open_id = #{openId}
        AND tenant_id = #{tenantId}
        AND biz_question_id in
        <foreach collection="bizQuestionIdList" item="bizQuestionId" open="(" separator="," close=")">
            #{bizQuestionId}
        </foreach>
        <if test="questionVersionNumber != null and questionVersionNumber != ''">
            AND question_version_number = #{questionVersionNumber}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        user_answer
        WHERE
        id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND enable = true
    </select>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true" keyColumn="id">
        INSERT INTO user_answer
        (biz_question_id, question_version_number, tenant_id, env_partition, open_id, score, answer,
        biz_answer_id, evaluation, batch_id, status)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.bizQuestionId,jdbcType=CHAR}, #{item.questionVersionNumber,jdbcType=CHAR}, #{item.tenantId,jdbcType=BIGINT},
        #{item.envPartition,jdbcType=VARCHAR},
        #{item.openId,jdbcType=VARCHAR}, #{item.score,jdbcType=DECIMAL}, #{item.answer,jdbcType=VARCHAR},
        #{item.bizAnswerId,jdbcType=CHAR}, #{item.evaluation,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate">
        INSERT INTO user_answer
        (biz_question_id, question_version_number, tenant_id,env_partition, open_id, score, answer,
        biz_answer_id, evaluation, batch_id, status, pass, enable)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.bizQuestionId,jdbcType=CHAR}, #{item.questionVersionNumber,jdbcType=CHAR}, #{item.tenantId,jdbcType=BIGINT},
            #{item.envPartition,jdbcType=VARCHAR},
            #{item.openId,jdbcType=VARCHAR}, #{item.score,jdbcType=DECIMAL}, #{item.answer,jdbcType=VARCHAR},
            #{item.bizAnswerId,jdbcType=CHAR}, #{item.evaluation,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}, #{item.pass,jdbcType=BIT}, #{item.enable,jdbcType=BIT} )
        </foreach>
        ON DUPLICATE KEY UPDATE
        id = LAST_INSERT_ID(id),
        score = VALUES(score),
        answer = VALUES(answer),
        biz_answer_id = VALUES(biz_answer_id),
        evaluation = VALUES(evaluation),
        env_partition = VALUES(env_partition),
        status = VALUES(status),
        pass = VALUES(pass),
        enable = VALUES(enable),
        update_time = NOW()
    </insert>

    <select id="selectByBatchId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        user_answer
        WHERE
        batch_id = #{batchId}
        AND enable = true
    </select>
</mapper>
