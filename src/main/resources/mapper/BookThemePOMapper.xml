<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookThemePOMapper">

    <resultMap id="BookThemeResultMap" type="com.unipus.digitalbook.model.po.book.BookThemePO">
        <id property="id" column="id"/>
        <result property="bookId" column="book_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="themeId" column="theme_id"/>
        <result property="overrideDefault" column="override_default" jdbcType="BIT"/>
        <result property="overrideCustom" column="override_custom" jdbcType="BIT"/>
        <result property="editorTemplate" column="editor_template" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, book_id, chapter_id, theme_id, override_default,
        override_custom, editor_template, create_by, update_by, enable
    </sql>

    <insert id="insert" parameterType="com.unipus.digitalbook.model.po.book.BookThemePO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO book_theme (
            book_id, chapter_id, theme_id, override_default,override_custom, editor_template, create_by, update_by, enable
        )
        VALUES (
        #{bookId},
        #{chapterId},
        #{themeId},
        #{overrideDefault},
        #{overrideCustom},
        #{editorTemplate},
        #{createBy},
        #{updateBy},
        #{enable})
    </insert>

    <update id="update" parameterType="com.unipus.digitalbook.model.po.book.BookThemePO">
        UPDATE book_theme
        <set>
            <if test="bookId != null">
                book_id = #{bookId},
            </if>
            <if test="chapterId != null">
                chapter_id = #{chapterId},
            </if>
            <if test="themeId != null">
                theme_id = #{themeId},
            </if>
            <if test="overrideDefault != null">
                override_default = #{overrideDefault},
            </if>
            <if test="overrideCustom != null">
                override_custom = #{overrideCustom},
            </if>
            <if test="editorTemplate != null">
                editor_template = #{editorTemplate},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
        </set>
        WHERE
        id = #{id}
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BookThemeResultMap">
        SELECT <include refid="Base_Column_List" /> FROM book_theme WHERE id = #{id}
    </select>

    <select id="selectByBookIdAndChapterId" resultMap="BookThemeResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM book_theme
        WHERE
            enable = true
        <if test="bookId != null and bookId != ''">
            AND book_id = #{bookId}
        </if>
        <if test="chapterId != null and chapterId != ''">
            AND chapter_id = #{chapterId}
        </if>
    </select>

</mapper>