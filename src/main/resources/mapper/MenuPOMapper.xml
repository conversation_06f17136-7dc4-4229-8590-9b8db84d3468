<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.MenuPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.menu.MenuPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="enable" column="enable" jdbcType="BOOLEAN"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,`name`,
        `path`,`position`,status,
        enable,create_by,update_by,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from menu
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectAll" resultType="com.unipus.digitalbook.model.po.menu.MenuPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from menu
        where enable = true
    </select>
    <select id="findMaxPositionByParentId" resultType="java.lang.Integer">
        select COALESCE(max(position), 0) from menu where parent_id = #{parentId,jdbcType=BIGINT} and enable = true
    </select>
    <select id="selectSubByParentId" resultType="com.unipus.digitalbook.model.po.menu.MenuPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from menu
        where parent_id = #{parentId,jdbcType=BIGINT} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from menu
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.menu.MenuPO" useGeneratedKeys="true">
        insert into menu
        ( id,parent_id,name
        ,path,position,status
        ,enable,create_by,update_by,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{parentId,jdbcType=BIGINT},#{name,jdbcType=VARCHAR}
        ,#{path,jdbcType=VARCHAR},#{sposition,jdbcType=INTEGER},#{status,jdbcType=TINYINT}
        ,#{enable,jdbcType=BOOLEAN},
        #{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.menu.MenuPO" useGeneratedKeys="true">
        insert into menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="parentId != null">parent_id,</if>
                <if test="name != null">name,</if>
                <if test="path != null">path,</if>
                <if test="position != null">position,</if>
                <if test="status != null">status,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="parentId != null">#{parentId,jdbcType=BIGINT},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="path != null">#{path,jdbcType=VARCHAR},</if>
                <if test="position != null">#{position,jdbcType=INTEGER},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="enable != null">#{enable,jdbcType=BOOLEAN},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.menu.MenuPO">
        update menu
        <set>
                <if test="parentId != null">
                    parent_id = #{parentId,jdbcType=BIGINT},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="path != null">
                    path = #{path,jdbcType=VARCHAR},
                </if>
                <if test="position != null">
                    `position` = #{position,jdbcType=INTEGER},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BOOLEAN},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.menu.MenuPO">
        update menu
        set 
            parent_id =  #{parentId,jdbcType=BIGINT},
            name =  #{name,jdbcType=VARCHAR},
            path =  #{path,jdbcType=VARCHAR},
            position =  #{position,jdbcType=INTEGER},
            status =  #{status,jdbcType=TINYINT},
            enable =  #{enable,jdbcType=BOOLEAN},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="logicalDelete">
        update menu
        set enable = false, update_by = #{updateBy}
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updatePosition">
        update menu
        set position = #{position,jdbcType=INTEGER}
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchUpdatePosition">
        UPDATE menu
        SET
        position = CASE
        <foreach collection="list" item="menu">
            WHEN id = #{menu.id} THEN #{menu.position}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="menu" open="(" separator="," close=")">
            #{menu.id}
        </foreach>
    </update>
</mapper>
