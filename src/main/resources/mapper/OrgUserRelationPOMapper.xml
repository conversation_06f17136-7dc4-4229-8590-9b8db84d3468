<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.OrgUserRelationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.OrgUserRelationPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="join_time" jdbcType="TIMESTAMP" property="joinTime" />
    <result column="leave_time" jdbcType="TIMESTAMP" property="leaveTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, user_id, org_id, join_time, leave_time, `status`, remark, create_time, update_time, 
    create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from org_user_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from org_user_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.OrgUserRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into org_user_relation (user_id, org_id, join_time, 
      leave_time, `status`, remark, 
      create_time, update_time, create_by, 
      update_by, `enable`)
    values (#{userId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{joinTime,jdbcType=TIMESTAMP}, 
      #{leaveTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.OrgUserRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into org_user_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="joinTime != null">
        join_time,
      </if>
      <if test="leaveTime != null">
        leave_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="joinTime != null">
        #{joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveTime != null">
        #{leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.OrgUserRelationPO">
    <!--
    @mbg.generated
    -->
    update org_user_relation
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="joinTime != null">
        join_time = #{joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveTime != null">
        leave_time = #{leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.OrgUserRelationPO">
    <!--
    @mbg.generated
    -->
    update org_user_relation
    set user_id = #{userId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      join_time = #{joinTime,jdbcType=TIMESTAMP},
      leave_time = #{leaveTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="userExistInOrg" resultType="int">
    select count(*)
    from org_user_relation
    where enable = true
    and user_id = #{userId,jdbcType=BIGINT}
    and org_id = #{orgId,jdbcType=BIGINT}
    and `status` = 1
    </select>
  <select id="getOrgIdByUserId" resultType="java.lang.Long">
    select org_id
    from org_user_relation
    where enable = true
    and user_id = #{userId,jdbcType=BIGINT}
    and `status` = 1
  </select>

  <update id="batchDeleteRelationsByUserIdListAndOrgId">
    update org_user_relation
    set enable = false,
    update_by = #{opUserId}
    where user_id in
    <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
      #{userId}
    </foreach>
    and org_id = #{orgId}
    </update>

  <select id="queryValidOrgUserIdsByUserIds" parameterType="java.util.List" resultType="java.lang.Long">
    SELECT DISTINCT our.user_id
    FROM org_user_relation our
    INNER JOIN organization org ON our.org_id = org.id
    WHERE our.enable = 1
    AND org.enable = 1
<!--    AND org.status = 1-->
    AND our.user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
  </select>
</mapper>