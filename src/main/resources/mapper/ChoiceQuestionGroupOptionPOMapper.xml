<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChoiceQuestionGroupOptionPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="optionId" column="option_id" jdbcType="CHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,option_id,
        name,content,create_time,
        update_time,create_by,update_by,
        enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from choice_question_group_option
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByGroupId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from choice_question_group_option
        where group_id = #{groupId,jdbcType=BIGINT} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from choice_question_group_option
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO" useGeneratedKeys="true">
        insert into choice_question_group_option
        ( id,group_id,option_id
        ,name,content,create_time
        ,update_time,create_by,update_by
        ,enable)
        values (#{id,jdbcType=BIGINT},#{groupId,jdbcType=BIGINT},#{optionId,jdbcType=CHAR}
        ,#{name,jdbcType=VARCHAR},#{content,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT}
        ,#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO" useGeneratedKeys="true">
        insert into choice_question_group_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="groupId != null">group_id,</if>
                <if test="optionId != null">option_id,</if>
                <if test="name != null">name,</if>
                <if test="content != null">content,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
                <if test="optionId != null">#{optionId,jdbcType=CHAR},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="content != null">#{content,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO choice_question_group_option (
        group_id, option_id, name, content, create_by, update_by
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.groupId},
            #{item.optionId},
            #{item.name},
            #{item.content},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = COALESCE(VALUES(name), choice_question_option.name),
        content = COALESCE(VALUES(content), choice_question_option.content),
        update_by = COALESCE(VALUES(update_by), choice_question_option.update_by),
        enable = COALESCE(VALUES(enable), choice_question_option.enable);
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO">
        update choice_question_group_option
        <set>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="optionId != null">
                    option_id = #{optionId,jdbcType=CHAR},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO">
        update choice_question_group_option
        set 
            group_id =  #{groupId,jdbcType=BIGINT},
            option_id =  #{optionId,jdbcType=CHAR},
            name =  #{name,jdbcType=VARCHAR},
            content =  #{content,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
