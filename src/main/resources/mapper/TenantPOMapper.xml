<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unipus.digitalbook.dao.TenantPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.tenant.TenantPO">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_by" property="createBy" jdbcType="BIGINT" />
        <result column="update_by" property="updateBy" jdbcType="BIGINT" />
        <result column="enable" property="enable" jdbcType="BIT" />
    </resultMap>

    <sql id="Base_Column_List">
        id, name, create_time, update_time, create_by, update_by, enable
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tenant
        WHERE id = #{id}
    </select>

</mapper>