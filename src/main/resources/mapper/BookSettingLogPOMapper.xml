<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookSettingLogPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookSettingLogPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookId" column="book_id" jdbcType="CHAR"/>
        <result property="assistantOpened" column="assistant_opened" jdbcType="BIT"/>
        <result property="assistantUpdated" column="assistant_updated" jdbcType="BIT"/>
        <result property="knowledgeUpdated" column="knowledge_updated" jdbcType="BIT"/>
        <result property="wordPracticeUpdated" column="wordpractice_updated" jdbcType="BIT"/>
        <result property="assistantVersion" column="assistant_version" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <resultMap id="SearchResultMap" type="com.unipus.digitalbook.model.entity.book.BookSettingLog">
        <result property="assistantOpened" column="assistant_opened"/>
        <result property="assistantUpdated" column="assistant_updated"/>
        <result property="knowledgeUpdated" column="knowledge_updated"/>
        <result property="wordPracticeUpdated" column="wordpractice_updated" jdbcType="BIT"/>
        <result property="publishTime" column="create_time"/>
        <result property="operator" column="name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_id,assistant_opened,assistant_updated,knowledge_updated,assistant_version,create_time,update_time,create_by,update_by,enable
    </sql>

    <select id="selectByBookId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_setting_log
        where book_id = #{bookId,jdbcType=CHAR}
    </select>

    <insert id="saveLog" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookSettingLogPO" useGeneratedKeys="true">
        INSERT INTO book_setting_log (book_id,assistant_opened,assistant_updated,knowledge_updated,assistant_version,wordpractice_updated,create_time,update_time,create_by,update_by,enable)
        VALUES (#{bookId,jdbcType=CHAR}, #{assistantOpened,jdbcType=BIT}, #{assistantUpdated,jdbcType=BIT},
        #{knowledgeUpdated,jdbcType=BIT}, #{assistantVersion,jdbcType=VARCHAR}, #{wordPracticeUpdated,jdbcType=BIT},#{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
    </insert>

    <sql id="SearchLogConditions">
        FROM book_setting_log log
        left join user_info user on log.create_by = user.id
        WHERE log.book_id = #{bookId}
        <if test="name != null and name != ''">
            AND user.name LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="beginTime != null">
            AND log.create_time &gt;= #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND log.create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <select id="searchLog" resultMap="SearchResultMap">
        SELECT log.assistant_opened,log.assistant_updated,log.knowledge_updated,log.wordpractice_updated,log.create_time,user.name
        <include refid="SearchLogConditions"/>
        ORDER BY log.create_time DESC, log.id DESC
        <if test="page != null">
            LIMIT #{page.offset}, #{page.limit}
        </if>
    </select>

    <select id="searchLogCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        <include refid="SearchLogConditions"/>
    </select>

    <select id="selectLastLogIdByBookId" resultType="long">
        select bsl.id from book_setting_log bsl where bsl.book_id =  #{bookId} order by bsl.create_time desc,bsl.update_time desc limit 1
    </select>

    <update id="updateWordPracticeUpdated">
        update book_setting_log set wordpractice_updated = 1 where id = #{id}
    </update>

</mapper>