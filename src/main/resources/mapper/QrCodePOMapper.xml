<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.QrCodePOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.qrcode.QrCodePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl" />
    <result column="qr_code_size" jdbcType="VARCHAR" property="qrCodeSize" />
    <result column="real_book_location" jdbcType="VARCHAR" property="realBookLocation" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="book_inner_url" jdbcType="VARCHAR" property="bookInnerUrl" />
    <result column="link_verification_status" jdbcType="BIT" property="linkVerificationStatus" />
    <result column="link_verification_time" jdbcType="TIMESTAMP" property="linkVerificationTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, qr_code_url, qr_code_size, real_book_location, book_id, book_inner_url, link_verification_status, 
    link_verification_time, remarks, create_time, update_time, create_by, update_by, 
    `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qr_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.qrcode.QrCodePO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into qr_code (id, qr_code_url, qr_code_size, real_book_location,
      book_id, book_inner_url, link_verification_status, 
      link_verification_time, remarks, create_time, 
      update_time, create_by, update_by, 
      `enable`)
    values (#{id,jdbcType=BIGINT}, #{qrCodeUrl,jdbcType=VARCHAR}, #{qrCodeSize,jdbcType=VARCHAR}, #{realBookLocation,jdbcType=VARCHAR},
      #{bookId,jdbcType=CHAR}, #{bookInnerUrl,jdbcType=VARCHAR}, #{linkVerificationStatus,jdbcType=BIT}, 
      #{linkVerificationTime,jdbcType=TIMESTAMP}, #{remarks,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.qrcode.QrCodePO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into qr_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url,
      </if>
      <if test="qrCodeSize != null">
        qr_code_size,
      </if>
      <if test="realBookLocation != null">
        real_book_location,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="bookInnerUrl != null">
        book_inner_url,
      </if>
      <if test="linkVerificationStatus != null">
        link_verification_status,
      </if>
      <if test="linkVerificationTime != null">
        link_verification_time,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="qrCodeUrl != null">
        #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeSize != null">
        #{qrCodeSize,jdbcType=VARCHAR},
      </if>
      <if test="realBookLocation != null">
        #{realBookLocation,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="bookInnerUrl != null">
        #{bookInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="linkVerificationStatus != null">
        #{linkVerificationStatus,jdbcType=BIT},
      </if>
      <if test="linkVerificationTime != null">
        #{linkVerificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.qrcode.QrCodePO">
    update qr_code
    <set>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeSize != null">
        qr_code_size = #{qrCodeSize,jdbcType=VARCHAR},
      </if>
      <if test="realBookLocation != null">
        real_book_location = #{realBookLocation,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="bookInnerUrl != null">
        book_inner_url = #{bookInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="linkVerificationStatus != null">
        link_verification_status = #{linkVerificationStatus,jdbcType=BIT},
      </if>
      <if test="linkVerificationTime != null">
        link_verification_time = #{linkVerificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.qrcode.QrCodePO">
    update qr_code
    set qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      qr_code_size = #{qrCodeSize,jdbcType=VARCHAR},
      real_book_location = #{realBookLocation,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=CHAR},
      book_inner_url = #{bookInnerUrl,jdbcType=VARCHAR},
      link_verification_status = #{linkVerificationStatus,jdbcType=BIT},
      link_verification_time = #{linkVerificationTime,jdbcType=TIMESTAMP},
      remarks = #{remarks,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into qr_code (id, qr_code_url, qr_code_size, real_book_location,
    book_id, book_inner_url, link_verification_status,
    link_verification_time, remarks, create_time,
    update_time, create_by, update_by,
    `enable`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.id,jdbcType=BIGINT}, #{item.qrCodeUrl,jdbcType=VARCHAR}, #{item.qrCodeSize,jdbcType=VARCHAR}, #{item.realBookLocation,jdbcType=VARCHAR},
    #{item.bookId,jdbcType=CHAR}, #{item.bookInnerUrl,jdbcType=VARCHAR}, #{item.linkVerificationStatus,jdbcType=BIT},
    #{item.linkVerificationTime,jdbcType=TIMESTAMP}, #{item.remarks,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
    #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT},
    #{item.enable,jdbcType=BIT})
    </foreach>
  </insert>

  <sql id="SearchConditions">
    FROM qr_code qc
    WHERE qc.enable = true
    <if test="bookIds != null and bookIds.size() != 0">
      AND qc.book_id IN
      <foreach collection="bookIds" open="(" close=")" item="bookId" separator=",">
        #{bookId}
      </foreach>
    </if>
  </sql>

  <select id="searchCount" resultType="java.lang.Integer">
    SELECT COUNT(*)
    <include refid="SearchConditions"/>
  </select>

  <select id="search" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    <include refid="SearchConditions"/>
    ORDER BY qc.create_time DESC, qc.id DESC
    <if test="page != null">
      LIMIT #{page.offset}, #{page.limit}
    </if>
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM qr_code
    WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND enable = true
  </select>
</mapper>