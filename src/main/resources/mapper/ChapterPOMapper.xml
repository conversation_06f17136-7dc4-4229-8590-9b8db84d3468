<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChapterPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.chapter.ChapterPO">
            <id property="id" column="id" jdbcType="CHAR"/>
            <result property="bookId" column="book_id" jdbcType="CHAR"/>
            <result property="chapterNumber" column="chapter_number" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_id,chapter_number,
        name,create_time,update_time,
        create_by,update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from chapter
        where  id = #{id,jdbcType=CHAR} 
    </select>
    <select id="selectByBookId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from chapter
        where book_id = #{bookId,jdbcType=CHAR} and enable = 1
    </select>
    <select id="selectIdsByBookId" parameterType="java.lang.String" resultType="java.lang.String">
        select
        id
        from chapter
        where book_id = #{bookId,jdbcType=CHAR} and enable = 1
    </select>
    <select id="selectByBookIdAndNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from chapter
        where
        book_id = #{bookId,jdbcType=CHAR}
        and chapter_number = #{chapterNumber,jdbcType=INTEGER}
        and enable = true
    </select>
    <select id="selectIdsByChapterIds" resultType="java.lang.String">
        select
        id
        from chapter
        where id in
        <foreach collection="chapterIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=CHAR}
        </foreach>
        and enable = true
    </select>
    <select id="existsChapter" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT 1 FROM chapter WHERE id in
        <foreach collection="chapterIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=CHAR}
        </foreach>
        and enable = true)
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from chapter
        where  id = #{id,jdbcType=CHAR} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterPO" useGeneratedKeys="true">
        insert into chapter
        ( id,book_id,chapter_number
        ,name,create_time,update_time
        ,create_by,update_by,enable
        )
        values (#{id,jdbcType=CHAR},#{bookId,jdbcType=CHAR},#{chapterNumber,jdbcType=INTEGER}
        ,#{name,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT},#{enable,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterPO" useGeneratedKeys="true">
        insert into chapter
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="bookId != null">book_id,</if>
                <if test="chapterNumber != null">chapter_number,</if>
                <if test="name != null">name,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=CHAR},</if>
                <if test="bookId != null">#{bookId,jdbcType=CHAR},</if>
                <if test="chapterNumber != null">#{chapterNumber,jdbcType=INTEGER},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterPO">
        update chapter
        <set>
                <if test="bookId != null">
                    book_id = #{bookId,jdbcType=CHAR},
                </if>
                <if test="chapterNumber != null">
                    chapter_number = #{chapterNumber,jdbcType=INTEGER},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=CHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterPO">
        update chapter
        set 
            book_id =  #{bookId,jdbcType=CHAR},
            chapter_number =  #{chapterNumber,jdbcType=INTEGER},
            name =  #{name,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=CHAR} 
    </update>


    <update id="batchUpdateChapterNumber" parameterType="java.util.List">
        UPDATE chapter
        SET chapter_number =
        CASE id
        <foreach collection="chapterList" item="chapter">
            WHEN #{chapter.id} THEN #{chapter.chapterNumber}
        </foreach>
        END,
        update_time = NOW(),
        update_by = #{chapterList[0].updateBy}
        WHERE id IN
        <foreach collection="chapterList" item="chapter" open="(" separator="," close=")">
            #{chapter.id}
        </foreach>
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from chapter
        where enable = true
    </select>
</mapper>
