<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookOperationLogPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookOperationLogPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="operation_user_id" jdbcType="BIGINT" property="operationUserId" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operation_content" jdbcType="VARCHAR" property="operationContent" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>

  <resultMap id="OperationLogResultMap" type="com.unipus.digitalbook.model.po.book.SearchOperationLogPO">
    <id property="id" column="id"/>
    <result property="bookId" column="book_id"/>
    <result property="operationUserId" column="operation_user_id"/>
    <result property="operationTime" column="operation_time"/>
    <result property="operationContent" column="operation_content"/>
    <result property="operationType" column="operation_type"/>
    <result property="operationUserName" column="operation_user_name"/>
  </resultMap>

  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, book_id, operation_user_id, operation_time, operation_content, operation_type,
    create_time, update_time, create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select
    <include refid="Base_Column_List" />
    from book_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from book_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.book.BookOperationLogPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_operation_log (book_id, operation_user_id, operation_time,
    operation_content, operation_type, create_time,
    update_time, create_by, update_by,
    `enable`)
    values (#{bookId,jdbcType=CHAR}, #{operationUserId,jdbcType=BIGINT}, #{operationTime,jdbcType=TIMESTAMP},
    #{operationContent,jdbcType=VARCHAR}, #{operationType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
    #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT},
    #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.book.BookOperationLogPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        book_id,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="operationContent != null">
        operation_content,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=BIGINT},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationContent != null">
        #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookOperationLogPO">
    <!--
    @mbg.generated
    -->
    update book_operation_log
    <set>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=BIGINT},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationContent != null">
        operation_content = #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookOperationLogPO">
    <!--
    @mbg.generated
    -->
    update book_operation_log
    set book_id = #{bookId,jdbcType=CHAR},
    operation_user_id = #{operationUserId,jdbcType=BIGINT},
    operation_time = #{operationTime,jdbcType=TIMESTAMP},
    operation_content = #{operationContent,jdbcType=VARCHAR},
    operation_type = #{operationType,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=BIGINT},
    update_by = #{updateBy,jdbcType=BIGINT},
    `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="SearchOperationLogConditions">
    FROM book_operation_log bol
    left join user_info ui on bol.operation_user_id = ui.id AND ui.enable = true
    WHERE bol.enable = true
    <if test="bookId != null">
      AND bol.book_id = #{bookId}
    </if>
    <if test="userName != null and userName != ''">
      AND ui.name LIKE CONCAT('%',#{userName},'%')
    </if>
    <if test="operationStartTime != null">
      AND bol.operation_time &gt;= #{operationStartTime}
    </if>
    <if test="operationEndTime != null">
      AND bol.operation_time &lt;= #{operationEndTime}
    </if>
  </sql>

  <select id="searchOperationLogList" resultMap="OperationLogResultMap">
    SELECT bol.id,
           bol.book_id,
           bol.operation_user_id,
           bol.operation_time,
           bol.operation_content,
           bol.operation_type,
           ui.name as operation_user_name
    <include refid="SearchOperationLogConditions"/>
    ORDER BY bol.operation_time DESC, bol.id DESC
    <if test="page != null">
      LIMIT #{page.offset}, #{page.limit}
    </if>
    </select>

  <select id="searchOperationLogListCount" resultType="java.lang.Long">
    SELECT COUNT(*)
    <include refid="SearchOperationLogConditions"/>
  </select>
</mapper>