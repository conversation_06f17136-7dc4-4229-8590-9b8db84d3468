<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookPO">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="editorId" column="editor_id" jdbcType="BIGINT"/>
        <result column="status" jdbcType="INTEGER" property="status" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,org_id,editor_id, `status`,create_time,update_time,
        create_by,update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book
        where  id = #{id,jdbcType=CHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from book
        where  id = #{id,jdbcType=CHAR}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookPO" useGeneratedKeys="true">
        insert into book (id,org_id,editor_id, `status`,create_time,update_time
        ,create_by,update_by,enable
        )
        values (#{id,jdbcType=CHAR},#{orgId,jdbcType=BIGINT},#{editorId,jdbcType=BIGINT},#{status,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT},#{enable,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookPO" useGeneratedKeys="true">
        insert into book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orgId != null">org_id,</if>
            <if test="editorId != null">editor_id,</if>
            <if test="status != null">`status`,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=CHAR},</if>
            <if test="orgId != null">#{orgId,jdbcType=BIGINT},</if>
            <if test="editorId != null">#{editorId,jdbcType=BIGINT},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookPO">
        update book
        <set>
                <if test="orgId != null">
                    org_id = #{orgId,jdbcType=BIGINT},
                </if>
                <if test="editorId != null">
                    editor_id = #{editorId,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    `status` = #{status,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=CHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookPO">
        update book
        set
            org_id =  #{orgId,jdbcType=BIGINT},
            editor_id =  #{editorId,jdbcType=BIGINT},
            `status` = #{status,jdbcType=INTEGER},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=CHAR}
    </update>
    <update id="updateTimeByBookId">
        update book
        set update_time = NOW()
        where id = #{bookId,jdbcType=CHAR}
    </update>

    <select id="selectByOrgIdAndIds" resultType="com.unipus.digitalbook.model.po.book.BookPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book
        where enable = true
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="orgId != null">
            and org_id = #{orgId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectEditorId" resultType="java.lang.Long">
        select editor_id from book where id = #{bookId,jdbcType=CHAR} and enable = true
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book
        where enable = true
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectBookIdsByOrgIds" resultType="string">
        SELECT id
        FROM book
        WHERE org_id IN
        <foreach collection="orgIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND enable = true
        ORDER BY create_time DESC
    </select>

    <select id="selectIdsByTenantId" resultType="string">
        SELECT id
        FROM book
        WHERE tenant_id = #{tenantId}
          AND enable = true
        ORDER BY create_time DESC
    </select>

</mapper>
