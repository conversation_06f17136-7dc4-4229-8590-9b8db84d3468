<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ResourcePermissionPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.ResourcePermissionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="resourceId" column="resource_id" jdbcType="CHAR"/>
            <result property="resourceType" column="resource_type" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="permission" column="permission" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,resource_type,user_id,permission,
        create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectUserByResourceId" parameterType="java.lang.Long" resultType="com.unipus.digitalbook.model.po.book.ResourceUserPO">
        select
            u.id as userId,
            u.name as userName,
            u.cell_phone as mobile,
            rp.permission as permission
        from resource_permission rp
        inner join user_info u on rp.user_id = u.id and u.enable = 1
        where
            rp.enable = 1
        and rp.resource_id = #{resourceId,jdbcType=VARCHAR}
        <if test="resourceType!= null and resourceType!= ''">
            and rp.resource_type = #{resourceType,jdbcType=INTEGER}
        </if>
        <if test="permission!= null and permission!= ''">
            and permission &amp; #{permission,jdbcType=TINYINT} != 0
        </if>
        <if test="keyword != null and keyword != ''">
            and (u.name like concat('%',#{keyword},'%') or u.cell_phone like concat('%',#{keyword},'%'))
        </if>
        <!-- 根据权限更新时间升序排列 -->
        order by rp.update_time
    </select>

    <select id="selectPermissions" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from resource_permission
        where
            enable = 1
        <foreach collection="paramList" item="param" open="and (" separator="or" close=")">
            (  1 = 1
            <if test="param.resourceId != null and param.resourceId != ''">
                and resource_id = #{param.resourceId,jdbcType=CHAR}
            </if>
            <if test="param.resourceType != null and param.resourceType != ''">
                and resource_type = #{param.resourceType,jdbcType=CHAR}
            </if>
            <if test="param.userId != null and param.userId != ''">
                and user_id = #{param.userId,jdbcType=BIGINT}
            </if>
            <if test="param.permission != null and param.permission != ''">
                and permission &amp; #{param.permission,jdbcType=TINYINT} != 0
            </if>
            )
        </foreach>
    </select>

    <select id="selectPermission" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from resource_permission
        where
            enable = 1
        <if test="resourceId != null and resourceId != ''">
            and resource_id = #{resourceId,jdbcType=CHAR}
        </if>
        <if test="resourceType != null and resourceType != ''">
            and resource_type = #{resourceType,jdbcType=CHAR}
        </if>
        <if test="userId != null and userId != ''">
            and user_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="permission != null and permission != ''">
            and permission &amp; #{permission,jdbcType=TINYINT} != 0
        </if>
    </select>

    <update id="removePermission">
        update resource_permission set
            update_by = #{updateBy,jdbcType=BIGINT},
            enable = 0
        where
            enable = 1
        and resource_id = #{resourceId,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <insert id="batchInsertOrUpdatePermissions"  parameterType="java.util.List">
        insert into resource_permission (
            resource_id,resource_type,user_id,permission, create_by, update_by,enable
        ) values
        <foreach collection="list" item="item" separator="," >
            (
            #{item.resourceId,jdbcType=CHAR},
            #{item.resourceType,jdbcType=TINYINT},
            #{item.userId,jdbcType=BIGINT},
            #{item.permission,jdbcType=TINYINT},
            #{item.createBy,jdbcType=BIGINT},
            #{item.updateBy,jdbcType=BIGINT},
            #{item.enable,jdbcType=TINYINT}
            )
        </foreach>
        on duplicate key update
        id = LAST_INSERT_ID(id),
        resource_id = values(resource_id),
        resource_type = values(resource_type),
        user_id = values(user_id),
        permission = values(permission),
        update_by = values(update_by),
        enable = values(enable)
    </insert>
</mapper>
