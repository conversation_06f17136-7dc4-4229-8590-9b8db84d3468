<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserInfoPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.UserInfoPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sso_id" jdbcType="VARCHAR" property="ssoId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="active_time" jdbcType="TIMESTAMP" property="activeTime" />
    <result column="status" jdbcType="INTEGER" property="status"/>
  </resultMap>

  <resultMap id="UserAndOrgPOResultMap" type="com.unipus.digitalbook.model.po.user.SearchUserAndOrgPO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="sso_id" jdbcType="VARCHAR" property="ssoId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone"/>
    <result column="email" jdbcType="VARCHAR" property="email"/>
    <result column="gender" jdbcType="INTEGER" property="gender"/>
    <result column="desc" jdbcType="VARCHAR" property="desc"/>
    <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="active_time" jdbcType="TIMESTAMP" property="activeTime"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="org_id" jdbcType="BIGINT" property="orgId"/>
  </resultMap>

  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, sso_id, `name`, cell_phone, email, gender, `desc`, avatar_url, `enable`, create_time, 
    update_time, create_by, update_by, active_time, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from user_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from user_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.UserInfoPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_info (sso_id, `name`, cell_phone, 
      email, gender, `desc`, 
      avatar_url, `enable`, create_time, 
      update_time, create_by, update_by, 
      active_time, `status`)
    values (#{ssoId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{cellPhone,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, #{desc,jdbcType=VARCHAR}, 
      #{avatarUrl,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{activeTime,jdbcType=TIMESTAMP},#{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.UserInfoPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ssoId != null">
        sso_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="cellPhone != null">
        cell_phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="avatarUrl != null">
        avatar_url,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="activeTime != null">
        active_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ssoId != null">
        #{ssoId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="cellPhone != null">
        #{cellPhone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="avatarUrl != null">
        #{avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="activeTime != null">
        #{activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.UserInfoPO">
    <!--
    @mbg.generated
    -->
    update user_info
    <set>
      <if test="ssoId != null">
        sso_id = #{ssoId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="cellPhone != null">
        cell_phone = #{cellPhone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="avatarUrl != null">
        avatar_url = #{avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="activeTime != null">
        active_time = #{activeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.UserInfoPO">
    <!--
    @mbg.generated
    -->
    update user_info
    set sso_id = #{ssoId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      cell_phone = #{cellPhone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      `desc` = #{desc,jdbcType=VARCHAR},
      avatar_url = #{avatarUrl,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      active_time = #{activeTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySsoId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, sso_id, `name`, cell_phone, email, gender, `desc`, avatar_url,user_info.active_time
    from user_info
    where sso_id = #{ssoId}
    and enable = true
    limit 1
  </select>

  <update id="activateUser">
    update user_info
    set active_time = now(),
        sso_id      = #{ssoId,jdbcType=VARCHAR},
        status      = 1
    where id = #{id,jdbcType=BIGINT}
      and enable = true
      and active_time is null
  </update>

  <select id="selectByCellPhone" resultMap="BaseResultMap">
    select id, sso_id, `name`, cell_phone, email, gender, `desc`, avatar_url,user_info.active_time
    from user_info
    where cell_phone = #{cellPhone,jdbcType=VARCHAR}
    and enable =true
  </select>

  <sql id="SearchUserAndOrgConditions">
    FROM user_info ui
    INNER JOIN org_user_relation our ON ui.id = our.user_id AND our.status = 1 AND our.enable = true
    left join  `organization` o ON our.org_id = o.id
    WHERE ui.enable = true
    and o.enable = true
    and o.status = 1
    <if test="orgId != null and orgId != 0">
      AND our.org_id = #{orgId}
    </if>
    <if test="cellPhone != null and cellPhone != ''">
      <choose>
        <when test="cellPhone.length() == 11">
          AND ui.cell_phone = #{cellPhone}
        </when>
        <otherwise>
          AND ui.cell_phone LIKE CONCAT('%', #{cellPhone}, '%')
        </otherwise>
      </choose>
    </if>
    <if test="userName != null and userName != ''">
      AND ui.name LIKE CONCAT('%',#{userName},'%')
    </if>
    <if test="status != null">
      AND ui.status = #{status}
    </if>
  </sql>

  <select id="searchUserAndOrg" resultMap="UserAndOrgPOResultMap">
    SELECT ui.id,
           ui.sso_id,
           ui.`name`,
           ui.cell_phone,
           ui.email,
           ui.gender,
           ui.`desc`,
           ui.avatar_url,
           ui.active_time,
           ui.create_time,
           ui.status,
           our.org_id
    <include refid="SearchUserAndOrgConditions"/>
    ORDER BY ui.create_time DESC, ui.id DESC
    <if test="page != null">
      LIMIT #{page.offset}, #{page.limit}
    </if>
  </select>

  <select id="searchUserAndOrgCount" resultType="java.lang.Long">
    SELECT COUNT(*)
    <include refid="SearchUserAndOrgConditions"/>
  </select>


  <sql id="queryConditions">
    <!-- 基础关联查询条件 -->
    FROM user_info u
    LEFT JOIN org_user_relation our ON u.id = our.user_id
    <where>
      <!-- 手机号查询条件 -->
      <if test="cellPhone != null and cellPhone != ''">
        <choose>
          <when test="cellPhone.length() == 11">
            AND u.cell_phone = #{cellPhone}
          </when>
          <otherwise>
            AND u.cell_phone LIKE CONCAT('%', #{cellPhone}, '%')
          </otherwise>
        </choose>
      </if>
      <!-- 用户状态查询条件 -->
      <if test="status != null">
        AND u.status = #{status}
      </if>
      <!-- 机构ID查询条件 -->
      <if test="orgId != null">
        AND our.org_id = #{orgId}
        AND our.enable = 1
        AND our.status = 1
      </if>
      <!-- 默认只查询启用的用户 -->
      AND u.enable = 1
    </where>
  </sql>

  <!-- 排序SQL片段 -->
  <sql id="sortConditions">
    <choose>
      <when test="page != null and page.sortInfo != null and page.sortInfo.property != null">
        ORDER BY
        <choose>
          <when test="page.sortInfo.property == 'createTime'">u.create_time</when>
          <when test="page.sortInfo.property == 'name'">u.name</when>
          <when test="page.sortInfo.property == 'cellPhone'">u.cell_phone</when>
          <when test="page.sortInfo.property == 'status'">u.status</when>
          <otherwise>u.create_time</otherwise>
        </choose>
        <choose>
          <when test="page.sortInfo.direction != null and page.sortInfo.direction.toUpperCase() == 'ASC'">ASC</when>
          <otherwise>DESC</otherwise>
        </choose>
      </when>
      <otherwise>
        ORDER BY u.create_time DESC
      </otherwise>
    </choose>
  </sql>

  <!-- 查询满足条件的总数 -->
  <select id="countByPhoneAndOrgAndStatus" resultType="long">
    SELECT COUNT(DISTINCT u.id)
    <include refid="queryConditions"/>
  </select>

  <select id="searchByPhoneAndOrgAndStatus" resultMap="BaseResultMap">
    select u.id as id,
    sso_id,
    `name`,
    cell_phone,
    email,
    gender,
    `desc`,
    avatar_url,
    active_time,
    u.create_time as create_time,
    u.status as status
    <include refid="queryConditions"/>
    <include refid="sortConditions"/>
    <if test="page != null">
      LIMIT #{page.limit} OFFSET #{page.offset}
    </if>
  </select>
  <select id="bathSelectUser" resultType="com.unipus.digitalbook.model.po.UserInfoPO" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from user_info
    where id in
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    and enable = true
  </select>
    <select id="selectAll" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from user_info
    </select>

    <update id="batchDelete">
      UPDATE user_info
      SET enable = false,
      update_by = #{opUserId}
      WHERE id IN
      <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
        #{userId}
      </foreach>
      AND enable = true
      and status = 0
    </update>

  <select id="selectValidUsersByOrgAndKeyword" resultType="com.unipus.digitalbook.model.po.UserInfoPO">
      SELECT
        u.id AS id,
        u.name AS name,
        u.cell_phone AS cellPhone
      FROM user_info u
      INNER JOIN org_user_relation our ON our.user_id = u.id AND our.status = 1 AND our.enable = 1
      INNER JOIN organization o ON o.id = our.org_id AND o.status = 1 AND o.enable = 1
      <if test="orgId!= null and orgId!= ''">
        AND o.id = #{orgId}
      </if>
      WHERE
          u.status = 1
      AND u.enable = 1
    <if test="keyword != null and keyword != ''">
      AND (u.name LIKE CONCAT('%',#{keyword},'%') OR u.cell_phone = #{keyword})
    </if>
      GROUP BY u.id
      ORDER BY CONVERT(u.name USING GBK)
    <if test="page != null">
      LIMIT #{page.limit} OFFSET #{page.offset}
    </if>
  </select>

  <select id="checkUserValidityWithUserIdsAndOrgId" resultType="java.lang.Long">
    SELECT u.id
    FROM user_info u
    INNER JOIN org_user_relation our ON our.user_id = u.id AND our.status = 1 AND our.enable = 1
    INNER JOIN organization o ON o.id = our.org_id AND o.status = 1 AND o.enable = 1
    <if test="orgId!= null and orgId!= ''">
      AND o.id = #{orgId}
    </if>
    WHERE
    u.status = 1
    AND u.enable = 1
    <foreach collection="userIds" item="userId" open="and (" separator="or" close=")">
      user_id = #{userId,jdbcType=BIGINT}
    </foreach>
    GROUP BY u.id
  </select>
</mapper>