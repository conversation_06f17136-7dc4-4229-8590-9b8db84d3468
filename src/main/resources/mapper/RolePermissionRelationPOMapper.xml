<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.RolePermissionRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="permissionReference" column="permission_reference" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="enable" column="enable" jdbcType="TINYINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,role_id,permission_reference,
        type,enable,create_by,update_by,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role_permission_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByRoleIdsAndType" resultType="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role_permission_relation
        where type = #{type,jdbcType=INTEGER} and enable = true
        and role_id in
        <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from role_permission_relation
        where type = #{type,jdbcType=INTEGER} and enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from role_permission_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO" useGeneratedKeys="true">
        insert into role_permission_relation
        ( id,role_id,permission_reference
        ,type,enable,create_by,update_by
        ,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{roleId,jdbcType=BIGINT},#{permissionReference,jdbcType=VARCHAR}
        ,#{type,jdbcType=INTEGER},#{enable,jdbcType=TINYINT}
        ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO" useGeneratedKeys="true">
        insert into role_permission_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="roleId != null">role_id,</if>
                <if test="permissionReference != null">permission_reference,</if>
                <if test="type != null">type,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="roleId != null">#{roleId,jdbcType=BIGINT},</if>
                <if test="permissionReference != null">#{permissionReference,jdbcType=VARCHAR},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="enable != null">#{enable,jdbcType=TINYINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO">
        update role_permission_relation
        <set>
                <if test="roleId != null">
                    role_id = #{roleId,jdbcType=BIGINT},
                </if>
                <if test="permissionReference != null">
                    permission_reference = #{permissionReference,jdbcType=VARCHAR},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=TINYINT},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.role.RolePermissionRelationPO">
        update role_permission_relation
        set 
            role_id =  #{roleId,jdbcType=BIGINT},
            permission_reference =  #{permissionReference,jdbcType=VARCHAR},
            type =  #{type,jdbcType=INTEGER},
            enable =  #{enable,jdbcType=TINYINT},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO role_permission_relation (role_id, permission_reference, type, enable, create_by, update_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.permissionReference}, #{item.type}, #{item.enable}, #{item.createBy}, #{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enable = VALUES(enable),
        update_by = VALUES(update_by),
        create_by = CASE WHEN enable = true THEN VALUES(create_by) ELSE create_by END,
        create_time = CASE WHEN enable = true THEN NOW() ELSE create_time END
    </insert>
</mapper>
