<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.SeriesPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.SeriesPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,enable,
        create_by,update_by,create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from series
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByIds" resultType="com.unipus.digitalbook.model.po.SeriesPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from series
        where  id in
        <foreach item="item" collection="seriesIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and enable = true
    </select>
    <select id="selectAll" resultType="com.unipus.digitalbook.model.po.SeriesPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from series
        where enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from series
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.SeriesPO" useGeneratedKeys="true">
        insert into series
        ( id,name,enable
        ,create_by,update_by,create_time
        ,update_time)
        values (#{id,jdbcType=BIGINT},#{name,jdbcType=VARCHAR},#{enable,jdbcType=BIT}
        ,#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.SeriesPO" useGeneratedKeys="true">
        insert into series
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="name != null">name,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.SeriesPO">
        update series
        <set>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.SeriesPO">
        update series
        set 
            name =  #{name,jdbcType=VARCHAR},
            enable =  #{enable,jdbcType=BIT},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
