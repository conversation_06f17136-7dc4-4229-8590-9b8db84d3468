<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.OrgRoleRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.role.OrgRoleRelationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="TINYINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,org_id,role_id,
        enable,update_by,create_by,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from org_role_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectRoleIdByOrgId" resultType="java.lang.Long">
        select role_id from org_role_relation where org_id = #{orgId,jdbcType=BIGINT} and enable = true
    </select>
    <select id="selectIdByRoleId" resultType="java.lang.Long">
        select id from org_role_relation where role_id = #{roleId,jdbcType=BIGINT} and enable = true
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from org_role_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.OrgRoleRelationPO" useGeneratedKeys="true">
        insert into org_role_relation
        ( id,org_id,role_id
        ,enable,create_by,update_by,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{orgId,jdbcType=BIGINT},#{roleId,jdbcType=BIGINT}
        ,#{enable,jdbcType=TINYINT},#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO org_role_relation (org_id, role_id, enable, create_by, update_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orgId}, #{item.roleId}, #{item.enable}, #{item.createBy}, #{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enable = VALUES(enable),
        update_by = VALUES(update_by),
        create_by = CASE WHEN enable = true THEN VALUES(create_by) ELSE create_by END,
        create_time = CASE WHEN enable = true THEN NOW() ELSE create_time END
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.role.OrgRoleRelationPO" useGeneratedKeys="true">
        insert into org_role_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orgId != null">org_id,</if>
                <if test="roleId != null">role_id,</if>
                <if test="enable != null">enable,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="orgId != null">#{orgId,jdbcType=BIGINT},</if>
                <if test="roleId != null">#{roleId,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=TINYINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.role.OrgRoleRelationPO">
        update org_role_relation
        <set>
                <if test="orgId != null">
                    org_id = #{orgId,jdbcType=BIGINT},
                </if>
                <if test="roleId != null">
                    role_id = #{roleId,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=TINYINT},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.role.OrgRoleRelationPO">
        update org_role_relation
        set 
            org_id =  #{orgId,jdbcType=BIGINT},
            role_id =  #{roleId,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=TINYINT},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="bathLogicalDelete">
        update org_role_relation
        set enable = false, update_by = #{updateBy}
        where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
