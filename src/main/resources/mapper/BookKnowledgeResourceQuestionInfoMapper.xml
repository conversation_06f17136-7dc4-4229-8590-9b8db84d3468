<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookKnowledgeResourceQuestionInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="resourceId" column="resource_id" jdbcType="BIGINT"/>
        <result property="resourceDetailId" column="resource_detail_id" jdbcType="BIGINT"/>
        <result property="questionId" column="question_id" jdbcType="VARCHAR"/>
        <result property="questionIndex" column="question_index" jdbcType="VARCHAR"/>
        <result property="questionKey" column="question_key" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,resource_detail_id,question_id,
        question_index,question_key,enable,
        create_time,update_time,create_by,
        update_by
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_question_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByResourceIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_question_info
        where resource_id in
        <foreach collection="resourceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from book_knowledge_resource_question_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_question_info
        ( id, resource_id, resource_detail_id, question_id
        , question_index, question_key, enable
        , create_time, update_time, create_by
        , update_by)
        values ( #{id,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, #{resourceDetailId,jdbcType=BIGINT}
               , #{questionId,jdbcType=INTEGER}
               , #{questionIndex,jdbcType=INTEGER}, #{questionKey,jdbcType=INTEGER}, #{enable,jdbcType=BIT}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}
               , #{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_question_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="resourceDetailId != null">resource_detail_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="questionIndex != null">question_index,</if>
            <if test="questionKey != null">question_key,</if>
            <if test="enable != null">enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="resourceId != null">#{resourceId,jdbcType=BIGINT},</if>
            <if test="resourceDetailId != null">#{resourceDetailId,jdbcType=BIGINT},</if>
            <if test="questionId != null">#{questionId,jdbcType=INTEGER},</if>
            <if test="questionIndex != null">#{questionIndex,jdbcType=INTEGER},</if>
            <if test="questionKey != null">#{questionKey,jdbcType=INTEGER},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO">
        update book_knowledge_resource_question_info
        <set>
            <if test="resourceId != null">
                resource_id = #{resourceId,jdbcType=BIGINT},
            </if>
            <if test="resourceDetailId != null">
                resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            </if>
            <if test="questionId != null">
                question_id = #{questionId,jdbcType=INTEGER},
            </if>
            <if test="questionIndex != null">
                question_index = #{questionIndex,jdbcType=INTEGER},
            </if>
            <if test="questionKey != null">
                question_key = #{questionKey,jdbcType=INTEGER},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByResourceIdSelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO">
        update book_knowledge_resource_question_info
        <set>
            <if test="resourceDetailId != null">
                resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            </if>
            <if test="questionId != null">
                question_id = #{questionId,jdbcType=INTEGER},
            </if>
            <if test="questionIndex != null">
                question_index = #{questionIndex,jdbcType=INTEGER},
            </if>
            <if test="questionKey != null">
                question_key = #{questionKey,jdbcType=INTEGER},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where resource_id = #{resourceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceQuestionInfoPO">
        update book_knowledge_resource_question_info
        set resource_id        = #{resourceId,jdbcType=BIGINT},
            resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            question_id        = #{questionId,jdbcType=INTEGER},
            question_index     = #{questionIndex,jdbcType=INTEGER},
            question_key       = #{questionKey,jdbcType=INTEGER},
            enable             = #{enable,jdbcType=BIT},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            create_by          = #{createBy,jdbcType=BIGINT},
            update_by          = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
