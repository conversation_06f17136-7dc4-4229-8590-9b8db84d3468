<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookPublishPackagePOMapper">
    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="book_version_id" jdbcType="BIGINT" property="bookVersionId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        <result column="publish_package" jdbcType="LONGVARCHAR" property="publishPackage"
                typeHandler="com.unipus.digitalbook.conf.mybatis.type.handler.BookPublishItemListTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
        @mbg.generated
        -->
        id, book_version_id, create_time, update_time, create_by, update_by, `enable`
    </sql>
    <sql id="Blob_Column_List">
        <!--
        @mbg.generated
        -->
        publish_package
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!--
        @mbg.generated
        -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from book_publish_package
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByBookVersionId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from book_publish_package
        where book_version_id = #{versionId,jdbcType=BIGINT}
        limit 1
    </select>
    <select id="selectByBookIdAndVersionNumber" resultMap="ResultMapWithBLOBs">
        select
            bp.*
        from book_publish_package bp
        join book_version bv on bp.book_version_id = bv.id
            where bv.book_id = #{bookId,jdbcType=VARCHAR}
            and bv.version_num = #{versionNumber,jdbcType=VARCHAR}
            and bp.enable = 1
        limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
        @mbg.generated
        -->
        delete from book_publish_package
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into book_publish_package (book_version_id, create_time, update_time,
        create_by, update_by, `enable`,
        publish_package)
        values (#{bookVersionId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT},
        #{publishPackage,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into book_publish_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookVersionId != null">
                book_version_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
            <if test="publishPackage != null">
                publish_package,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookVersionId != null">
                #{bookVersionId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BIT},
            </if>
            <if test="publishPackage != null">
                #{publishPackage,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        update book_publish_package
        <set>
            <if test="bookVersionId != null">
                book_version_id = #{bookVersionId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
            <if test="publishPackage != null">
                publish_package = #{publishPackage,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        update book_publish_package
        set book_version_id = #{bookVersionId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=BIGINT},
        update_by = #{updateBy,jdbcType=BIGINT},
        `enable` = #{enable,jdbcType=BIT},
        publish_package = #{publishPackage,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.BookPublishPackagePO">
        <!--
        @mbg.generated
        -->
        update book_publish_package
        set book_version_id = #{bookVersionId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=BIGINT},
        update_by = #{updateBy,jdbcType=BIGINT},
        `enable` = #{enable,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>