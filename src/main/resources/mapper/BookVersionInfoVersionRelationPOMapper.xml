<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookVersionInfoVersionRelationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_version_id" jdbcType="BIGINT" property="bookVersionId" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, book_version_id, info_id, content_type, create_time, update_time,
    create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from book_version_info_version_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByBookVersionId"
          parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from book_version_info_version_relation
    where book_version_id = #{bookVersionId,jdbcType=BIGINT}
    and enable = true
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from book_version_info_version_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_version_info_version_relation (book_version_id, info_id, content_type, 
      create_time, update_time,
      create_by, update_by, `enable`
      )
    values (#{bookVersionId,jdbcType=BIGINT}, #{infoId,jdbcType=BIGINT}, #{contentType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into book_version_info_version_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookVersionId != null">
        book_version_id,
      </if>
      <if test="infoId != null">
        info_id,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookVersionId != null">
        #{bookVersionId,jdbcType=BIGINT},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=BIGINT},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO">
    <!--
    @mbg.generated
    -->
    update book_version_info_version_relation
    <set>
      <if test="bookVersionId != null">
        book_version_id = #{bookVersionId,jdbcType=BIGINT},
      </if>
      <if test="infoId != null">
        info_id = #{infoId,jdbcType=BIGINT},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO">
    <!--
    @mbg.generated
    -->
    update book_version_info_version_relation
    set book_version_id = #{bookVersionId,jdbcType=BIGINT},
      info_id = #{infoId,jdbcType=BIGINT},
      content_type = #{contentType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>