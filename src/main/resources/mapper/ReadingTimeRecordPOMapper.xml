<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ReadingTimeRecordPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.learn.ReadingTimeRecordPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="open_id" jdbcType="CHAR" property="openId" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="chapter_id" jdbcType="CHAR" property="chapterId" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, open_id, book_id, chapter_id, start_time, end_time, tenant_id, create_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from reading_time_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeRecordPO">
    insert into reading_time_record (open_id, book_id, chapter_id,
    start_time, end_time, tenant_id, create_time)
    values (#{openId,jdbcType=bookId,jdbcType=CHAR}, #{bookId,jdbcType=CHAR}, #{chapterId,jdbcType=CHAR},
    #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
    #{createTime,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeRecordPO">
    insert into reading_time_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="openId != null">
        open_id,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="chapterId != null">
        chapter_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="openId != null">
        #{openId,jdbcType=CHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null">
        #{chapterId,jdbcType=CHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeRecordPO">
    update reading_time_record
    <set>
      <if test="openId != null">
        open_id = #{openId,jdbcType=CHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null">
        chapter_id = #{chapterId,jdbcType=CHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.learn.ReadingTimeRecordPO">
    update reading_time_record
    set open_id = #{openId,jdbcType=CHAR},
        book_id = #{bookId,jdbcType=CHAR},
        chapter_id = #{chapterId,jdbcType=CHAR},
        start_time = #{startTime,jdbcType=BIGINT},
        end_time = #{endTime,jdbcType=BIGINT},
        tenant_id = #{tenantId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 根据用户ID和书籍ID查询阅读记录 -->
  <select id="selectByopenIdAndBookId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_record
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
    order by create_time desc
  </select>
  
  <!-- 根据用户ID、书籍ID和章节ID查询阅读记录 -->
  <select id="selectByopenIdAndBookIdAndChapterId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_record
    where open_id = #{openId,jdbcType=CHAR}
    and book_id = #{bookId,jdbcType=CHAR}
    and chapter_id = #{chapterId,jdbcType=CHAR}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
    order by create_time desc
  </select>
  
  <!-- 更新结束时间 -->
  <update id="updateEndTime">
    update reading_time_record
    set end_time = #{endTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 查询未结束的阅读记录 -->
  <select id="selectUnfinishedRecords" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from reading_time_record
    where end_time is null
    <if test="openId != null">
      and open_id = #{openId,jdbcType=CHAR}
    </if>
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=BIGINT}
    </if>
    order by create_time desc
  </select>
</mapper>