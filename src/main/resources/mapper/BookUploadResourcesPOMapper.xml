<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookUploadResourcesPOMapper">
    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.BookUploadResourcesPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="book_id" jdbcType="VARCHAR" property="bookId"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="storage_path" jdbcType="VARCHAR" property="storagePath"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="mime_type" jdbcType="VARCHAR" property="mimeType"/>
        <result column="file_size_bytes" jdbcType="BIGINT" property="fileSizeBytes"/>
        <result column="uploader_id" jdbcType="BIGINT" property="uploaderId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
    </resultMap>
    <sql id="Base_Column_List">

        id, book_id, resource_name, storage_path, resource_type, mime_type, file_size_bytes,
        uploader_id, `status`, create_time, update_time, create_by, update_by, `enable`
    </sql>
    <sql id="Blob_Column_List">

        description
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">

        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from book_upload_resources
        where id = #{id,jdbcType=BIGINT}
    </select>
  <select id="selectByBookId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from book_upload_resources
    where book_id = #{bookId,jdbcType=VARCHAR}
    and `enable` = true
    order by create_time desc

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">

        delete from book_upload_resources
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <update id="deleteBookFileListByIdList">
        update book_upload_resources
        set `enable` = 0
        where id in
        <foreach item="item" collection="idList" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <insert id="insert" parameterType="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into book_upload_resources (book_id, resource_name, storage_path,
        resource_type, mime_type, file_size_bytes,
        uploader_id, `status`, create_time,
        update_time, create_by, update_by,
        `enable`, description)
        values (#{bookId,jdbcType=VARCHAR}, #{resourceName,jdbcType=VARCHAR}, #{storagePath,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR}, #{mimeType,jdbcType=VARCHAR}, #{fileSizeBytes,jdbcType=BIGINT},
        #{uploaderId,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT},
        #{enable,jdbcType=BIT}, #{description,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into book_upload_resources
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">
                book_id,
            </if>
            <if test="resourceName != null">
                resource_name,
            </if>
            <if test="storagePath != null">
                storage_path,
            </if>
            <if test="resourceType != null">
                resource_type,
            </if>
            <if test="mimeType != null">
                mime_type,
            </if>
            <if test="fileSizeBytes != null">
                file_size_bytes,
            </if>
            <if test="uploaderId != null">
                uploader_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
            <if test="description != null">
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">
                #{bookId,jdbcType=VARCHAR},
            </if>
            <if test="resourceName != null">
                #{resourceName,jdbcType=VARCHAR},
            </if>
            <if test="storagePath != null">
                #{storagePath,jdbcType=VARCHAR},
            </if>
            <if test="resourceType != null">
                #{resourceType,jdbcType=VARCHAR},
            </if>
            <if test="mimeType != null">
                #{mimeType,jdbcType=VARCHAR},
            </if>
            <if test="fileSizeBytes != null">
                #{fileSizeBytes,jdbcType=BIGINT},
            </if>
            <if test="uploaderId != null">
                #{uploaderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BIT},
            </if>
            <if test="description != null">
                #{description,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        update book_upload_resources
        <set>
            <if test="bookId != null">
                book_id = #{bookId,jdbcType=VARCHAR},
            </if>
            <if test="resourceName != null">
                resource_name = #{resourceName,jdbcType=VARCHAR},
            </if>
            <if test="storagePath != null">
                storage_path = #{storagePath,jdbcType=VARCHAR},
            </if>
            <if test="resourceType != null">
                resource_type = #{resourceType,jdbcType=VARCHAR},
            </if>
            <if test="mimeType != null">
                mime_type = #{mimeType,jdbcType=VARCHAR},
            </if>
            <if test="fileSizeBytes != null">
                file_size_bytes = #{fileSizeBytes,jdbcType=BIGINT},
            </if>
            <if test="uploaderId != null">
                uploader_id = #{uploaderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        update book_upload_resources
        set book_id = #{bookId,jdbcType=VARCHAR},
        resource_name = #{resourceName,jdbcType=VARCHAR},
        storage_path = #{storagePath,jdbcType=VARCHAR},
        resource_type = #{resourceType,jdbcType=VARCHAR},
        mime_type = #{mimeType,jdbcType=VARCHAR},
        file_size_bytes = #{fileSizeBytes,jdbcType=BIGINT},
        uploader_id = #{uploaderId,jdbcType=BIGINT},
        `status` = #{status,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=BIGINT},
        update_by = #{updateBy,jdbcType=BIGINT},
        `enable` = #{enable,jdbcType=BIT},
        description = #{description,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.BookUploadResourcesPO">

        update book_upload_resources
        set book_id = #{bookId,jdbcType=VARCHAR},
        resource_name = #{resourceName,jdbcType=VARCHAR},
        storage_path = #{storagePath,jdbcType=VARCHAR},
        resource_type = #{resourceType,jdbcType=VARCHAR},
        mime_type = #{mimeType,jdbcType=VARCHAR},
        file_size_bytes = #{fileSizeBytes,jdbcType=BIGINT},
        uploader_id = #{uploaderId,jdbcType=BIGINT},
        `status` = #{status,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=BIGINT},
        update_by = #{updateBy,jdbcType=BIGINT},
        `enable` = #{enable,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>