<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperScoreTemplateRelationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="paper_score_template_id" jdbcType="BIGINT" property="paperScoreTemplateId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, book_id, paper_score_template_id, create_time, update_time, create_by, update_by, 
    enable
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from paper_score_template_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from paper_score_template_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO">
    insert into paper_score_template_relation (id, book_id, paper_score_template_id, 
      create_time, update_time, create_by, 
      update_by, enable)
    values (#{id,jdbcType=BIGINT}, #{bookId,jdbcType=CHAR}, #{paperScoreTemplateId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into paper_score_template_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="paperScoreTemplateId != null">
        paper_score_template_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        enable,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="paperScoreTemplateId != null">
        #{paperScoreTemplateId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO">
    update paper_score_template_relation
    <set>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="paperScoreTemplateId != null">
        paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO">
    update paper_score_template_relation
    set book_id = #{bookId,jdbcType=CHAR},
      paper_score_template_id = #{paperScoreTemplateId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      enable = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectTemplateIdListByBookId" resultType="long">
    select paper_score_template_id from paper_score_template_relation
    where enable = 1
    and book_id = #{bookId,jdbcType=CHAR}
  </select>

  <!-- 批量插入 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO paper_score_template_relation (book_id, paper_score_template_id,
    create_by, update_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bookId,jdbcType=VARCHAR}, #{item.paperScoreTemplateId,jdbcType=BIGINT},
      #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT})
    </foreach>
  </insert>

  <update id="batchDisable">
    update paper_score_template_relation set enable = 0, update_by = #{updateUserId,jdbcType=BIGINT}
    where enable = 1
    and book_id = #{bookId,jdbcType=CHAR}
  </update>
</mapper>