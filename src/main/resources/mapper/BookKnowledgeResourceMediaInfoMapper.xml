<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookKnowledgeResourceMediaInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="resourceId" column="resource_id" jdbcType="BIGINT"/>
        <result property="resourceDetailId" column="resource_detail_id" jdbcType="BIGINT"/>
        <result property="startTime" column="start_time" jdbcType="INTEGER"/>
        <result property="startPictureUrl" column="start_picture_url" jdbcType="VARCHAR"/>
        <result property="multimediaKey" column="multimedia_key" jdbcType="VARCHAR"/>
        <result property="multimediaIndex" column="multimedia_index" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="INTEGER"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,resource_detail_id,start_time,
        start_picture_url,multimedia_key,multimedia_index,
        end_time,enable,create_time,
        update_time,create_by,update_by
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_media_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByResourceIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_media_info
        where resource_id in
        <foreach collection="resourceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from book_knowledge_resource_media_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_media_info
        ( id, resource_id, resource_detail_id, start_time
        , start_picture_url, multimedia_key, multimedia_index
        , end_time, enable, create_time
        , update_time, create_by, update_by)
        values ( #{id,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, #{resourceDetailId,jdbcType=BIGINT}
               , #{startTime,jdbcType=INTEGER}
               , #{startPictureUrl,jdbcType=VARCHAR}, #{multimediaKey,jdbcType=VARCHAR}
               , #{multimediaIndex,jdbcType=VARCHAR}
               , #{endTime,jdbcType=INTEGER}, #{enable,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_media_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="resourceDetailId != null">resource_detail_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="startPictureUrl != null">start_picture_url,</if>
            <if test="multimediaKey != null">multimedia_key,</if>
            <if test="multimediaIndex != null">multimedia_index,</if>
            <if test="endTime != null">end_time,</if>
            <if test="enable != null">enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="resourceId != null">#{resourceId,jdbcType=BIGINT},</if>
            <if test="resourceDetailId != null">#{resourceDetailId,jdbcType=BIGINT},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="startPictureUrl != null">#{startPictureUrl,jdbcType=VARCHAR},</if>
            <if test="multimediaKey != null">#{multimediaKey,jdbcType=VARCHAR},</if>
            <if test="multimediaIndex != null">#{multimediaIndex,jdbcType=VARCHAR},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO">
        update book_knowledge_resource_media_info
        <set>
            <if test="resourceId != null">
                resource_id = #{resourceId,jdbcType=BIGINT},
            </if>
            <if test="resourceDetailId != null">
                resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="startPictureUrl != null">
                start_picture_url = #{startPictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="multimediaKey != null">
                multimedia_key = #{multimediaKey,jdbcType=VARCHAR},
            </if>
            <if test="multimediaIndex != null">
                multimedia_index = #{multimediaIndex,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=INTEGER},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByResourceIdSelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO">
        update book_knowledge_resource_media_info
        <set>
            <if test="resourceDetailId != null">
                resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="startPictureUrl != null">
                start_picture_url = #{startPictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="multimediaKey != null">
                multimedia_key = #{multimediaKey,jdbcType=VARCHAR},
            </if>
            <if test="multimediaIndex != null">
                multimedia_index = #{multimediaIndex,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=INTEGER},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where resource_id = #{resourceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO">
        update book_knowledge_resource_media_info
        set resource_id        = #{resourceId,jdbcType=BIGINT},
            resource_detail_id = #{resourceDetailId,jdbcType=BIGINT},
            start_time         = #{startTime,jdbcType=INTEGER},
            start_picture_url  = #{startPictureUrl,jdbcType=VARCHAR},
            multimedia_key     = #{multimediaKey,jdbcType=VARCHAR},
            multimedia_index   = #{multimediaIndex,jdbcType=VARCHAR},
            end_time           = #{endTime,jdbcType=INTEGER},
            enable             = #{enable,jdbcType=BIT},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            create_by          = #{createBy,jdbcType=BIGINT},
            update_by          = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
