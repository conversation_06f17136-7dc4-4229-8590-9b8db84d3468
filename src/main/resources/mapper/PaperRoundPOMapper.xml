<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperRoundPOMapper">

    <resultMap id="BaseResultMap" type="PaperRoundPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="score_batch_id" property="scoreBatchId" jdbcType="VARCHAR"/>
        <result column="user_score" property="userScore" jdbcType="DECIMAL"/>
        <result column="standard_score" property="standardScore" jdbcType="DECIMAL"/>
        <result column="question_count" property="questionCount" jdbcType="INTEGER"/>
        <result column="correct_count" property="correctCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, score_batch_id, user_score, standard_score, question_count, correct_count,
        create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- Insert or update a record -->
    <insert id="insertOrUpdate" parameterType="PaperRoundPO">
        INSERT INTO paper_round (
            id,
            score_batch_id,
            user_score,
            standard_score,
            question_count,
            correct_count,
            create_by,
            update_by,
            enable
        ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{scoreBatchId,jdbcType=VARCHAR},
            #{userScore,jdbcType=DECIMAL},
            #{standardScore,jdbcType=DECIMAL},
            #{questionCount,jdbcType=INTEGER},
            #{correctCount,jdbcType=INTEGER},
            #{createBy,jdbcType=VARCHAR},
            #{updateBy,jdbcType=VARCHAR},
            #{enable,jdbcType=BIT}
        )
        ON DUPLICATE KEY UPDATE
             score_batch_id = VALUES(score_batch_id),
             user_score = VALUES(user_score),
             standard_score = VALUES(standard_score),
             question_count = VALUES(question_count),
             correct_count = VALUES(correct_count),
             update_by = VALUES(update_by),
             enable = VALUES(enable)
    </insert>

    <!-- Select by score batch ID -->
    <select id="selectByScoreBatchIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM paper_round
        WHERE score_batch_id IN
        <foreach collection="scoreBatchIds" item="scoreBatchId" open="(" separator="," close=")">
            #{scoreBatchId}
        </foreach>
        AND enable = 1
    </select>

    <!-- 根据试卷实例ID取得实例信息 -->
    <select id="getPaperInstanceById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM paper_round
        WHERE enable = 1 AND id = #{instanceId}
    </select>

    <!-- 根据试卷id、版本号、用户id、租户id查询最新试卷实例 -->
    <select id="getLatestPaperInstance" resultType="PaperRoundPO">
        SELECT
            pr.id AS id,
            pr.score_batch_id AS scoreBatchId,
            pr.user_score AS userScore,
            pr.create_time AS createTime
        FROM paper_round AS pr
        LEFT JOIN paper_score_batch AS psb ON pr.score_batch_id = psb.id AND pr.enable = 1
        WHERE psb.enable = 1
        <if test="paperId != null">
            AND psb.paper_id = #{paperId,jdbcType=VARCHAR}
        </if>
        <if test="versionNumber != null">
            AND psb.paper_version_number = #{versionNumber,jdbcType=VARCHAR}
        </if>
        <if test="openId != null">
            AND psb.open_id = #{openId,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null">
            AND psb.tenant_id = #{tenantId,jdbcType=BIGINT}
        </if>
        <if test="status != null">
            AND psb.status = #{status,jdbcType=INTEGER}
        </if>
        ORDER BY psb.create_time DESC, pr.create_time DESC
        LIMIT 1
    </select>

    <!-- 根据试卷成绩批次ID查询试卷实例列表 -->
    <select id="getLatestPaperInstanceByScoreBatchId" resultMap="BaseResultMap">
        SELECT
           <include refid="Base_Column_List"/>
        FROM paper_round AS pr
        WHERE pr.enable = 1 AND pr.score_batch_id = #{scoreBatchId,jdbcType=VARCHAR}
        ORDER BY pr.create_time DESC
    </select>

</mapper>