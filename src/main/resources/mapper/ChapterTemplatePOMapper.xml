<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChapterTemplatePOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="from_chapter_id" jdbcType="VARCHAR" property="fromChapterId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="preview_image_url" jdbcType="VARCHAR" property="previewImageUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, `name`, from_chapter_id, url, preview_image_url, create_time, update_time, create_by, 
    update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    from chapter_template
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
    @mbg.generated
    -->
    delete from chapter_template
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chapter_template (`name`, from_chapter_id, url, 
      preview_image_url, create_time, update_time, 
      create_by, update_by, `enable`
      )
    values (#{name,jdbcType=VARCHAR}, #{fromChapterId,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{previewImageUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{enable,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chapter_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="fromChapterId != null">
        from_chapter_id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="previewImageUrl != null">
        preview_image_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="fromChapterId != null">
        #{fromChapterId,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="previewImageUrl != null">
        #{previewImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO">
    <!--
    @mbg.generated
    -->
    update chapter_template
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="fromChapterId != null">
        from_chapter_id = #{fromChapterId,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="previewImageUrl != null">
        preview_image_url = #{previewImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO">
    <!--
    @mbg.generated
    -->
    update chapter_template
    set `name` = #{name,jdbcType=VARCHAR},
      from_chapter_id = #{fromChapterId,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      preview_image_url = #{previewImageUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getChapterTemplateListByChapterIdList"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from chapter_template
    where
        enable = true
    and
        from_chapter_id in
    <foreach collection="chapterIdList" item="chapterId" open="(" separator="," close=")">
      #{chapterId}
    </foreach>
  </select>
</mapper>