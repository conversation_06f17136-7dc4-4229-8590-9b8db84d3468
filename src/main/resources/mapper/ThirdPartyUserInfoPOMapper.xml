<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ThirdPartyUserInfoPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="openId" column="open_id" />
            <result property="nickName" column="nick_name" />
            <result property="fullName" column="full_name" />
            <result property="userName" column="user_name" />
            <result property="mobile" column="mobile" />
            <result property="email" column="email" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="createBy" column="create_by" />
            <result property="updateBy" column="update_by" />
            <result property="enable" column="enable" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,open_id,nick_name,full_name,user_name,
        mobile,email,create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from third_party_user_info
        where  id = #{id} 
    </select>

    <select id="selectByOpenIdAndTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_party_user_info
        where open_id = #{openId} and tenant_id = #{tenantId}
        limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from third_party_user_info
        where  id = #{id} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO" useGeneratedKeys="true">
        insert into third_party_user_info
        ( id,tenant_id,open_id,nick_name,full_name,user_name,
        mobile,email,create_time,update_time,create_by,
        update_by,enable)
        values (#{id},#{tenantId},#{openId},#{nickName},#{fullName},#{userName},
        #{mobile},#{email},#{createTime},#{updateTime},#{createBy},
        #{updateBy},#{enable})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO" useGeneratedKeys="true">
        insert into third_party_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="openId != null">open_id,</if>
                <if test="nickName != null">nick_name,</if>
                <if test="fullName != null">full_name,</if>
                <if test="userName != null">user_name,</if>
                <if test="mobile != null">mobile,</if>
                <if test="email != null">email,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="tenantId != null">#{tenantId},</if>
                <if test="openId != null">#{openId},</if>
                <if test="nickName != null">#{nickName},</if>
                <if test="fullName != null">#{fullName},</if>
                <if test="userName != null">#{userName},</if>
                <if test="mobile != null">#{mobile},</if>
                <if test="email != null">#{email},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updateTime != null">#{updateTime},</if>
                <if test="createBy != null">#{createBy},</if>
                <if test="updateBy != null">#{updateBy},</if>
                <if test="enable != null">#{enable},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO">
        update third_party_user_info
        <set>
                <if test="tenantId != null">
                    tenant_id = #{tenantId},
                </if>
                <if test="openId != null">
                    open_id = #{openId},
                </if>
                <if test="nickName != null">
                    nick_name = #{nickName},
                </if>
                <if test="fullName != null">
                    full_name = #{fullName},
                </if>
                <if test="userName != null">
                    user_name = #{userName},
                </if>
                <if test="mobile != null">
                    mobile = #{mobile},
                </if>
                <if test="email != null">
                    email = #{email},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy},
                </if>
                <if test="enable != null">
                    enable = #{enable},
                </if>
        </set>
        where   id = #{id} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO">
        update third_party_user_info
        set 
            tenant_id =  #{tenantId},
            open_id =  #{openId},
            nick_name =  #{nickName},
            full_name =  #{fullName},
            user_name =  #{userName},
            mobile =  #{mobile},
            email =  #{email},
            create_time =  #{createTime},
            update_time =  #{updateTime},
            create_by =  #{createBy},
            update_by =  #{updateBy},
            enable =  #{enable}
        where   id = #{id} 
    </update>
</mapper>
