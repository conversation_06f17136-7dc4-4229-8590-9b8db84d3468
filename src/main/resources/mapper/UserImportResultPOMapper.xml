<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserImportResultPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.user.UserImportResultPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_end_time" jdbcType="TIMESTAMP" property="taskEndTime"/>
        <result column="excel_url" jdbcType="VARCHAR" property="excelUrl"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
        <result column="success_num" jdbcType="INTEGER" property="successNum"/>
        <result column="fail_num" jdbcType="INTEGER" property="failNum"/>
        <result column="fail_excel_url" jdbcType="VARCHAR" property="failExcelUrl"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            user_id,
            org_id,
            task_status,
            task_start_time,
            task_end_time,
            excel_url,
            total,
            success_num,
            fail_num,
            fail_excel_url,
            create_time,
            update_time,
            create_by,
            update_by,
            `enable`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_import_result
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_import_result
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.user.UserImportResultPO" useGeneratedKeys="true">
        insert into user_import_result (user_id, org_id, task_status,
        task_start_time, task_end_time, excel_url,
        total, success_num, fail_num,
        fail_excel_url, create_time,
        update_time, create_by, update_by,
        `enable`)
        values (#{userId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{taskStatus,jdbcType=INTEGER},
        #{taskStartTime,jdbcType=TIMESTAMP}, #{taskEndTime,jdbcType=TIMESTAMP}, #{excelUrl,jdbcType=VARCHAR},
        #{total,jdbcType=INTEGER}, #{successNum,jdbcType=INTEGER}, #{failNum,jdbcType=INTEGER},
        #{failExcelUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT},
        #{enable,jdbcType=BIT})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.user.UserImportResultPO" useGeneratedKeys="true">
        insert into user_import_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="taskStartTime != null">
                task_start_time,
            </if>
            <if test="taskEndTime != null">
                task_end_time,
            </if>
            <if test="excelUrl != null and excelUrl != ''">
                excel_url,
            </if>
            <if test="total != null">
                total,
            </if>
            <if test="successNum != null">
                success_num,
            </if>
            <if test="failNum != null">
                fail_num,
            </if>
            <if test="failExcelUrl != null and failExcelUrl != ''">
                fail_excel_url,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=BIGINT},
            </if>
            <if test="taskStatus != null">
                #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="taskStartTime != null">
                #{taskStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskEndTime != null">
                #{taskEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="excelUrl != null and excelUrl != ''">
                #{excelUrl,jdbcType=VARCHAR},
            </if>
            <if test="total != null">
                #{total,jdbcType=INTEGER},
            </if>
            <if test="successNum != null">
                #{successNum,jdbcType=INTEGER},
            </if>
            <if test="failNum != null">
                #{failNum,jdbcType=INTEGER},
            </if>
            <if test="failExcelUrl != null and failExcelUrl != ''">
                #{failExcelUrl,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.user.UserImportResultPO">
        update user_import_result
        set user_id = #{userId,jdbcType=BIGINT},
        org_id = #{orgId,jdbcType=BIGINT},
        task_status = #{taskStatus,jdbcType=INTEGER},
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
        task_end_time = #{taskEndTime,jdbcType=TIMESTAMP},
        excel_url = #{excelUrl,jdbcType=VARCHAR},
        total = #{total,jdbcType=INTEGER},
        success_num = #{successNum,jdbcType=INTEGER},
        fail_num = #{failNum,jdbcType=INTEGER},
        fail_excel_url = #{failExcelUrl,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=BIGINT},
        update_by = #{updateBy,jdbcType=BIGINT},
        `enable` = #{enable,jdbcType=BIT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.user.UserImportResultPO">
        update user_import_result
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=BIGINT},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="taskStartTime != null">
                task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskEndTime != null">
                task_end_time = #{taskEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="excelUrl != null and excelUrl != ''">
                excel_url = #{excelUrl,jdbcType=VARCHAR},
            </if>
            <if test="total != null">
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="successNum != null">
                success_num = #{successNum,jdbcType=INTEGER},
            </if>
            <if test="failNum != null">
                fail_num = #{failNum,jdbcType=INTEGER},
            </if>
            <if test="failExcelUrl != null and failExcelUrl != ''">
                fail_excel_url = #{failExcelUrl,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>