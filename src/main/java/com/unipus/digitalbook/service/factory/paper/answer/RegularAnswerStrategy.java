package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 常规卷答案策略
 */
@Component
@RequiredArgsConstructor
public class RegularAnswerStrategy extends AbstractPaperAnswerStrategy{

    /**
     * 获取试卷类型
     */
    @Override
    public PaperTypeEnum getPaperType() {
        return PaperTypeEnum.REGULAR;
    }

    /**
     * 提交用户作答
     * @param paperInstance 试卷实例
     * @param userAnswerMap 用户答案
     * @param userAccessInfo 用户访问信息
     * @return 用户试卷信息
     */
    @Override
    public UserPaperInfo submit(PaperInstance paperInstance, Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo) {

        // 1.构建用户试卷成绩提交扩展信息
        UserPaperSubmitExtInfo ext = super.buildUserPaperSubmitExtInfo(paperInstance, userAnswerMap, userAccessInfo);

        // 2.提交用户作答，取得作答成绩
        List<UserQuestionScore> userQuestionScores = super.submit(paperInstance.getBigQuestionGroupList(), userAnswerMap);

        // 3.构建用户作答成绩信息实体
        UserPaperScore score = new UserPaperScore(paperInstance.getScoreBatchId(), paperInstance.getInstanceId(),
                PaperTypeEnum.REGULAR,userQuestionScores, null, PaperSubmitStatusEnum.SUBMITTED);

        // 4.保存用户挑战卷成绩信息
        super.saveUserPaperRecord(paperInstance, score);

        // 5.构建试卷作答信息
        UserPaperInfo userPaperInfo = UserPaperInfo.build(paperInstance, score.getUserScore(), PaperSubmitStatusEnum.SUBMITTED);

        // 6.自动提交成绩到UAI
        super.syncUserPaperScore(userPaperInfo, ext);

        return userPaperInfo;
    }

}
