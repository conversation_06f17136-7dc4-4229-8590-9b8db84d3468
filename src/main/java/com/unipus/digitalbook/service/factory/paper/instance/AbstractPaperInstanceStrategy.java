package com.unipus.digitalbook.service.factory.paper.instance;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.PaperExtendPOMapper;
import com.unipus.digitalbook.dao.PaperInstanceRelationPOMapper;
import com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperPO;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.service.PaperInstanceCacheManager;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 试卷实例策略抽象类
 * 实现了策略模式中的抽象策略角色，为不同类型的试卷实例创建提供通用功能。
 * 具体的试卷类型策略类需要继承此抽象类并实现特定的业务逻辑。
 */
@Slf4j
public abstract class AbstractPaperInstanceStrategy implements PaperInstanceStrategy {

    @Resource
    private PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    @Resource
    private PaperInstanceRelationPOMapper paperInstanceRelationPOMapper;
    @Resource
    private PaperExtendPOMapper paperExtendPOMapper;
    @Resource
    private PaperQuestionInstancePOMapper paperQuestionInstancePOMapper;
    @Resource
    private QuestionService questionService;
    @Resource
    private PaperInstanceCacheManager paperInstanceCacheManager;

    /**
     * 初始化预览卷状态
     *
     * @param paper    试卷对象
     * @param openId   用户ID
     * @param tenantId 租户ID
     * @param clearPreviewHistory 是否清除历史作答
     */
    @Override
    public void initPreviewPaperStatus(Paper paper, String openId, Long tenantId, Boolean clearPreviewHistory) {
        log.debug("用户{}，租户{}，试卷{}，版本{}", openId, tenantId, paper.getPaperId(), paper.getVersionNumber());
        if(clearPreviewHistory) {
            // 清除历史作答
            paperScoreBatchPOMapper.deletePreviewInfo(paper.getPaperId(), paper.getVersionNumber(), openId, tenantId);
            paperInstanceRelationPOMapper.deletePreviewInfo(paper.getPaperId(), paper.getVersionNumber(), openId, tenantId);
            log.debug("清除预览历史记录");
        }else{
            log.debug("不清除预览历史记录");
        }
    }

    /**
     * 创建试卷实例
     * @param paper 试卷对象
     * @param finalQuestionList 大题题组列表
     * @param scoreBatchId      成绩提交批次ID
     * @param instanceId        试卷实例ID/轮次ID
     * @param openId            用户ssoID
     * @param tenantId          租户ID
     * @return 试卷实例
     */
    protected PaperInstance createPaperInstance(Paper paper, List<BigQuestionGroup> finalQuestionList,
                         String scoreBatchId, String instanceId, String openId, Long tenantId, UnitTestModeEnum testMode) {
        // 成绩提交批次ID（如果不为空则继续该成绩批次，否则开始新的成绩批次）
        scoreBatchId = StringUtils.hasText(scoreBatchId) ? scoreBatchId : generateScoreBatchId();
        // 试卷实例ID（如果不为空则继续该轮次/该实例，否则开始新的作答轮次/实例）
        instanceId = StringUtils.hasText(instanceId) ? instanceId : generateInstanceId();
        // 生成试卷实例
        PaperInstance paperInstance = new PaperInstance(openId, tenantId, scoreBatchId, instanceId, paper, testMode, finalQuestionList);
        // 暂存用户作答试卷实例，作为提交记录保存的参照
        paperInstanceCacheManager.cachePaperInstance(paperInstance);

        return paperInstance;
    }

    /** 生成试卷成绩批次ID */
    protected String generateScoreBatchId() {
        return IdentifierUtil.getShortUUID();
    }

    /** 生成试卷实例ID */
    protected String generateInstanceId() {
        return IdentifierUtil.getShortUUID();
    }

    /**
     * 获取已经存在的用户试卷实例
     * @param instanceId 试卷实例ID
     * @return 试卷实例
     */
    @Override
    public PaperInstance loadPaperInstance(String instanceId){
        if(!StringUtils.hasText(instanceId)){
            return null;
        }
        // 获取缓存中的试卷实例
        PaperInstance paperInstanceCatch = paperInstanceCacheManager.getPaperInstanceCache(instanceId);
        if(paperInstanceCatch!=null){
            // 返回缓存中的试卷实例
            return paperInstanceCatch;
        }
        // 从数据库中获取试卷实例
        return getPaperInstanceFromDatabase(instanceId);
    }

    /**
     * 从数据库加载试卷实例
     * @param instanceId 试卷实例ID
     * @return 试卷实例
     */
    protected PaperInstance getPaperInstanceFromDatabase(String instanceId){

        // 1.查询试卷成绩批次记录，获取试卷基本信息
        PaperScoreBatchPO paperScoreBatchPO = paperScoreBatchPOMapper.gePaperScoreBatchByInstanceId(instanceId, null);
        if(paperScoreBatchPO == null){
            log.debug("未能查询到试卷成绩批次记录，instanceId: {}", instanceId);
            return null;
        }
        String scoreBatchId = paperScoreBatchPO.getId();
        String paperId = paperScoreBatchPO.getPaperId();
        String versionNumber = paperScoreBatchPO.getPaperVersionNumber();
        Long tenantId = paperScoreBatchPO.getTenantId();
        String openId = paperScoreBatchPO.getOpenId();

        // 2.获取试卷详细信息
        Paper paper = getPaper(paperId, versionNumber);
        if (paper == null){
            return null;
        }

        // 3.查询试卷题目实例记录
        List<PaperQuestionInstancePO> paperQuestionInstancePOS = paperQuestionInstancePOMapper.selectByRoundIds(List.of(instanceId));
        if(CollectionUtils.isEmpty(paperQuestionInstancePOS)){
            log.debug("未能从数据库中查询到试卷题目实例记录，instanceId: {}", instanceId);
            return null;
        }
        // 获取试卷大题ID列表（试卷题目作答记录是以小题维度记录的，需要对大题ID去重）
        List<Long> bigGroupIds = paperQuestionInstancePOS.stream().map(PaperQuestionInstancePO::getQuestionGroupId).distinct().toList();
        // 获取小题ID列表
        Set<String> bizQuestionIds = paperQuestionInstancePOS.stream().map(PaperQuestionInstancePO::getQuestionBizId).collect(Collectors.toSet());

        // 4.根据大题题组主键ID列表查询大题列表
        List<BigQuestionGroup> finalQuestionList = questionService.batchGetBigQuestions(bigGroupIds);
        if(CollectionUtils.isEmpty(finalQuestionList)){
            log.debug("根据题目ID查询大题列表为空， instanceId:{}", paperId);
            return null;
        }

        // 5.通过试卷实例ID构建大题列表（各类型试卷根据条件重构题目列表）
        finalQuestionList.forEach(q->rebuildDiagnosticQuestionByRetention(q.getQuestions(), bizQuestionIds));

        // 6.生成用户作答试卷实例
        PaperInstance paperInstance = createPaperInstance(paper, finalQuestionList, scoreBatchId, instanceId, openId, tenantId,
                getTestMode(instanceId, openId, tenantId));
        log.debug("加载试卷实例信息:\n{}", JsonUtil.toJsonString(paperInstance));

        return paperInstance;
    }

    /** 获取试卷测试模式 */
    protected UnitTestModeEnum getTestMode(String instanceId, String openId, Long tenantId){
        return null;
    }

    /**
     * 获取试卷基本信息
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @return 试卷基本信息
     */
    private Paper getPaper(String paperId, String versionNumber) {
        PaperPO paperPO = paperExtendPOMapper.selectPaperDetail(paperId, versionNumber, true);
        if(paperPO==null){
            log.debug("未能查询到试卷基本信息，paperId: {}, versionNumber: {}", paperId, versionNumber);
            return null;
        }
        return paperPO.toEntity();
    }

    /**
     * 获取最近一次试卷实例信息
     * @param paperId 试卷ID
     * @param paperVersion 版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 诊断卷测试模式(1:诊断模式/2:推荐模式)
     * @param submitStatus 提交状态
     * @return 最近一次试卷实例信息
     */
    abstract PaperRoundPO getLatestInstanceInfo(String paperId, String paperVersion, String openId, Long tenantId, UnitTestModeEnum testMode, Integer submitStatus);

    /**
     * 获取用户特定试卷的最近一次试卷实例信息
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 诊断卷测试模式(1:诊断模式/2:推荐模式)
     * @param submitStatus 试卷提交状态
     * @return 用户作答记录
     */
    @Override
    public PaperInstance getLatestPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode, Integer submitStatus){
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();

        // 1.查询最新一次试卷提交记录
        PaperRoundPO latestPaperInstance = getLatestInstanceInfo(paperId, versionNumber, openId, tenantId, testMode, submitStatus);
        if(latestPaperInstance == null){
            log.debug("未能从数据库中查询到试卷最新轮次提交记录: {}, {}, {}, {}", paperId, versionNumber, openId, tenantId);
            return null;
        }
        String scoreBatchId = latestPaperInstance.getScoreBatchId();
        String instanceId = latestPaperInstance.getId();

        // 2.查询最近实例关联的试题列表
        List<PaperQuestionInstancePO> paperQuestionInstancePOS = paperQuestionInstancePOMapper.selectPaperQuestionsByRoundId(instanceId);
        if(CollectionUtils.isEmpty(paperQuestionInstancePOS)){
            log.debug("未能从数据库中查询到试卷最新轮次答题记录，paperId: {}, versionNumber: {}", paperId, versionNumber);
            return null;
        }

        // 获取试卷大题ID列表（试卷题目作答记录是以小题维度记录的，需要对大题ID去重）
        List<Long> bigGroupIds = paperQuestionInstancePOS.stream().map(PaperQuestionInstancePO::getQuestionGroupId).distinct().toList();
        // 获取小题ID列表
        Set<String> bizQuestionIds = paperQuestionInstancePOS.stream().map(PaperQuestionInstancePO::getQuestionBizId).collect(Collectors.toSet());

        // 3.构建用户作答试卷题目实例
        List<BigQuestionGroup> finalQuestionList = questionService.batchGetBigQuestions(bigGroupIds);
        if(CollectionUtils.isEmpty(finalQuestionList)){
            log.debug("未能从数据库中查询到试卷题目组列表，paperId: {}, versionNumber: {}", paperId, versionNumber);
            return null;
        }

        // 4.通过试卷实例ID构建大题列表(仅保留当前实例中的小题)
        finalQuestionList.forEach(q->rebuildDiagnosticQuestionByRetention(q.getQuestions(), bizQuestionIds));

        // 5.生成用户作答试卷实例
        PaperInstance paperInstance = createPaperInstance(paper, finalQuestionList, scoreBatchId, instanceId,  openId, tenantId, testMode);
        log.debug("加载最近一次试卷实例信息:\n{}", JsonUtil.toJsonString(paperInstance));

        return paperInstance;
    }

    /**
     * 保留诊断卷中指定题目
     * @param questions 题目列表
     * @param needToRetentionIds 需要保留的题目ID列表（任意层级上的对象ID列表）
     */
    private void rebuildDiagnosticQuestionByRetention(List<Question> questions, Set<String> needToRetentionIds) {
        if (CollectionUtils.isEmpty(questions) || CollectionUtils.isEmpty(needToRetentionIds)) {
            // 题组列表为空或者移除对象ID集合为空，则停止处理
            return;
        }

        // 过滤题目列表
        if(questions.stream().anyMatch(q -> needToRetentionIds.contains(q.getBizQuestionId()))){
            // 包含指定的题组ID，则从这一层移除指定题目
            questions.removeIf(q -> !needToRetentionIds.contains(q.getBizQuestionId()));
        }
        // 递归处理下级题目列表
        questions.forEach(q -> rebuildDiagnosticQuestionByRetention(q.getQuestions(), needToRetentionIds));
    }
}
