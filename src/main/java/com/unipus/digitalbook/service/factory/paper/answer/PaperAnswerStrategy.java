package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;

import java.util.List;
import java.util.Map;

/**
 * 试卷答案策略
 */
public interface PaperAnswerStrategy {

    /**
     * 获取试卷类型
     */
    PaperTypeEnum getPaperType();

    /**
     * 提交试卷成绩
     * @param paperInstance 试卷实例
     * @param userAnswerMap 用户答案
     * @param userAccessInfo 用户访问信息
     * @return 用户试卷成绩
     */
    UserPaperInfo submit(PaperInstance paperInstance, Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo);

    /**
     * 提交用户最佳成绩
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本
     * @param userAccessInfo 用户访问信息
     * @return 用户试卷信息
     */
    UserPaperInfo submitBestScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo);

    /**
     * 保存用户试卷记录
     * @param paperInstance 试卷实例
     * @param userPaperScore 试卷分数对象
     */
    void saveUserPaperRecord(PaperInstance paperInstance, UserPaperScore userPaperScore);

    /**
     * 获取用户试卷答案
     * @param paperInstance 试卷实例
     * @return 用户试卷答案
     */
    UserPaperAnswer getUserPaperAnswer(PaperInstance paperInstance);

    /**
     * 同步用户试卷分数
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本
     * @param userAccessInfo 用户访问信息
     * @param unitTest 单元测试模式枚举
     * @return 用户试卷分数
     */
    UserPaperScore getUserPaperScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo, UnitTestModeEnum unitTest);

    /**
     * 同步用户试卷成绩到UAI
     * 1.挑战卷：取得当前成绩批次的最高成绩后同步UAI
     * 2.常规卷/诊断卷：每次作答记录（同时计算成绩）的时候同步UAI
     * @param userPaperInfo 用户试卷成绩信息
     * @param ext 用户访问信息
     */
    void syncUserPaperScore(UserPaperInfo userPaperInfo, UserPaperSubmitExtInfo ext);

}
