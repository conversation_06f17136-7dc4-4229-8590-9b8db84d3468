package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.conf.cache.CacheManagerConfig;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.service.TagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签处理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TagProcessor {
    private final TagService tagService;
    private final CacheManagerConfig cacheManagerConfig;

    /**
     * 处理知识点
     * @param questionIds 题目ID列表
     * @return 知识点信息映射（key：知识点(名称)，value:资源ID列表）
     */
    public List<Tag> processKnowledgePoints(String paperId, List<String> questionIds, String versionNumber) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return List.of();
        }
        // 生成标签缓存key（默认版本不参与缓存）
        String cacheKey = (versionNumber==null || IdentifierUtil.DEFAULT_VERSION_NUMBER.equals(versionNumber)) ?
                null : paperId + ":" + versionNumber;

        // 获取缓存里已存在的标签信息
        Map<String, List<Tag>> cachedTagsMap = getCachedTagsMap(cacheKey);

        // 计算缓存未命中的题目ID
        List<String> missingIds = questionIds.stream()
                .filter(qid -> !cachedTagsMap.containsKey(qid))
                .toList();

        // 如果全部命中直接返回（按题目顺序）
        if (missingIds.isEmpty()) {
            return sortByQuestionIds(cachedTagsMap, questionIds);
        }

        // 仅对未命中部分查询，减小回源压力
        Map<String, List<Tag>> fetched = tagService.getTagListByResourceIdsAndVersion(missingIds, TagResourceTypeEnum.QUESTION, versionNumber);
        if (fetched == null) {
            fetched = Collections.emptyMap();
        }

        if (fetched.isEmpty() && cacheKey == null) {
            // 无缓存键且无新数据，直接按已有缓存（为空）返回
            return sortByQuestionIds(cachedTagsMap, questionIds);
        }

        // 合并缓存与新查询结果；对仍未返回的数据做负缓存，避免重复回源
        Map<String, List<Tag>> merged = new HashMap<>(cachedTagsMap);
        merged.putAll(fetched);
        missingIds.stream()
                .filter(id -> !merged.containsKey(id))
                .forEach(id -> merged.put(id, List.of()));

        log.debug("诊断卷标签信息(merge后):{}", JsonUtil.toJsonString(merged));
        // 写入本地缓存（cacheKey 为 null 时会被忽略）
        addLocalCatchData(cacheKey, merged);

        // 返回按题目顺序排好的标签列表
        return sortByQuestionIds(merged, questionIds);
     }

     // 按题目ID列表顺序输出知识点，并确保返回非空列表
    private List<Tag> sortByQuestionIds(Map<String, List<Tag>> tagsMap, List<String> questionIds) {
        return questionIds.stream()
                .map(tagsMap::get)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(List::stream)
                .toList();
    }

    // 获取缓存数据
     private Map<String, List<Tag>> getCachedTagsMap(String cacheKey) {
        if(cacheKey==null) {
            return new HashMap<>();
        }
        Map<String, List<Tag>> tagsMap = cacheManagerConfig.getLocalCacheData(CacheManagerConfig.CACHE_NAME_PAPER_TAGS, cacheKey);
        if(tagsMap==null) {
            return new HashMap<>();
        }
        return  tagsMap;
     }

     // 写入缓存数据
     private void addLocalCatchData(String cacheKey, Map<String, List<Tag>> tagsMap) {
         cacheManagerConfig.addLocalCacheData(CacheManagerConfig.CACHE_NAME_PAPER_TAGS, cacheKey, tagsMap);
     }
}
