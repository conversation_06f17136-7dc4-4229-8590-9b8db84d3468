package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.PaperRoundPOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.service.PaperInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 挑战卷答案策略
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ChallengeAnswerStrategy extends AbstractPaperAnswerStrategy{
    private final PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    private final PaperRoundPOMapper paperRoundPOMapper;
    private final PaperInstanceService paperInstanceService;

    /**
     * 获取试卷类型
     */
    @Override
    public PaperTypeEnum getPaperType() {
        return PaperTypeEnum.CHALLENGE;
    }

    /**
     * 提交用户作答
     * @param paperInstance 试卷实例
     * @param userAnswerMap 用户答案
     * @param userAccessInfo 用户访问基本信息
     * @return 用户试卷信息
     */
    @Override
    public UserPaperInfo submit(PaperInstance paperInstance, Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo) {

        // 提交用户作答，取得作答成绩
        List<UserQuestionScore> userQuestionScores = super.submit(paperInstance.getBigQuestionGroupList(), userAnswerMap);

        // 构建用户作答成绩信息实体
        UserPaperScore score = new UserPaperScore(paperInstance.getScoreBatchId(), paperInstance.getInstanceId(),
                PaperTypeEnum.CHALLENGE, userQuestionScores, null, PaperSubmitStatusEnum.UNSUBMITTED);

        // 保存用户挑战卷成绩信息
        super.saveUserPaperRecord(paperInstance, score);

        // 构建用户试卷成绩信息并返回
        return UserPaperInfo.build(paperInstance, score.getUserScore(), PaperSubmitStatusEnum.UNSUBMITTED);
    }

    /**
     * 提交用户最佳成绩
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷信息
     */
    @Override
    public UserPaperInfo submitBestScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();

        // 1.查询成绩批次表（取得scoreBatchId：status=0未提交, 可以查询到时间连续的跨版本的批次记录）
        PaperScoreBatchPO condition = new PaperScoreBatchPO(paperId, null, openId, tenantId,
                PaperSubmitStatusEnum.UNSUBMITTED.getCode());
        PaperScoreBatchPO paperScoreBatchPO = paperScoreBatchPOMapper.getLatestPaperScoreBatch(condition);
        if(paperScoreBatchPO==null){
            log.debug("未找到未提交成绩记录:{}，{}，{}，{}", openId, tenantId, paperId, versionNumber);
            throw new IllegalArgumentException("成绩批次表未找到未提交成绩记录");
        }
        String scoreBatchId = paperScoreBatchPO.getId();

        // 2.查询用户挑战轮次表（取得轮次成绩列表）
        List<PaperRoundPO> paperRoundPOs = paperRoundPOMapper.selectByScoreBatchIds(List.of(scoreBatchId));
        if(CollectionUtils.isEmpty(paperRoundPOs)){
            log.debug("未找到挑战轮次记录:{}，{}，{}，{}", openId, tenantId, paperId, versionNumber);
            throw new IllegalArgumentException("挑战轮次表未找到挑战轮次记录");
        }
        // 3.构建试卷最佳成绩信息
        PaperRoundPO bestRound = paperRoundPOs.stream()
                .max(Comparator.comparing(PaperRoundPO::getUserScore))
                .orElseThrow(() -> new IllegalArgumentException("未找到有效的挑战轮次记录"));
        UserPaperInfo userPaperInfo = UserPaperInfo.buildBestUserPaperScoreInfo(paperScoreBatchPO, bestRound);
        String bestScorePaperInstanceId = bestRound.getId();

        // 4.获取最佳得分的试卷实例及用户试卷信息
        UserPaperSubmitExtInfo ext = buildUserPaperSubmitExtInfo(userAccessInfo, bestScorePaperInstanceId);

        // 5.提交成绩到UAI（同步成功更新提交状态）
        super.syncUserPaperScore(userPaperInfo, ext);

        return userPaperInfo;
    }

    private UserPaperSubmitExtInfo buildUserPaperSubmitExtInfo(UserAccessInfo userAccessInfo, String paperInstanceId) {
        // 1.构建用户试卷实例
        PaperInstance paperInstance = paperInstanceService.loadPaperInstance(paperInstanceId, PaperTypeEnum.CHALLENGE);
        if(paperInstance==null){
            log.error("挑战卷:试卷实例不存在，instanceId:{}", paperInstanceId);
            throw new BizException("挑战卷:试卷实例不存在");
        }

        // 2.构建用户答案
        UserPaperAnswer userPaperAnswer = super.getUserPaperAnswer(paperInstance);
        if(userPaperAnswer==null){
            log.error("用户答案不存在，instanceId:{}", paperInstanceId);
            throw new BizException("用户答案不存在");
        }
        Map<String, List<UserAnswer>> userAnswerMap = userPaperAnswer.getUserAnswersMap();

        // 3.构建用户试卷成绩提交扩展信息
        return super.buildUserPaperSubmitExtInfo(paperInstance, userAnswerMap, userAccessInfo);
    }

}
