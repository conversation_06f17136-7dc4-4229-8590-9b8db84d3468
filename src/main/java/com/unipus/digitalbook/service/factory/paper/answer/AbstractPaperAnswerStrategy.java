package com.unipus.digitalbook.service.factory.paper.answer;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.paper.UserPaperNotPassException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper;
import com.unipus.digitalbook.dao.PaperRoundPOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.service.PaperVersionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 试卷作答处理抽象类
 */
@Slf4j
public abstract class AbstractPaperAnswerStrategy implements PaperAnswerStrategy{

    @Resource
    private UserAnswerProcessor answerProcessor;
    @Resource
    private PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    @Resource
    private PaperQuestionInstancePOMapper paperQuestionInstancePOMapper;
    @Resource
    private PaperRoundPOMapper paperRoundPOMapper;
    @Resource
    private PaperVersionService paperVersionService;
    @Resource
    private TenantMessageProducer tenantMessageProducer;

    /**
     * 处理用户答案
     * @param questionGroupList 大题组列表
     * @param userAnswerMap 用户答案
     * @return 用户试题得分列表
     */
    protected List<UserQuestionScore> submit(List<BigQuestionGroup> questionGroupList,
                                             Map<String, List<UserAnswer>> userAnswerMap) {
        // Process answers and calculate scores
        return answerProcessor.processAnswers(questionGroupList, userAnswerMap);
    }

    /**
     * 提交最高分
     * (根据需要个别实现)
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public @Nullable UserPaperInfo submitBestScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo) {
        // 默认无需处理
        return null;
    }

    /**
     * 保存用户试卷记录
     * @param paperInstance 试卷实例
     * @param userPaperScore 试卷分数对象
     */
    @Override
    public void saveUserPaperRecord(PaperInstance paperInstance, @Nullable UserPaperScore userPaperScore) {
        String instanceId = paperInstance.getInstanceId();
        String openId = paperInstance.getOpenId();
        Long tenantId = paperInstance.getTenantId();

        // 1. 校验题目组
        List<BigQuestionGroup> bigQuestionGroupList = paperInstance.getSortedQuestionGroups();
        if (CollectionUtils.isEmpty(bigQuestionGroupList)) {
            throw new BizException("试卷实例中题目组列表为空");
        }

        // 2. 保存成绩批次
        BigDecimal userScore = Optional.ofNullable(userPaperScore).map(UserPaperScore::getUserScore).orElse(BigDecimal.ZERO);
        PaperScoreBatchPO paperScoreBatchPO = new PaperScoreBatchPO(paperInstance, userScore,
                PaperSubmitStatusEnum.UNSUBMITTED.getCode(), null, openId, tenantId);
        if (paperScoreBatchPOMapper.insertOrUpdate(paperScoreBatchPO) <= 0) {
            throw new BizException("保存用户试卷成绩批次记录失败");
        }

        // 3. 保存轮次/实例
        PaperRoundPO paperRoundPO = new PaperRoundPO(paperInstance, userPaperScore, openId);
        if (paperRoundPOMapper.insertOrUpdate(paperRoundPO) <= 0) {
            throw new BizException("保存用户试卷轮次记录失败");
        }

        // 4. 获取小题作答信息(小题维度存储用户作答记录)
        Map<String, UserPaperScore.UserQuestionScoreRecord> questionScoreMap = userPaperScore==null ?
                Map.of() : userPaperScore.getUserSmallQuestionScoreRecordsMap();
        UserPaperScore.UserQuestionScoreRecord defaultRecord = UserPaperScore.UserQuestionScoreRecord.getDefaultRecord();

        // 5. 生成题目实例PO列表
        int totalQuestions = bigQuestionGroupList.stream().mapToInt(g ->
                g.getQuestions() == null ?  0 : g.getQuestions().size()).sum();
        List<PaperQuestionInstancePO> questionInstances = new ArrayList<>(totalQuestions);

        for (BigQuestionGroup group : bigQuestionGroupList) {
            List<Question> questions = group.getQuestions();
            if (CollectionUtils.isEmpty(questions)) continue;

            for (Question small : questions) {
                String bizQuestionId = small.getBizQuestionId();
                UserPaperScore.UserQuestionScoreRecord info = questionScoreMap.getOrDefault(bizQuestionId, defaultRecord);
                questionInstances.add(new PaperQuestionInstancePO(group, small, instanceId, info, openId));
            }
        }

        // 6. 批量插入
        if (!questionInstances.isEmpty()) {
            paperQuestionInstancePOMapper.batchInsertOrUpdate(questionInstances);
        }

        log.debug("保存用户试卷记录成功:试卷实例ID【{}】, 试卷题目实例数量【{}】", instanceId, questionInstances.size());
    }

    /**
     * 获取用户试卷答案
     * @param paperInstance 试卷实例
     * @return 用户试卷答案
     */
    @Override
    public UserPaperAnswer getUserPaperAnswer(PaperInstance paperInstance) {
        String instanceId = paperInstance.getInstanceId();
        String scoreBatchId = paperInstance.getScoreBatchId();

        // 1.查询试卷成绩批次提交状态
        Integer submitStatus = paperScoreBatchPOMapper.getPaperScoreBatchStatus(scoreBatchId);
        if(submitStatus==null){
            log.info("未找到成绩批次信息:成绩批次ID【{}】，试卷实例ID【{}】", scoreBatchId, instanceId);
            return null;
        }

        // 2.查询题目的用户作答记录
        Map<String, List<UserAnswer>> userAnswersMap = answerProcessor.getAnswers(paperInstance);

        // 3.输出用户作答记录
        return new UserPaperAnswer(scoreBatchId, instanceId, BigDecimal.ZERO, userAnswersMap, submitStatus);
    }

    /**
     * 获取用户最后的试卷成绩批次记录
     * 常规卷/挑战卷：使用当前默认处理
     * 诊断卷：重载该方法实现获取逻辑
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 测试模式
     * @return 用户作答记录
     */
    protected PaperScoreBatchPO getLatestPaperScoreBatch(String paperId, String versionNumber, String openId,
                                                         Long tenantId, UnitTestModeEnum testMode){
        PaperScoreBatchPO condition = new PaperScoreBatchPO(paperId, versionNumber, openId, tenantId, null);
        return paperScoreBatchPOMapper.getLatestPaperScoreBatch(condition);
    }

    /**
     * 获取用户试卷已成绩
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param userAccessInfo 用户访问信息
     * @param testMode 单元测试模式枚举
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public UserPaperScore getUserPaperScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo, UnitTestModeEnum testMode) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();

        // 1.查询成绩批次表
        PaperScoreBatchPO latestScoreBatchPO = getLatestPaperScoreBatch(paperId, null, openId, tenantId, testMode);
        if(latestScoreBatchPO==null){
            log.debug("未找到未提交成绩记录:{}，{}，{}，{}", paperId, versionNumber, openId, tenantId);
            return null;
        }
        String scoreBatchId = latestScoreBatchPO.getId();
        Integer paperType = latestScoreBatchPO.getPaperType();
        PaperSubmitStatusEnum submitStatus = PaperSubmitStatusEnum.getEnumByCode(latestScoreBatchPO.getStatus());

        // 2.查询用挑战轮次表/实例表
        List<PaperRoundPO> paperRoundPOs = paperRoundPOMapper.getLatestPaperInstanceByScoreBatchId(scoreBatchId);
        if(CollectionUtils.isEmpty(paperRoundPOs)){
            log.debug("未找到挑战轮次记录/试卷实例:{}，{}，{}，{}", paperId, versionNumber, openId, tenantId);
            return null;
        }
        String latestPaperRoundId = paperRoundPOs.getFirst().getId();

        // 3.查询用户试卷题目实例成绩
        List<PaperQuestionInstancePO> latestRoundQuestionInstancePOs =
                paperQuestionInstancePOMapper.selectPaperQuestionsByRoundId(latestPaperRoundId);
        if(CollectionUtils.isEmpty(latestRoundQuestionInstancePOs)){
            log.debug("未找到用户试卷题目实例成绩:{}，{}，{}，{}", paperId, versionNumber, openId, tenantId);
            return null;
        }
        List<UserQuestionScore> userQuestionScores = latestRoundQuestionInstancePOs.stream()
                .map(UserQuestionScore::new).toList();

        // 4.构建用户试卷成绩信息，返回用户试卷成绩信息
        return new UserPaperScore(scoreBatchId, latestPaperRoundId, PaperTypeEnum.getByCode(paperType),
                userQuestionScores, paperRoundPOs, submitStatus);
    }

    /**
     * 构建用户试卷提交扩展信息
     *
     * @param paperInstance 试卷实例
     * @param userAnswerMap 用户答案
     * @param userAccessInfo 用户访问信息
     * @return 用户试卷提交扩展信息
     */
    protected UserPaperSubmitExtInfo buildUserPaperSubmitExtInfo(PaperInstance paperInstance,
        Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo) {

        String paperId = paperInstance.getPaperId();
        String paperVersionNumber = paperInstance.getVersionNumber();

        BookVersion bookVersion = paperVersionService.getBookInfoByPaperVersion(paperId, paperVersionNumber);

        String bookId = null;
        String bookVersionNumber = null;
        if(bookVersion==null){
            log.info("未检索到试卷关联的教材(可能是编辑中默认版本的试卷)，试卷ID:{},试卷版本:{}", paperId, paperVersionNumber);
        }else {
            bookId = bookVersion.getBookId();
            bookVersionNumber = bookVersion.getVersionNum();
        }
        return new UserPaperSubmitExtInfo(bookId, bookVersionNumber, paperInstance, userAnswerMap, userAccessInfo);
    }

    /**
     * 同步用户试卷成绩到UAI
     * 1.挑战卷：取得当前成绩批次的最高成绩后同步UAI
     * 2.常规卷/诊断卷：每次作答记录（同时计算成绩）的时候同步UAI
     * @param userPaperInfo 用户试卷信息
     * @param ext 用户试卷成绩提交扩展信息
     */
    @Override
    public void syncUserPaperScore(UserPaperInfo userPaperInfo, UserPaperSubmitExtInfo ext){

        // 提交成绩到UAI
        pushUserPaperAnswerToThird(ext);

        // 更新用户成绩批次
        updateUserPaperScoreBatch(userPaperInfo);
    }

    /**
     * 更新用户成绩批次
     * @param userPaperInfo 用户试卷信息
     */
    protected void updateUserPaperScoreBatch(UserPaperInfo userPaperInfo) {
        // 提交成功，更新成绩提交状态表
        paperScoreBatchPOMapper.updateById(new PaperScoreBatchPO(userPaperInfo));
    }

    /**
     * 提交用户试卷到UAI，同时检测客观题正确率
     * 如果过关率未达标，则抛出业务异常
     * @param ext 用户试卷提交扩展信息
     * @exception UserPaperNotPassException 用户试卷未通过
     */
    private void pushUserPaperAnswerToThird(UserPaperSubmitExtInfo ext) {
        if(isPreviewMode(ext)){
            return;
        }
        // 1. 构建用户试卷客观题作答信息
        UserPaperSyncInfo userPaperSyncInfo = new UserPaperSyncInfo(ext);
        UserAccessInfo userAccessInfo = ext.getUserAccessInfo();
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAccessInfo.getTenantId(), MessageTopicEnum.PUSH_PAPER_ANSWER)).isPresent()) {
            Response<String> response = tenantMessageProducer.produceSyncMessage(
                    userAccessInfo.getTenantId(),
                    MessageTopicEnum.PUSH_PAPER_ANSWER,
                    new TypeReference<>() {
                    },
                    new PaperAnswerNodeData(userPaperSyncInfo),
                    new TypeReference<>() {
                    });
            // 3.检查是否通过
            if (!response.isSuccess()) {
                throw new UserPaperNotPassException(response.getMessage());
            }
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_PAPER_ANSWER.name(), userAccessInfo.getTenantId());
        }
    }
    private boolean isPreviewMode(UserPaperSubmitExtInfo ext) {
        return ext.getUserAccessInfo().getOpenId() == null
                || ext.getBookId() == null
                || ext.getBookVersionNumber() == null
                || IdentifierUtil.DEFAULT_VERSION_NUMBER.equals(ext.getBookVersionNumber());
    }
}
