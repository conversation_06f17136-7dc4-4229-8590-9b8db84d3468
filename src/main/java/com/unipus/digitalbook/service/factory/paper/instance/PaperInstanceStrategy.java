package com.unipus.digitalbook.service.factory.paper.instance;

import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.PaperInstanceCreationContext;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;

/**
 * 试卷实例处理器接口
 */
public interface PaperInstanceStrategy {

    /**
     * 获取试卷实例类型
     */
    PaperTypeEnum getPaperType();

    /**
     * 初始化预览卷状态
     *
     * @param paper    试卷对象
     * @param openId   用户ID
     * @param tenantId 租户ID
     * @param clearPreviewHistory 是否清除历史作答
     */
    void initPreviewPaperStatus(Paper paper, String openId, Long tenantId, Boolean clearPreviewHistory);

    /**
     * 生成预览试卷实例
     * @param context 试卷实例创建上下文对象
     * @return 试卷实例对象
     */
    PaperInstance createPreviewPaperInstance(PaperInstanceCreationContext context);

    /**
     * 生成实际试卷实例
     * @param context 试卷实例创建上下文对象
     * @return 试卷实例对象
     */
    PaperInstance createRealPaperInstance(PaperInstanceCreationContext context);

    /**
     * 加载试卷实例
     * @param instanceId 试卷实例ID
     * @return 试卷实例对象
     */
    PaperInstance loadPaperInstance(String instanceId);

    /**
     * 获取用户特定试卷的最近一次试卷实例信息
     * @param paper 试卷对象
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 诊断卷测试模式(1:诊断模式/2:推荐模式)
     * @param submitStatus 试卷提交状态
     * @return 用户作答记录
     */
    PaperInstance getLatestPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode, Integer submitStatus);

}
