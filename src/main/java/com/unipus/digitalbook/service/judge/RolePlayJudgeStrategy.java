package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.clio.ClioSentResult;
import com.unipus.digitalbook.model.entity.question.type.RolePlayQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 角色扮演题评测策略
 *
 * <AUTHOR>
 * @date 2025/3/21 14:41
 */
@Slf4j
@Component
public class RolePlayJudgeStrategy extends AbstractOralJudgeStrategy<RolePlayQuestion> {

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.ROLE_PLAY_SCOOP);
    }

    @Override
    protected BigDecimal total(String evaluation) {
        ClioSentResult clioOpenResult = JSON.parseObject(evaluation, ClioSentResult.class);
        if (clioOpenResult != null && clioOpenResult.getFinalResult() != null
                && clioOpenResult.getFinalResult().getResult() != null
                && clioOpenResult.getFinalResult().getResult().getTotal() != null) {
            return BigDecimal.valueOf(clioOpenResult.getFinalResult().getResult().getTotal());
        }
        log.error("角色扮演作答结果数据异常: {}", evaluation);
        return BigDecimal.ZERO;
    }
}
