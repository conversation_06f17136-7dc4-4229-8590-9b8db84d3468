package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.user.UserImportResult;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 用户导入服务接口定义
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/6 14:39
 */
public interface UserImportService {

    /**
     * 保存用户导入结果
     *
     * @param userImportResult 用户导入结果对象
     * @return 用户导入结果ID
     */
    Long save(UserImportResult userImportResult);

    /**
     * 根据ID获取用户导入结果
     *
     * @param id 用户导入结果ID
     * @return 用户导入结果
     */
    UserImportResult getById(Long id);

    /**
     * 根据ID更新用户导入结果
     *
     * @param userImportResult 用户导入结果对象
     * @return 操作是否成功
     */
    Boolean updateById(UserImportResult userImportResult);

    /**
     * 下载Excel用户导入模板
     *
     * @param orgId    组织ID
     * @param response HTTP响应对象
     */
    void downloadTemplate(Long orgId, HttpServletResponse response);

    /**
     * 通过Excel文件导入用户数据
     *
     * @param userId 用户ID
     * @param orgId  机构ID
     * @param file   要导入的Excel文件
     * @return 返回用户导入结果的ID
     */
    Long importByExcel(Long userId, Long orgId, MultipartFile file);
}
