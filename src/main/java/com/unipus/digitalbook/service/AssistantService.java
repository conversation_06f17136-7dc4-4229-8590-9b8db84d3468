package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.AssistantDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantInfoListDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantPositionInfoListDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantTemplateReferenceListDTO;
import com.unipus.digitalbook.model.params.assistant.AssistantAddParam;
import com.unipus.digitalbook.model.params.assistant.AssistantSelectBlocksParam;
import com.unipus.digitalbook.model.params.assistant.AssistantTemplateReferenceParam;
import com.unipus.digitalbook.model.po.assistant.AssistantPO;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTemplateResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTemplateSearchResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTypeListResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;

public interface AssistantService {

    /**
     * 学生获取章节最新的数字人实例列表
     * @param openId
     * @param bookId
     * @param bookVersionNumber
     * @param chapterId
     * @return
     */
    Response<AssistantPositionInfoListDTO> latestInstanceList(String openId, String bookId, String bookVersionNumber, String chapterId);

    /**
     * 教师获取章节数字人实例列表
     * @param currentUserId
     * @param bookId
     * @param chapterId
     * @return
     */
    Response<AssistantInfoListDTO> instanceList(Long currentUserId, String bookId, String chapterId);

    /**
     * 教师发布教材数字人
     * @param currentUserId
     * @param bookId
     * @return
     */
    BaseResponse<Void> publishAll(Long currentUserId, String bookId);

    /**
     * 保存或更新数字人实例
     * @param param 数字人实例参数
     */
    void addAssistant(AssistantAddParam param, Long currentUserId);

    /**
     * 更新数字人实例
     * @param assistantPO
     */
    void updateAssistant(AssistantPO assistantPO);

    /**
     * 查询数字人实例
     * @param currentUserId
     * @param assistantId
     */
    Response<AssistantDTO> queryAssistant(Long currentUserId, String assistantId);

    /**
     * 查询cms模板列表
     * @param currentUserId
     * @param assistantType
     * @return
     */
    Response<AssistantTemplateSearchResponse> templateList(Long currentUserId, Integer assistantType);

    /**
     * 查询cms模板信息
     * @param currentUserId
     * @param templateId
     * @return
     */
    Response<AssistantTemplateResponse> queryTemplate(Long currentUserId, String templateId);

    /**
     * 删除数字人实例
     * @param id
     */
    void disableById(String id);

    /**
     * 发布数字人版本
     * @param bookId
     * @param version
     */
    void publishByBookId(String bookId, String version);

    /**
     * 根据ID查询数字人实例
     * @param id
     * @return
     */
    AssistantPO selectById(String id);

    /**
     * 获取数字人类型列表
     * @param currentUserId
     * @return
     */
    Response<AssistantTypeListResponse> assistantTypeList(Long currentUserId);

    Response<String> selectBlocks(AssistantSelectBlocksParam param);

    AssistantTemplateReferenceListDTO templateReference(AssistantTemplateReferenceParam param);
}
