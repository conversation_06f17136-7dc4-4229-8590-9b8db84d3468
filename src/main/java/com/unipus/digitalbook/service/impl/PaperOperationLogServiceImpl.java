package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.PaperBookRelationPOMapper;
import com.unipus.digitalbook.dao.QuestionGroupPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import com.unipus.digitalbook.model.enums.BookOperationEnum;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import com.unipus.digitalbook.service.BookOperationLogService;
import com.unipus.digitalbook.service.PaperOperationLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 试卷操作日志服务实现类
 */
@Service
@Slf4j
public class PaperOperationLogServiceImpl implements PaperOperationLogService {

    @Resource
    private BookOperationLogService operationLogService;
    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;
    @Resource
    private PaperBookRelationPOMapper paperBookRelationPOMapper;

    // 日志模板常量
    private static final String LOG_INSERT_PAPER = "新增【%s】测试：【%s】";
    private static final String LOG_UPDATE_PAPER_NAME = "编辑【%s】测试的名称从“%s”变更为“%s”";
    private static final String LOG_EDIT_PAPER_CONTENT = "编辑【%s】测试：【%s】";
    private static final String LOG_DELETE_PAPER = "删除【%s】测试：【%s】";
    private static final String LOG_SAVE_QUESTION_BANK = "当新增了知识挑战的题库，显示内容：新增题库：%s";
    private static final String LOG_EDIT_QUESTION_BANK = "编辑题库信息，将题库名称从“%s”变更为“%s”。将题库每题分数由“%s”变更为“%s”，将每轮随机抽取题目数由“%s”变更为“%s”。";
    private static final String LOG_DELETE_QUESTION_BANK = "删除题库：【%s】";

    /**
     * 保存试卷操作日志
     * 1、当新增测试成功后，记录新增记录。显示内容：新增【类型】测试：【测试名称】
     * 6、当编辑测试名称成功后，记录编辑记录。显示内容：编辑【类型】测试的名称从“名称1”变更为“名称2”
     * @param oldPaper 新试卷对象
     * @param newPaper 新试卷对象
     * @param userId 用户ID
     */
    @Override
    public void addPaperSaveOperation(Paper oldPaper, Paper newPaper, Long userId) {
        if (newPaper == null) {
            log.warn("新试卷对象为空");
            return;
        }

        if (oldPaper == null) {
            logInsert(newPaper, userId);
            return;
        }

        if (!Objects.equals(oldPaper.getPaperName(), newPaper.getPaperName())) {
            logUpdateName(oldPaper, newPaper, userId);
            return;
        }

        if (!newPaper.compareContent(oldPaper)) {
            logEditContent(newPaper, userId);
        }
    }

    private void logInsert(Paper paper, Long userId) {
        String message = LOG_INSERT_PAPER.formatted(getPaperTypeDesc(paper), paper.getPaperName());
        log.debug(message);
        processLog(paper.getPaperId(), null, userId, message, BookOperationEnum.INSERT);
    }

    private void logUpdateName(Paper oldPaper, Paper newPaper, Long userId) {
        String message = LOG_UPDATE_PAPER_NAME.formatted(getPaperTypeDesc(oldPaper), oldPaper.getPaperName(), newPaper.getPaperName());
        log.debug(message);
        processLog(newPaper.getPaperId(), null, userId, message, BookOperationEnum.UPDATE);
    }

    private void logEditContent(Paper paper, Long userId) {
        String message = LOG_EDIT_PAPER_CONTENT.formatted(getPaperTypeDesc(paper), paper.getPaperName());
        log.debug(message);
        processLog(paper.getPaperId(), null, userId, message, BookOperationEnum.SAVE);
    }

    private String getPaperTypeDesc(Paper paper) {
        return paper.getPaperType() == null ? "" : paper.getPaperType().getDesc();
    }

    /**
     * 删除试卷操作日志
     * 2、当删除测试测试成功后，记录删除记录。显示内容：删除【类型】测试：【测试名称】
     * @param paper 历史试卷对象
     * @param userId 当前用户ID
     */
    @Override
    public void addPaperDeleteOperation(Paper paper, Long userId) {
        if (paper == null) {
            log.warn("待删除试卷为空");
            return;
        }

        String message = LOG_DELETE_PAPER.formatted(getPaperTypeDesc(paper), paper.getPaperName());
        log.debug(message);
        processLog(paper.getPaperId(), null, userId, message, BookOperationEnum.DELETE);
    }

    /**
     * 试卷内容操作日志
     * 7、当常规测试和诊断测试编辑时，点击保存按钮后，记录编辑测试记录，显示内容：编辑【类型】测试：【测试名称】
     * @param paper 试卷对象
     * @param userId 当前用户ID
     */
    @Override
    public void addPaperContentOperation(Paper paper, Long userId) {
        if (paper == null || paper.getPaperType() == null) {
            log.warn("试卷或试卷类型为空");
            return;
        }

        String message = LOG_EDIT_PAPER_CONTENT.formatted(getPaperTypeDesc(paper), paper.getPaperName());
        log.debug(message);
        processLog(paper.getPaperId(), null, userId, message, BookOperationEnum.SAVE);
    }

    /**
     * 保存题库操作日志
     * 3、当新增了知识挑战的题库，显示内容：新增题库：题库名称
     * 4、当编辑了题库的信息，显示内容：编辑题库信息，将题库名称从“名称1”变更为“名称2”。将题库每题分数由“值1”变更为“值2”，将每轮随机抽取题目数由“值1”变更为“值2”。
     * @param paper 试卷对象
     * @param questionBank 题库对象
     * @param oldQuestionBank 旧题库对象
     * @param userId 用户ID
     */
    @Override
    public void addBankSaveOperation(Paper paper, QuestionBank questionBank, QuestionBank oldQuestionBank, Long userId) {
        if (questionBank == null) {
            log.warn("题库对象为空");
            return;
        }

        String message;
        if (oldQuestionBank != null) {
            message = LOG_EDIT_QUESTION_BANK.formatted(
                    oldQuestionBank.getBankName(),
                    questionBank.getBankName(),
                    oldQuestionBank.getQuestionScore(),
                    questionBank.getQuestionScore(),
                    oldQuestionBank.getQuestionsPerRound(),
                    questionBank.getQuestionsPerRound()
            );
        } else {
            message = LOG_SAVE_QUESTION_BANK.formatted(questionBank.getBankName());
        }

        log.debug(message);
        processLog(paper.getPaperId(), null, userId, message, BookOperationEnum.SAVE);
    }

    /**
     * 删除题库操作日志
     * 5、当删除了题库成功后，记录日志。显示内容：删除题库：【题库名称】
     * @param questionBank 题库
     * @param userId 用户ID
     */
    @Override
    public void addBankDeleteOperation(QuestionBank questionBank, Long userId) {
        if (questionBank == null) {
            log.warn("待删除题库为空");
            return;
        }

        String message = LOG_DELETE_QUESTION_BANK.formatted(questionBank.getBankName());
        log.debug(message);
        processLog(null, questionBank.getBankId(), userId, message, BookOperationEnum.DELETE);
    }

    /**
     * 记录操作日志
     * @param paperId 试卷ID
     * @param bankId 题库ID
     * @param userId 用户ID
     * @param message 日志内容
     * @param operation 操作类型
     */
    private void processLog(String paperId, String bankId, Long userId, String message, BookOperationEnum operation) {
        try {
            operationLogService.log(getBookId(paperId, bankId), userId, message, operation.getCode());
        } catch (IllegalArgumentException e) {
            log.error("无法记录日志，参数不合法", e);
        } catch (Exception e) {
            log.error("操作日志记录失败", e);
        }
    }

    /**
     * 取得教材ID
     * @param paperId 试卷ID
     * @param bankId 题库ID
     * @return 教材ID
     */
    private String getBookId(String paperId, String bankId) {
        String targetPaperId = paperId;
        if (StringUtils.hasText(bankId)) {
            QuestionGroupPO questionGroup = questionGroupPOMapper.selectParentByChildBizGroupId(bankId, IdentifierUtil.DEFAULT_VERSION_NUMBER);
            if (questionGroup == null) {
                throw new IllegalArgumentException("试卷不存在");
            }
            targetPaperId = questionGroup.getBizGroupId();
        }

        if (!StringUtils.hasText(targetPaperId)) {
            throw new IllegalArgumentException("无法取得试卷ID");
        }

        String bookId = paperBookRelationPOMapper.selectBookIdByPaperId(targetPaperId);
        if (!StringUtils.hasText(bookId)) {
            throw new IllegalArgumentException("无法取得教材ID");
        }

        return bookId;
    }
}
