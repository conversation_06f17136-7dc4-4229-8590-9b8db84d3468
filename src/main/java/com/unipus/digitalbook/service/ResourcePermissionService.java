package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.params.book.ResourceUserSearchParam;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * 教材权限相关业务
 * <AUTHOR>
 */
public interface ResourcePermissionService {

    /**
     * 获取平台所有待分配权限用户列表
     * @param param 检索条件
     * @return 资源用户列表
     */
    List<ResourceUser> getUsersToShare(ResourceUserSearchParam param);

    /**
     * 获取教材共享用户列表
     * @param param 检索条件
     * @return 用户id列表
     */
    List<ResourceUser> getSharedUsers(ResourceUserSearchParam param);

    /**
     * 获取平台所有教材待分配权限用户列表
     * @param orgId 组织id
     * @param resourceId 资源id
     * @param permission 权限类型
     * @param keyword 关键词
     * @param pageParams 分页参数
     * @return 用户信息列表
     */
    List<ResourceUser> getNoPermissionUser(
            @Nullable Long orgId, String resourceId, Integer permission, @Nullable String keyword, @Nullable PageParams pageParams);

    /**
     * 更新用户分享权限
     * @param resourceId 资源id
     * @param userIds 用户id列表
     * @param opUserId 操作用户id
     * @return 更新结果
     */
    Boolean updateUserSharePermission(String resourceId, List<Long> userIds, Long opUserId);

    /**
     * 更新用户资源权限(用于权限分配)
     *
     * @param inputPermissions 权限参数(包含资源id, 用户id, 权限类型)
     * @param opUserId 操作用户id
     * @return 更新结果
     */
     Boolean updateUserPermission(List<ResourcePermission> inputPermissions, Long opUserId);

    /**
     * 根据资源ID或用户ID获取权限信息列表
     * @param params 权限检索参数
     * @return 权限信息列表
     */
    List<ResourcePermission> getPermissions(List<PermissionSearchParam> params);

    /**
     * 根据资源ID列表获取权限信息列表
     * @param resourceIds 资源ID列表
     * @return 权限信息列表
     */
    List<ResourcePermission> getPermissionsByResourceIds(List<String> resourceIds);

    /**
     * 根据用户ID列表与权限类型获取权限信息列表
     * @param userIds 用户ID列表
     * @param permissionTypeEnum 权限类型
     * @return 权限信息列表
     */
    List<ResourcePermission> getPermissionsByUserIds(List<Long> userIds, @Nullable PermissionTypeEnum permissionTypeEnum);

    /**
     * 取得资源的用户信息列表
     * @param orgId 组织id
     * @param resourceId 资源id
     * @param resourceType 资源类型 (教材/章节)
     * @param permissionType 权限类型: 阅读/编辑
     * @param keyword 关键字
     * @return 用户列表
     */
    List<ResourceUser> getResourceUsers(Long orgId, String resourceId, @Nullable ResourceTypeEnum resourceType,
                                               @Nullable PermissionTypeEnum permissionType, @Nullable String keyword);

    /**
     * 取得用户资源列表
     * @param userId 用户ID
     * @param resourceType 资源类型 (教材/章节)
     * @param permissionType 权限类型: 阅读/编辑
     * @return 资源ID列表
     */
    List<String> getUserResources(Long userId, ResourceTypeEnum resourceType, PermissionTypeEnum permissionType);

    /**
     * 删除用户权限
     * @param resourcePermission 权限数据
     * @param opUserId 操作用户id
     */
    Boolean removeUserPermission(ResourcePermission resourcePermission, Long opUserId);

    /**
     * 判断用户是否有指定资源的指定权限
     *
     * @param resourcePermission 权限数据
     * @return 是否有编辑权限 true:有 false:无
     */
    Boolean hasResourcePermission(ResourcePermission resourcePermission);

}
