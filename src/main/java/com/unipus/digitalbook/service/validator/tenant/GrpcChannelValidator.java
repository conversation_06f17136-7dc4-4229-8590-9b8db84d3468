package com.unipus.digitalbook.service.validator.tenant;

import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.conf.tenant.GrpcDiscoveryConfig;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

@Service
@Slf4j
public class GrpcChannelValidator implements TenantChannelValidator{

    @Resource
    private GrpcDiscoveryConfig grpcDiscoveryConfig;

    @Override
    public void validate(TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO) {
        Long tenantId = tenantSubscribePO.getTenantId();
        String messageTopic = tenantSubscribePO.getMessageTopic();
        Pair<Method, Object> pair = grpcDiscoveryConfig.getMethod(tenantId, MessageTopicEnum.fromMessageTopic(messageTopic));

        if (pair == null) {
            log.error("tenantId: {} messageTopic: {} grpc not found", tenantId, messageTopic);
            throw new TenantMessageException(ProduceResultEnum.GRPC_NOT_FOUND.getMessage());
        }

        Method method = pair.getLeft();

        Type[] types = method.getGenericParameterTypes();
        if (types.length != 1) {
            log.error("tenantId: {} messageTopic: {} grpc 方法参数不正确，方法: {}, 参数数量: {}", tenantId, messageTopic, method.getName(), types.length);
            throw new TenantMessageException(ProduceResultEnum.GRPC_METHOD_PARAMETER_LENGTH_ERROR.getMessage());
        }
    }

    @Override
    public ChannelEnum supportChannel() {
        return ChannelEnum.GRPC;
    }
}
