package com.unipus.digitalbook.service.validator.tenant;

import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class KafkaChannelValidator implements TenantChannelValidator{
    @Override
    public void validate(TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO) {
        Long tenantId = tenantSubscribePO.getTenantId();
        String messageTopic = tenantSubscribePO.getMessageTopic();

        String bootstrapServers = tenantSubscribePO.getKafkaBootstrapServers();

        if (!StringUtils.hasText(bootstrapServers)) {
            log.error("tenantId {} messageTopic {} kafka not found", tenantId, messageTopic);
            throw new TenantMessageException(ProduceResultEnum.KAFKA_NOT_FOUND.getMessage());
        }
    }

    @Override
    public ChannelEnum supportChannel() {
        return ChannelEnum.KAFKA;
    }
}
