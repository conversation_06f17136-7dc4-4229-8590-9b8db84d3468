package com.unipus.digitalbook.service.validator.tenant;

import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;

public interface TenantChannelValidator {
    void validate(TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO);
    ChannelEnum supportChannel();
}
