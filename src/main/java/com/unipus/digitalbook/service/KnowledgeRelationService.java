package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.*;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeRelationQueryResponse;

/**
 * 图谱创建相关接口
 */
@Deprecated
public interface KnowledgeRelationService {

    /**
     * 新增关系
     *
     * @param params 新增关系请求参数
     * @return 基础响应
     */
    Response relationAdd(KnowledgeRelationAddRequest params);

    /**
     * 更新关系
     *
     * @param params 更新关系请求参数
     * @return 基础响应
     */
    Response relationUpdate(KnowledgeRelationUpdateRequest params);

    /**
     * 删除关系
     *
     * @param relationId 关系ID
     * @return 基础响应
     */
    Response relationDelete(String relationId);

    /**
     * 搜索关系类型
     *
     * @param query 搜索条件
     * @return 关系类型列表响应
     */
    Response<KnowledgeRelationQueryResponse> relationQuery(String query, String graphId);



}
