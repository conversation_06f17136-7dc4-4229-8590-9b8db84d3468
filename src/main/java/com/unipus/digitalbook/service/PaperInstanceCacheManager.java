package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.PaperInstance;

/**
 * 试卷实例缓存管理器
 */
public interface PaperInstanceCacheManager {

    /**
     * 缓存试卷实例
     * @param paperInstance 试卷实例
     */
    void cachePaperInstance(PaperInstance paperInstance);

    /**
     * 获取缓存的试卷实例
     * @param instanceId 实例ID
     * @return 试卷实例
     */
    PaperInstance getPaperInstanceCache(String instanceId);
}
