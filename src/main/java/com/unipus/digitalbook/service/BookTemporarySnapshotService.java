package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;

/**
 * 教材临时快照服务
 */
public interface BookTemporarySnapshotService {

    /**
     * 保存快照
     *
     * @param bookTemporarySnapshot 教材临时快照实体
     * @param userId                用户ID
     * @return 保存结果
     */
    Boolean saveSnapshot(BookTemporarySnapshot bookTemporarySnapshot, Long userId);

    /**
     * 根据主键ID查询快照
     *
     * @param id 主键ID
     * @return 快照结果
     */
    BookTemporarySnapshot getSnapshotById(Long id);

    /**
     * 根据教材ID和章节ID查询快照
     *
     * @param bookTemporarySnapshot 教材临时快照实体
     * @return 教材临时快照
     */
    BookTemporarySnapshot getLatestSnapshotByBookIdAndChapterId(BookTemporarySnapshot bookTemporarySnapshot);
}
