package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.AsyncFailLog;

import java.util.List;

/**
 * 异步处理异常日志操作服务接口
 */
public interface AsyncFailLogService {

    /**
     * 添加异步处理异常日志
     * @param asyncFailLog 异常日志对象
     */
    void addAsyncFailLog(AsyncFailLog asyncFailLog);

    /**
     * 查询异常日志
     * @param asyncFailLog 异常日志对象
     */
    List<AsyncFailLog> getAsyncFailLog(AsyncFailLog asyncFailLog);

    /**
     * 查询指定操作的异常日志数量
     * @param operation 异常日志操作
     */
    int selectFailCountByOperation(String operation);

    /**
     * 根据操作获取异常日志
     * @param operation 异常日志操作
     * @return 异常日志列表
     */
    List<AsyncFailLog> getByOperation(String operation);
}
