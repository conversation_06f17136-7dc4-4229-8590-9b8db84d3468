package com.unipus.digitalbook.service.converter;

import java.util.Objects;

public class ConverterKey {
    private final Class<?> sourceClass;
    private final Class<?> targetClass;

    public ConverterKey(Class<?> sourceClass, Class<?> targetClass) {
        this.sourceClass = sourceClass;
        this.targetClass = targetClass;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConverterKey that = (ConverterKey) o;
        return Objects.equals(sourceClass, that.sourceClass) &&
                Objects.equals(targetClass, that.targetClass);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sourceClass, targetClass);
    }
}
