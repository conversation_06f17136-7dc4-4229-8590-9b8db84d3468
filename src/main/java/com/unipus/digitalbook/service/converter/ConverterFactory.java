package com.unipus.digitalbook.service.converter;

import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import com.unipus.digitalbook.model.po.question.QuestionPO;

import java.util.HashMap;
import java.util.Map;

public class ConverterFactory {
    private static final ConverterKey CHOICE_QUESTION_CONVERTER = new ConverterKey(ChoiceQuestion.class, QuestionPO.class);


    private static final Map<ConverterKey, QuestionConverter> CONVERTER_MAP = new HashMap<>();

    static {
        CONVERTER_MAP.put(CHOICE_QUESTION_CONVERTER, new BigQuestionGroupConverter());
    }
    public static <T, S> S converter(T source, Class<S> targetClass) {
        if (source == null) {
            return null;
        }
        QuestionConverter converter = CONVERTER_MAP.get(new ConverterKey(source.getClass(), targetClass));
        if (converter == null) {
            throw new IllegalArgumentException("没有找到转换器");
        }
        return (S)converter.convert(source);
    }

    private ConverterFactory() {

    }

}
