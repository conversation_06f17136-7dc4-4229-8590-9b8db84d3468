package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;

@Getter
public abstract class TwoGenericBodyConverter<T, S> extends BaseBodyConverter implements BodyConverter<T, S> {

    private final TypeReference<T> oneTypeReference;
    private final TypeReference<S> twoTypeReference;

    protected TwoGenericBodyConverter(TypeReference<T> oneTypeReference, TypeReference<S> twoTypeReference) {
        this.oneTypeReference = oneTypeReference;
        this.twoTypeReference = twoTypeReference;
    }

    public S convert(T one) throws Exception {
        return convert(one, getTwo(), this::o2o);
    }

    public abstract S o2o(T one);

    public TypeReference<S> getTwo() {
        return twoTypeReference;
    }
}
