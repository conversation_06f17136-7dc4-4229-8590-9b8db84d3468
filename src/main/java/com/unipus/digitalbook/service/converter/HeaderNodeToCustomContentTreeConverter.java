package com.unipus.digitalbook.service.converter;

import com.unipus.digitalbook.model.dto.content.CustomContentNodeDTO;
import com.unipus.digitalbook.model.entity.content.CustomContentHeaderNode;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
@Slf4j
public class HeaderNodeToCustomContentTreeConverter {

    /**
     * Converts a flat list of CustomContentHeaderNode objects into a hierarchical tree of CustomContentNodeDTO objects
     * @param headerNodes The list of CustomContentHeaderNode objects to convert
     * @return A list of root CustomContentNodeDTO objects (typically h1 headers)
     */
    public static List<CustomContentNodeDTO> convertToCustomContentNodeTree(List<CustomContentHeaderNode> headerNodes) {
        if (headerNodes == null || headerNodes.isEmpty()) {
            return Collections.emptyList();
        }

        // Result list that will contain only the top-level nodes
        List<CustomContentNodeDTO> result = new ArrayList<>();

        // Stack to keep track of the current path in the hierarchy
        // Each level in the header hierarchy (h1, h2, etc.) has a corresponding stack position
        Map<Integer, CustomContentNodeDTO> headerStack = new HashMap<>();

        for (CustomContentHeaderNode headerNode : headerNodes) {
            // Create a new CustomContentNodeDTO from the CustomContentHeaderNode
            CustomContentNodeDTO customContentNode = getCustomContentNodeDTO(headerNode);

            // Determine the header level from the type (h1, h2, etc.)
            int level = getHeaderLevel(headerNode.getType());
            // If it's a h1, add it directly to the result list as a root node
            if (level == 1) {
                result.add(customContentNode);
                // Clear all levels from the stack and add this h1
                headerStack.clear();
                headerStack.put(1, customContentNode);
            } else {
                // Find the parent node for this header
                int parentLevel = findParentLevel(headerStack, level);

                if (parentLevel > 0) {
                    // Add this node as a child to its parent
                    CustomContentNodeDTO parentNode = headerStack.get(parentLevel);
                    parentNode.getChildren().add(customContentNode);

                    // Update the stack for this level and clear any deeper levels
                    for (int i = level + 1; i <= 6; i++) {
                        headerStack.remove(i);
                    }
                    headerStack.put(level, customContentNode);
                } else {
                    // No appropriate parent found, treat as root node
                    result.add(customContentNode);
                    headerStack.clear();
                    headerStack.put(level, customContentNode);
                }
            }
        }

        return result;
    }

    private static CustomContentNodeDTO getCustomContentNodeDTO(CustomContentHeaderNode headerNode) {
        CustomContentNodeDTO customContentNode = new CustomContentNodeDTO();
        customContentNode.setId(headerNode.getId());
        customContentNode.setText(headerNode.getText());
        customContentNode.setType(headerNode.getType());
        customContentNode.setChildren(new ArrayList<>());
        // Initialize with default values
        customContentNode.setWordCount(0L);
        customContentNode.setAudioDuration(0L);
        customContentNode.setVideoDuration(0L);
        return customContentNode;
    }

    /**
     * Extracts the header level from the header type (h1, h2, etc.)
     * @param headerType The type of header (h1, h2, etc.)
     * @return The level as an integer (1 for h1, 2 for h2, etc.)
     */
    private static int getHeaderLevel(String headerType) {
        if (headerType == null || headerType.isEmpty()) {
            return 0;
        }

        try {
            // Extract the digit from the header type (h1, h2, etc.)
            return Integer.parseInt(headerType.substring(1));
        } catch (NumberFormatException | IndexOutOfBoundsException e) {
            return 0;
        }
    }

    /**
     * Finds the appropriate parent level for a given header level
     * @param headerStack The current hierarchy stack
     * @param currentLevel The level of the current header
     * @return The level of the parent header (or 0 if no parent found)
     */
    private static int findParentLevel(Map<Integer, CustomContentNodeDTO> headerStack, int currentLevel) {
        // Look for the closest level that is higher in hierarchy (smaller number)
        for (int i = currentLevel - 1; i >= 1; i--) {
            if (headerStack.containsKey(i)) {
                return i;
            }
        }
        return 0; // No parent found
    }



    public static void main(String[] args) {
        // 创建一些测试用的 CustomContentHeaderNode 对象
        List<CustomContentHeaderNode> headerNodes = getTestCustomContentHeaderNodeList();

        // 转换为 CustomContentNodeDTO 树结构
        List<CustomContentNodeDTO> customContentNodes = HeaderNodeToCustomContentTreeConverter.convertToCustomContentNodeTree(headerNodes);

        // 打印树结构
        log.debug("转换后的树结构：");
        printCustomContentNodeTree(customContentNodes, 0);

        // 添加一个特殊情况测试：跳级（从h1直接到h3）
        log.debug("\n测试特殊情况 - 跳级：");
        List<CustomContentHeaderNode> specialCase = new ArrayList<>();
        specialCase.add(createCustomContentHeaderNode(10, "特殊章节", "h1", "id10"));
        specialCase.add(createCustomContentHeaderNode(11, "直接到h3", "h3", "id11"));
        specialCase.add(createCustomContentHeaderNode(12, "再到h2", "h2", "id21"));
        specialCase.add(createCustomContentHeaderNode(13, "再回到h3", "h3", "id31"));


        List<CustomContentNodeDTO> specialResult = HeaderNodeToCustomContentTreeConverter.convertToCustomContentNodeTree(specialCase);
        printCustomContentNodeTree(specialResult, 0);

        // 测试空列表情况
        log.debug("\n测试空列表：");
        List<CustomContentNodeDTO> emptyResult = HeaderNodeToCustomContentTreeConverter.convertToCustomContentNodeTree(new ArrayList<>());
        log.debug("空列表结果大小: {}", emptyResult.size());

        // 测试直接h2
        log.debug("\n测试直接h2列表：");
        List<CustomContentHeaderNode> specialCaseH2 = new ArrayList<>();
        specialCaseH2.add(createCustomContentHeaderNode(10, "特殊章节h2", "h2", "id10"));
        specialCaseH2.add(createCustomContentHeaderNode(11, "到h1", "h1", "id11"));
        specialCaseH2.add(createCustomContentHeaderNode(12, "再到h2", "h2", "id21"));
        specialCaseH2.add(createCustomContentHeaderNode(13, "再回到h3", "h3", "id31"));

        log.debug("测试直接h2列表结果: ");
        List<CustomContentNodeDTO> special2Result = HeaderNodeToCustomContentTreeConverter.convertToCustomContentNodeTree(specialCaseH2);
        printCustomContentNodeTree(special2Result, 0);

    }

    private static List<CustomContentHeaderNode> getTestCustomContentHeaderNodeList() {
        List<CustomContentHeaderNode> headerNodes = new ArrayList<>();

        // 创建一个简单的层次结构：h1 -> h2 -> h3, h1 -> h2 (另一个)
        CustomContentHeaderNode h1To1 = createCustomContentHeaderNode(1, "第一章", "h1", "id1");
        CustomContentHeaderNode h2To1 = createCustomContentHeaderNode(2, "1.1 引言", "h2", "id2");
        CustomContentHeaderNode h3To1 = createCustomContentHeaderNode(3, "1.1.1 背景信息", "h3", "id3");
        CustomContentHeaderNode h2To2 = createCustomContentHeaderNode(4, "1.2 概述", "h2", "id4");
        CustomContentHeaderNode h1To2 = createCustomContentHeaderNode(5, "第二章", "h1", "id5");
        CustomContentHeaderNode h2To3 = createCustomContentHeaderNode(6, "2.1 详细说明", "h2", "id6");
        CustomContentHeaderNode h3To2 = createCustomContentHeaderNode(7, "2.1.1 关键点", "h3", "id7");
        CustomContentHeaderNode h3To3 = createCustomContentHeaderNode(8, "2.1.2 次要点", "h3", "id8");
        CustomContentHeaderNode h2To4 = createCustomContentHeaderNode(9, "2.2 总结", "h2", "id9");

        // 添加到列表中 - 按顺序添加
        headerNodes.add(h1To1);
        headerNodes.add(h2To1);
        headerNodes.add(h3To1);
        headerNodes.add(h2To2);
        headerNodes.add(h1To2);
        headerNodes.add(h2To3);
        headerNodes.add(h3To2);
        headerNodes.add(h3To3);
        headerNodes.add(h2To4);
        return headerNodes;
    }

    // 辅助方法：创建一个CustomContentHeaderNode对象
    private static CustomContentHeaderNode createCustomContentHeaderNode(int key, String text, String type, String id) {
        return new CustomContentHeaderNode(key, text, type, id);
    }

    // 辅助方法：递归打印树结构
    private static void printCustomContentNodeTree(List<CustomContentNodeDTO> nodes, int level) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        String indent = "  ".repeat(level);

        for (CustomContentNodeDTO node : nodes) {
            log.debug("{}{} - {} (ID: {})", indent, node.getType(), node.getText(), node.getId());

            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                printCustomContentNodeTree(node.getChildren(), level + 1);
            }
        }
    }
}
