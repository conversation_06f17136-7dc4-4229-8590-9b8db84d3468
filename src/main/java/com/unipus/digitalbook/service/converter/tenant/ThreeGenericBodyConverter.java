package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;

@Getter
public abstract class ThreeGenericBodyConverter<T, S, U> extends BaseBodyConverter implements BodyConverter<T, U> {

    private final TypeReference<T> oneTypeReference;
    private final TypeReference<S> twoTypeReference;
    private final TypeReference<U> threeTypeReference;

    protected ThreeGenericBodyConverter(TypeReference<T> oneTypeReference,
                                     TypeReference<S> twoTypeReference,
                                     TypeReference<U> threeTypeReference) {
        this.oneTypeReference = oneTypeReference;
        this.twoTypeReference = twoTypeReference;
        this.threeTypeReference = threeTypeReference;
    }

    public U convert(T one) throws Exception {

        S two = convert(one, getTwo(), this::oneToTwo);

        return convert(two, getThree(), this::twoToThree);

    }

    public abstract S oneToTwo(T one);

    public abstract U twoToThree(S two);

    public TypeReference<S> getTwo() {
        return twoTypeReference;
    }

    public TypeReference<U> getThree() {
        return threeTypeReference;
    }
}
