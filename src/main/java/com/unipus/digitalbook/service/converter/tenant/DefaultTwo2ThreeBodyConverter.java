package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;

public abstract class DefaultTwo2ThreeBodyConverter<T, S, U> extends ThreeGenericBodyConverter<T, S, U>{
    protected DefaultTwo2ThreeBodyConverter(TypeReference<T> oneTypeReference, TypeReference<S> twoTypeReference, TypeReference<U> threeTypeReference) {
        super(oneTypeReference, twoTypeReference, threeTypeReference);
    }

    @Override
    public U twoToThree(S two) {
        return null;
    }
}
