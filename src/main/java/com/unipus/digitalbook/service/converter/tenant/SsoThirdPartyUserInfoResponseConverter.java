package com.unipus.digitalbook.service.converter.tenant;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.service.remote.restful.sso.response.SsoUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * SSO服务用户信息响应转换器
 * 将第三方服务响应转换为统一的ThirdPartyUserInfoResponse格式
 */
@Component("ssoThirdPartyUserInfoResponseConverter")
@Slf4j
public class SsoThirdPartyUserInfoResponseConverter implements BodyConverter<String, Response<ThirdPartyUserInfo>> {

    @Override
    public Response<ThirdPartyUserInfo> convert(String source) throws Exception {
        if (!StringUtils.hasText(source)) {
            return Response.fail("SSO服务响应为空");
        }
        try {
            // 不同租户可以通过配置不同的转换器来处理不同的响应格式
            SsoUserResponse ssoUserResponse = JsonUtil.parseObject(source, SsoUserResponse.class);
            if (ssoUserResponse == null) {
                return Response.fail("解析SSO服务响应失败");
            }
            Boolean status = ssoUserResponse.getStatus();
            if (Boolean.FALSE.equals(status)) {
                log.error("SSO服务获取用户信息返回错误，code: {}, msg: {}", ssoUserResponse.getCode(), ssoUserResponse.getMsg());
                return Response.fail("SSO服务获取用户信息返回错误: " + ssoUserResponse.getMsg());
            }
            // 转换为统一的用户信息格式
            SsoUserResponse.Rs rs = ssoUserResponse.getRs();
            if (rs == null) {
                return Response.fail("SSO服务返回的用户信息为空");
            }
            ThirdPartyUserInfo userInfo = new ThirdPartyUserInfo();
            userInfo.setNickName(rs.getNickname());
            userInfo.setFullName(rs.getFullname());
            userInfo.setUserName(rs.getUsername());
            userInfo.setMobile(rs.getMobile());
            userInfo.setEmail(rs.getEmail());
            return Response.success(userInfo);
        } catch (Exception e) {
            log.error("转换SSO用户信息响应失败", e);
            return Response.fail("转换SSO用户信息响应失败: " + e.getMessage());
        }
    }
}
