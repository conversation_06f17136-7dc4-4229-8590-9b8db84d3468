package com.unipus.digitalbook.service.converter.tenant;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO服务用户信息请求转换器
 * 将请求参数转换为不同租户所需的请求格式
 */
@Component("ssoThirdPartyUserInfoRequestConverter")
public class SsoThirdPartyUserInfoRequestConverter implements BodyConverter<Map<String, String>, Map<String, String>> {

    @Override
    public Map<String, String> convert(Map<String, String> source) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("userid", source.get("openId"));
        return params;
    }
}
