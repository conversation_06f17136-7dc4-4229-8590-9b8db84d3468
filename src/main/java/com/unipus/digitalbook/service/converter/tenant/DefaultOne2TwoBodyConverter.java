package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;

public abstract class DefaultOne2TwoBodyConverter<T, S, U> extends ThreeGenericBodyConverter<T, S, U>{
    protected DefaultOne2TwoBodyConverter(TypeReference<T> oneTypeReference, TypeReference<S> twoTypeReference, TypeReference<U> threeTypeReference) {
        super(oneTypeReference, twoTypeReference, threeTypeReference);
    }

    @Override
    public S oneToTwo(T one) {
        return null;
    }
}
