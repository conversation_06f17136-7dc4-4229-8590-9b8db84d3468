package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;

public class DefaultTwoGenericBodyConverter<S, T> extends TwoGenericBodyConverter<S, T>{

    public DefaultTwoGenericBodyConverter(TypeReference<S> oneTypeReference, TypeReference<T> twoTypeReference) {
        super(oneTypeReference, twoTypeReference);
    }

    @Override
    public T o2o(S s) {
        return null;
    }
}
