package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.model.entity.action.UserContentProgressData;
import com.unipus.digitalbook.model.entity.paper.PaperAnswerNodeData;
import com.unipus.digitalbook.model.entity.question.UserAnswerNodeData;
import com.unipus.digitalbook.model.events.BookPublishEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BodyConverterFactory {

    @Bean
    public DefaultTwoGenericBodyConverter<BookPublishEvent, String> publishBookRequestConverter() {
        return new DefaultTwoGenericBodyConverter<>(new TypeReference<>() {}, new TypeReference<>() {});
    }

    @Bean
    public DefaultTwoGenericBodyConverter<UserAnswerNodeData, String> pushUserAnswerRequestConverter() {
        return new DefaultTwoGenericBodyConverter<>(new TypeReference<>() {}, new TypeReference<>() {});
    }

    @Bean
    public DefaultTwoGenericBodyConverter<PaperAnswerNodeData, String> pushPagerAnswerRequestConverter() {
        return new DefaultTwoGenericBodyConverter<>(new TypeReference<>() {}, new TypeReference<>() {});
    }

    @Bean
    public DefaultTwoGenericBodyConverter<UserContentProgressData, String> pushUserProgressRequestConverter() {
        return new DefaultTwoGenericBodyConverter<>(new TypeReference<>() {}, new TypeReference<>() {});
    }

}
