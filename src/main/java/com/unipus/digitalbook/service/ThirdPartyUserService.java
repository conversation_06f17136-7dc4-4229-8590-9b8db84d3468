package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;

/**
 * 第三方用户服务接口
 *
 * <AUTHOR>
 * @date 2025年06月23日 18:55:16
 */
public interface ThirdPartyUserService {

    /**
     * 根据OpenId获取用户信息
     *
     * @param tenantId 租户ID
     * @param openId   用户OpenId
     * @return 第三方用户信息实体
     */
    ThirdPartyUserInfo getUserInfoByOpenId(Long tenantId, String openId);

    /**
     * 根据第三方用户ID获取用户信息
     *
     * @param userId 第三方用户ID
     * @return 第三方用户信息实体
     */
    ThirdPartyUserInfo getUserInfoByUserId(Long userId);

    /**
     * 保存第三方用户信息
     *
     * @param thirdPartyUserInfo 第三方用户信息实体
     * @param userId             用户ID
     * @return 第三方用户ID
     */
    String saveThirdPartyUser(ThirdPartyUserInfo thirdPartyUserInfo, Long userId);
}
