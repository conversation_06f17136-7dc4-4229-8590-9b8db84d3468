package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.book.BookFile;

import java.util.List;

public interface BookFileService {
    /**
     * 批量添加书文件记录
     *
     * @param bookFiles 书文件对象列表
     * @param creatorId 创建者ID
     * @return 操作是否成功
     */
    Boolean addBookFiles(List<BookFile> bookFiles, Long creatorId);

    /**
     * 批量删除书文件记录
     *
     * @param ids 书文件ID列表
     * @return 操作是否成功
     */
    Boolean deleteBookFiles(List<Long> ids);

    /**
     * 根据书ID查询书的所有上传的文件记录
     *
     * @return 书文件对象列表
     */
    List<BookFile> getAllBookFileListByBookId(String bookId);

}



