package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceList;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;

import java.util.List;

/**
 * 配套资源相关业务服务接口
 */
public interface ComplementResourceService {

    /**
     * 添加配套资源信息
     */
    String addResource(ComplementResource complementResource);

    /**
     * 根据主键ID获取资源信息
     *
     * @param id 主键ID
     * @return ComplementResource 对象，如果未找到则返回null
     */
    ComplementResource getResourceById(Long id);

    /**
     * 保存配套资源信息
     */
    Long saveResource(ComplementResource complementResource);

    /**
     * 更新配套资源名字
     */
    Boolean updateName(String resourceId, String name, Long opUserId);

    /**
     * 查询配套资源信息
     */
    ComplementResourceList search(String bookId, Integer resourceType, Integer visibleStatus, PageParams pageParams);

    /**
     * 删除配套资源
     */
    Boolean delete(String resourceId, Long opUserId);

    /**
     * 更新配套资源可见状态
     */
    Boolean updateVisibleStatus(String resourceId, Integer visibleStatus, Long opUserId);

    /**
     * 根据教材ID查询配套资源列表
     * @param bookId
     * @return
     */
    ComplementResourceList getResourceByBookId(String bookId);

    /**
     * 生成教材配套资源版本
     * @return 返回生成的版本资源列表
     */
    ComplementResourceList generateVersionResource(String bookId);
    /**
     * 添加配套资源关系
     * @param complementResourceReference 教材与配套资源引用实体参数
     * @param opUserId 操作用户id
     * @return 添加成功返回引用ID，失败返回Null
     */
    Long addReference(ComplementResourceReference complementResourceReference, Long opUserId);

    /**
     * 删除配套资源关系
     * @param referenceId 教材与配套资源引用ID
     * @param opUserId 操作人ID
     * @return 删除成功返回true，失败返回false
     */
    Boolean removeReference(Long referenceId, Long opUserId);

    /**
     * 更新配套资源关系
     * @param complementResourceReference 教材与配套资源引用实体参数
     * @param opUserId 操作人ID
     */
    Boolean updateReference(ComplementResourceReference complementResourceReference, Long opUserId);
    /**
     * 取得配套资源关系
     * @param complementResourceReference 教材与配套资源引用实体参数
     * @return 配套资源关系列表
     */
    List<ComplementResourceReference> getReference(ComplementResourceReference complementResourceReference);

    /**
     * 根据教材版本ID,获取配套资源列表
     *
     * @param bookVersionId 教材版本ID
     * @return 配套资源列表
     */
    List<ComplementResource> getResourceByBookVersionId(Long bookVersionId);
}
