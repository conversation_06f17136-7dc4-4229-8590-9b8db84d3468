package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import com.unipus.digitalbook.model.entity.paper.PaperVersion;
import com.unipus.digitalbook.model.entity.publish.BookVersion;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 试卷版本服务接口
 */
public interface PaperVersionService {

    /**
     * 根据试卷主键ID查询所有试卷
     *
     * @param paperPrimaryIds 试卷主键ID列表
     * @return 试卷列表
     */
    List<Paper> getPaperList(List<Long> paperPrimaryIds);

    /**
     * 根据教材版本ID查询所有试卷
     *
     * @param bookVersionId 教材版本ID
     * @return 试卷列表
     */
    List<Paper> getPaperList(Long bookVersionId);

    /**
     * 根据章节版本ID列表，获取试卷列表
     *
     * @param chapterVersionIds 章节版本ID列表
     * @param bookId 教材ID
     * @param bookVersionId 教材版本ID，可为null
     * @return 试卷列表
     */
    Map<Long, List<Paper>> getPaperChapterVersionMapping(List<Long> chapterVersionIds, String bookId, Long bookVersionId);

    /**
     * 复制教材中试卷为指定版本
     * 包含复制试卷、题库、题目、题库策略、诊断卷推荐题关系、教材章节试卷引用
     * 其中题目复制，包含题组和题目
     * @param paperIds 试卷ID列表
     * @param userId 用户ID
     * @return 试卷发布版本信息列表
     */
    List<PaperVersion>  copyPaperWithVersion(Set<String> paperIds, Long userId) ;

    /**
     * 获取试卷是否已上架
     *
     * @param paperIds 试卷ID列表
     * @return 试卷是否已上架Map
     */
    Map<String, Boolean> isPublishedPaper(List<String> paperIds);

    /**
     * 获取试卷的最新发布版本
     *
     * @param paperIds 试卷ID列表
     * @return 试卷列表
     */
    List<Paper> getLastPublishedPaperList(List<String> paperIds);

    /**
     * 根据教材ID/教材版本/试卷ID取得试卷版本
     * @param bookId 教材ID
     * @param bookVersionNumber 教材版本
     * @param paperId 试卷ID
     * @return 试卷版本
     */
    PaperVersion getPaperVersionByBookInfo(String bookId, String bookVersionNumber, String paperId);

    /**
     * 根据试卷ID及版本获取关联的教材ID和版本
     *
     * @param paperId            试卷ID
     * @param paperVersionNumber 试卷版本
     * @return 教材版本信息
     */
    BookVersion getBookInfoByPaperVersion(String paperId, String paperVersionNumber);

    /**
     * 根据教材版本ID取得试卷版本信息
     *
     * @param bookVersionId     教材版本ID
     * @return 试卷版本信息列表
     */
    List<PaperSyncInfo> getPaperSyncInfoByBookVersionId(Long bookVersionId);
}
