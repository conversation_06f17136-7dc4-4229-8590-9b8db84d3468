package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeNode;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.*;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.GraphNodeInfoResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeAddResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeDetailResponse;

/**
 * 图谱创建相关接口
 */
@Deprecated
public interface KnowledgeGraphService {


    /**
     * 子图详细内容
     *
     * @param graphId
     * @return
     */
    Response<GraphNodeInfoResponse> knowledgeGraphNodeList(String graphId);

    /**
     * 新增根节点
     *
     * @param params
     * @return knoledgeId
     */
    Response<KnowledgeNodeAddResponse> knowledgeBaseNodeAdd(KnowledgeNodeBaseAddRequest params);

    /**
     * 更新根节点
     *
     * @param params
     * @return null
     */
    Response knowledgeBaseNodeUpdate(KnowledgeNodeUpdateRequest params);


    /**
     * 新增同级知识点
     *
     * @param params
     * @return
     */
    Response<String> knowledgeNodeSameLevelAdd(KnowledgeNode params);


    /**
     * 新增子级知识点
     *
     * @param params 知识图谱节点参数
     * @return 知识图谱节点ID
     */
    Response<String> knowledgeSubNodeAdd(KnowledgeNode params);

    /**
     * 更新知识点
     *
     * @param params 知识图谱节点参数
     * @return 更新结果
     */
    Response knowledgeNodeUpdate(KnowledgeNode params);

    /**
     * 移动知识点
     *
     * @param params 知识图谱节点参数
     * @return 移动结果
     */
    Response<String> knowledgeNodeMove(KnowledgeNodeMoveRequest params);

    /**
     * 删除知识点
     *
     * @param params 知识图谱节点ID
     * @return 删除结果
     */
    Response knowledgeNodeDelete(KnowledgeNodeDeleteRequest params);

    /**
     * 查看知识点
     *
     * @param nodeId 知识图谱节点ID
     * @return 知识图谱节点信息
     */
    Response<KnowledgeNodeDetailResponse> knowledgeNodeDetail(String nodeId);

}
