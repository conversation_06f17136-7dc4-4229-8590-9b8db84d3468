package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.events.ReadingTimeEvent;

/**
 * 阅读时长服务接口
 */
public interface ReadingTimeService {

    /**
     * 开始阅读记录
     *
     * @param openId    用户ID
     * @param bookId    书籍ID
     * @param chapterId 章节ID
     * @param tenantId  租户ID
     * @return 是否成功
     */
    Boolean startReading(String openId, String bookId, String chapterId, Long tenantId);

    /**
     * 记录心跳数据
     *
     * @param openId    用户ID
     * @param bookId    书籍ID
     * @param chapterId 章节ID
     * @param tenantId  租户ID
     * @return 是否成功
     */
    Boolean recordHeartbeat(String openId, String bookId, String chapterId, Long tenantId);

    /**
     * 结束阅读记录
     *
     * @param openId    用户ID
     * @param bookId    书籍ID
     * @param chapterId 章节ID
     */
    void endReading(String openId, String bookId, String chapterId, long startTime, Long lastActive, Long tenantId);

    /**
     * 结束所有未结束的阅读记录
     */
    void endAllNoHeartbeat();

    /**
     * 保存阅读记录并计算个人章节总阅读时长
     *
     * @param readingTimeEvent 阅读时长事件
     */
    void saveRecordAndCalTotalTime(ReadingTimeEvent readingTimeEvent);

//    /**
//     * 获取用户在指定书籍的阅读记录
//     * @param userId 用户ID
//     * @param bookId 书籍ID
//     * @return 阅读记录列表
//     */
//    List<ReadingTimeRecord> getUserBookReadingRecords(Long userId, String bookId);
//
//    /**
//     * 获取用户在指定章节的阅读记录
//     * @param userId 用户ID
//     * @param bookId 书籍ID
//     * @param chapterId 章节ID
//     * @return 阅读记录列表
//     */
//    List<ReadingTimeRecord> getUserChapterReadingRecords(Long userId, String bookId, String chapterId);
//
//    /**
//     * 获取用户在指定章节的阅读时长统计
//     * @param userId 用户ID
//     * @param bookId 书籍ID
//     * @param chapterId 章节ID
//     * @return 阅读时长统计
//     */
//    ReadingTimeDuration getUserChapterDuration(Long userId, String bookId, String chapterId);
//
//    /**
//     * 获取用户在指定书籍的总阅读时长
//     * @param userId 用户ID
//     * @param bookId 书籍ID
//     * @return 总阅读时长（秒）
//     */
//    Long getUserBookTotalDuration(Long userId, String bookId);
//
//    /**
//     * 获取用户未结束的阅读记录
//     * @param userId 用户ID
//     * @param tenantId 租户ID
//     * @return 未结束的阅读记录列表
//     */
//    List<ReadingTimeRecord> getUnfinishedReadingRecords(Long userId, Long tenantId);
//
//    /**
//     * 清理超时的未结束记录
//     * @param timeoutMinutes 超时时间（分钟）
//     * @return 清理的记录数
//     */
//    Integer cleanupTimeoutRecords(Integer timeoutMinutes);
}