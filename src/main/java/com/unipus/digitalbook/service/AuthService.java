package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.user.BackendUserDTO;
import com.unipus.digitalbook.model.dto.user.LoginUserDTO;
import com.unipus.digitalbook.model.entity.BackendUserInfo;
import com.unipus.digitalbook.model.params.DoAuthParam;

import javax.naming.NoPermissionException;

/**
 * <AUTHOR>
 * @date  2024/10/16 下午5:08
 */
public interface AuthService {
    /**
     * 进行身份验证并获取服务票据（Service Ticket）授权。
     *
     * @param serviceTicket        服务票据，通常由客户端在访问服务时提供，用于验证用户身份和授权信息。
     * @param ticketGrantingTicket 票据授权票据，通常由CAS服务器在用户首次登录时生成，并用于后续的服务票据生成和验证。
     * @return Response 返回一个Response对象，其中包含了身份验证和授权的结果。
     * - 如果身份验证和授权成功，Response对象的状态码（code）将为成功状态（如200），响应消息（message）将为成功消息，响应数据（data）可能包含授权后的用户信息或其他相关数据。
     * - 如果身份验证或授权失败，Response对象的状态码将为失败状态（如401或403），响应消息将为失败原因，响应数据可能为null或包含错误详细信息。
     */
    Response<LoginUserDTO> auth(String serviceTicket, String ticketGrantingTicket);

    /**
     * 验证登录参数的有效性
     *
     * @param param 登录参数对象，包含服务票据和授权票据
     * @return 响应对象，包含状态码、响应消息和响应数据
     */
    Response<LoginUserDTO> validLogin(DoAuthParam param);

    Response<String> generateToken4CoEdit(Long userId, String bookId) throws NoPermissionException;

    /**
     * 获取后端用户token
     * @param param 内部认证参数类
     * @return 后端用户token
     */
    Response<BackendUserDTO> getBackendAccessToken(BackendUserInfo param);


    /**
     * 解析后端用户 token
     *
     * @param token 后端用户 token
     * @return 后端用户信息
     */
    Response<BackendUserInfo> parseBackendAccessToken(String token);

}
