package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.Organization;
import jakarta.annotation.Nullable;

import java.util.List;
import java.util.Map;

/**
 * 组织服务接口
 */
public interface OrgService {

    /**
     * 检查用户是否存在于指定组织中
     *
     * @param userId 用户ID
     * @param orgId  组织ID
     * @return 如果用户存在于指定组织中，返回true；否则返回false
     */
    Boolean userExistInOrg(Long userId, Long orgId);

    /**
     * 根据ID检查机构是否存在
     *
     * @param id 机构ID
     * @return 如果实体存在，返回true；否则返回false
     */
    Boolean existById(Long id);

    /**
     * 取得组织树
     * @param parentOrgId 指定父级组织ID（作为组织树的根节点）
     * @param subLevel 子节点层级数量(0:全部，1:一级子节点，2:二级子节点，以此类推)
     * @param status 状态，1:启用，0:禁用
     * @return 机构列表
     */
    List<Organization> getOrganizationTree(Long parentOrgId, Integer subLevel, Integer status);

    /**
     * 查询符合条件的所有机构
     * @param name   机构名称
     * @param status 机构状态
     * @param offset 偏移量
     * @param limit  每页数量
     * @return 机构列表
     */
    List<Organization> searchOrganizations(String name, Integer status, int offset, int limit);

    /**
     * 查询符合条件的所有机构的数量
     * @param name   机构名称
     * @param status 机构状态
     * @return 机构数量
     */
    Integer countOrganizations(String name, Integer status);

    /**
     * 新建一级机构
     * 超管的系统管理中，新建根节点的机构
     *
     * @param param 新建一级机构参数
     * @return 操作是否成功
     */
    Boolean addTopOrganization(Organization param, Long opUserId);

    /**
     * 新建下级机构
     * 超管的系统管理中，新建非一级机构
     *
     * @param param 新建下级机构参数
     * @return 操作是否成功
     */
    Boolean addSubOrganization(Organization param, Long opUserId);

    /**
     * 编辑机构
     * 超管的系统管理中，编辑机构，涉及到移动组织操作
     *
     * @param organization 编辑机构参数
     * @return 操作是否成功
     */
    Boolean editOrganization(Organization organization, Long opUserId);

    /**
     * 批量变更机构状态
     * <p>
     * 在超管的系统管理中，此方法用于批量变更指定机构的状态。
     *
     * @param idList   需要变更状态的机构ID列表
     * @param status   目标状态
     * @param opUserId 操作用户的ID
     * @return 操作是否成功
     */
    Boolean batchChangeOrganizationStatus(List<Long> idList, Integer status, Long opUserId);

    /**
     * 用户加入组织
     *
     * @param userId   用户ID
     * @param orgId    组织ID
     * @param opUserId 操作用户ID（可能是管理员或其他有权操作的用户）
     * @return 如果用户成功加入组织，返回true；否则返回false
     */
    Boolean joinOrganization(Long userId, Long orgId, Long opUserId);


    /**
     * 根据用户ID列表获取用户到组织的映射关系
     *
     * @param userIdList 用户ID列表
     * @return 返回一个Map，键为用户ID，值为该用户所属的组织列表
     */
    Map<Long, List<Organization>> getUser2OrgMap(List<Long> userIdList,Long orgId);

    /**
     * 取得组织详情
     * @param orgId 组织ID
     * @return 组织详情
     */
    @Nullable
    Organization getOrgDetail(Long orgId);

    /**
     * 根据用户ID获取用户所属的组织列表
     * @param userId 用户ID
     * @return 用户所属的组织列表
     */
    List<Organization> getUserOrgs(Long userId);


    /**
     * 批量删除用户和组织关系
     *
     * @param userIdList 用户ID列表
     * @param orgId      组织ID
     * @param opUserId   操作者ID
     * @return 返回布尔值，指示操作是否成功
     */
    Boolean batchDeleteRelationsByUserIdListAndOrgId(List<Long> userIdList,Long orgId,Long opUserId);


    /**
     * 根据组织ID列表获取组织实体列表
     *
     * @param orgIdList 组织ID列表，用于指定需要检索的组织
     * @return 返回一个组织实体列表，如果找不到任何组织则返回空列表
     */
    List<Organization> getOrganizationByIdList(List<Long> orgIdList);
}
