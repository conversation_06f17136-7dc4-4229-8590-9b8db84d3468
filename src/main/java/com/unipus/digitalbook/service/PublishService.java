package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.publish.BookPublishOrder;

import java.util.HashMap;

/**
 * 教材上架服务
 */
public interface PublishService {

    /**
     * 教材上架
     *
     * @param bookPublishInfos 教材上架检测信息参数
     * @param userId           用户ID
     * @return 提交结果
     */
    HashMap<String, String> publish(BookPublishOrder bookPublishInfos, Long userId);
}
