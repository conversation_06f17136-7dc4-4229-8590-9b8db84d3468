package com.unipus.digitalbook.service;

import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeInfo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * cms2二维码服务接口
 *
 * <AUTHOR>
 * @date 2025/2/20 14:05
 */
public interface Cms2QrCodeService {

    /**
     * 根据用户ID获取cms2的token
     *
     * @param userId 用户ID
     * @return 生成的token字符串
     */
    String genToken(Long userId);

    /**
     * 生成二维码列表
     *
     * @param bookId    教材ID
     * @param quantity  生成数量
     * @param pixelSize 二维码尺寸
     * @return 返回生成的二维码信息列表
     */
    List<QrCodeInfo> genQrCodes(String bookId, Integer quantity, Integer pixelSize);

    /**
     * 更新二维码信息
     *
     * @param id           二维码ID
     * @param bookId       教材ID
     * @param bookInnerUrl 教材内部链接
     * @param remarks      备注
     * @return 更新后的二维码信息对象
     */
    QrCodeInfo updateQrCode(Long id, String bookId, String bookInnerUrl, String remarks);

    /**
     * 查看二维码
     *
     * @param id       二维码ID
     * @param response HTTP响应对象
     */
    void displayQrCode(Long id, HttpServletResponse response);

    /**
     * 下载二维码
     *
     * @param id       二维码ID
     * @param response HTTP响应对象
     */
    void downloadQrCode(Long id, HttpServletResponse response);

    /**
     * 打包下载二维码
     *
     * @param ids      二维码ID列表
     * @param response HTTP响应对象
     */
    void downloadQrCodeZip(List<Long> ids, HttpServletResponse response);
}
