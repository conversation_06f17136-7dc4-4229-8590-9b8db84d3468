package com.unipus.digitalbook.service;


import com.unipus.digitalbook.model.entity.book.BookTheme;
import com.unipus.digitalbook.model.entity.book.Theme;

import java.util.List;

/**
 * 教材主题服务
 */
public interface BookThemeService {

    /**
     * 新增主题
     * @param theme 主题实体
     * @param userId 用户id
     * @return 新增主题ID
     */
    Long createTheme(Theme theme, Long userId);

    /**
     * 编辑主题
     * @param theme 主题实体
     * @param userId 用户id
     * @return 保存结果
     */
    Boolean editTheme(Theme theme, Long userId);

    /**
     * 查询主题列表
     * @param theme 主题
     * @return 主题列表
     */
    List<Theme> getThemeList(Theme theme);

    /**
     * 获取主题列表
     * @param ids 主题id列表
     * @return 主题实体列表
     */
    List<Theme> getThemeByIds(List<Long> ids);

    /**
     * 新增教材主题
     * @param bookTheme 教材主题实体
     * @param userId 用户id
     * @return 新增教材主题ID
     */
    Long createBookTheme(BookTheme bookTheme, Long userId);

    /**
     * 编辑教材主题
     * @param bookTheme 教材主题实体
     * @param userId 用户id
     * @return 保存结果
     */
    Boolean editBookTheme(BookTheme bookTheme, Long userId);

    /**
     * 查询教材主题
     * @param bookTheme 教材主题实体
     * @return 主题列表
     */
    List<BookTheme> getBookThemes(BookTheme bookTheme);
}