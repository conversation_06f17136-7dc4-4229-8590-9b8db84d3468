package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.entity.tag.TagResource;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.model.enums.TagTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 标签服务接口
 */
public interface TagService {

    /**
     * 保存题目标签列表及标签关系
     * @param questionTags 标签对象列表
     * @param userId 用户ID
     * @return 标签ID
     */
    Boolean saveQuestionTagList(List<QuestionTag> questionTags, Long userId);

    /**
     * 保存标签
     * @param tag  标签对象
     * @param parent 父标签ID
     * @param userId 用户ID
     * @return 标签ID
     */
    Long saveTag(Tag tag, Long parent, Long userId);

    /**
     * 保存标签列表
     * @param tagList 标签对象列表
     * @param resource 资源对象
     * @param userId 用户ID
     */
    void saveTagListToResource(List<Tag> tagList, TagResource resource, Long userId);

    /**
     * 根据标签类型获取顶级标签
     * @param tagType 标签类型
     * @return 标签对象
     */
    List<Tag> getTopLevelTagListByType(TagTypeEnum tagType);

    /**
     * 获取标签列表
     * @param tagIds 标签ID列表
     * @param tagType 标签类型
     * @return 标签对象
     */
    List<Tag> getTagList(List<Long> tagIds, TagTypeEnum tagType);

    /**
     * 根据ID列表查询标签列表（包含所有层级子标签）
     * @param tagIds 标签ID列表
     * @param tagType 标签类型
     * @return 标签对象
     */
    List<Tag> recursionQueryTagList(List<Long> tagIds, TagTypeEnum tagType);

    /**
     * 通过资源列表取得标签列表
     * @param resourceIds 资源ID列表
     * @param version 资源版本
     * @return 标签对象
     */
    Map<String, List<Tag>> getTagListByResourceIdsAndVersion(List<String> resourceIds, TagResourceTypeEnum resourceType, String version);
}
