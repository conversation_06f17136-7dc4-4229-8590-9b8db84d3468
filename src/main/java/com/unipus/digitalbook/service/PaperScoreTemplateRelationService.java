package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateRelation;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;

import java.util.List;

public interface PaperScoreTemplateRelationService {

    boolean addTemplateRelation(List<PaperScoreTemplateRelation> paperScoreTemplateRelationList, Long createBy);

    boolean editTemplateRelation(List<PaperScoreTemplateRelation> paperScoreTemplateRelationList, Long createBy);

    PaperScoreTemplateList getTemplateRelationList(String bookId);

    PaperScoreTemplate getPaperScoreTemplate(String bookId, PaperScoreTemplateTypeEnum scoreTemplateType);
}
