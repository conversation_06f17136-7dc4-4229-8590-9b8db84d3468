package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;

/**
 * 试卷实例服务接口
 */
public interface PaperInstanceService {

    /**
     * 生成预览模式试卷
     * @param param 试卷实例参数
     *              paperId 试卷ID
     *              paperType 试卷类型
     *              openId 用户ssoID
     *              tenantId 租户ID
     *              testMode 试卷模式
     * @return 试卷实例
     */
    PaperInstance generatePreviewPaperInstance(PaperInstance param);

    /**
     * 生成实际作答试卷
     * @param param 试卷实例参数
     *              paperId 试卷ID
     *              versionNumber 版本号
     *              paperType 试卷类型
     *              openId 用户ssoID
     *              tenantId 租户ID
     *              testMode 试卷模式
     *              instanceId 试卷实例ID/轮次ID (可以为空)
     * @return 试卷实例
     */
    PaperInstance generateRealPaperInstance(PaperInstance param);

    /**
     * 获取用户特定试卷的最近一次试卷实例信息
     *
     * @param param  试卷实例参数
     *               paperId 试卷ID
     *               versionNumber 版本号
     *               openId 用户ssoID
     *               tenantId 租户ID
     * @param submitStatus 试卷提交状态（0：未提交/1：已提交）
     * @return 用户作答记录
     */
    PaperInstance getLatestPaperInstance(PaperInstance param, Integer submitStatus);

    /**
     * 获取已有的试卷实例
     * @param instanceId 试卷实例ID/轮次ID
     * @param paperType 试卷类型
     * @return 试卷实例
     */
    PaperInstance loadPaperInstance(String instanceId, PaperTypeEnum paperType);

    /**
     * 获取试卷分析
     * @param instanceId 试卷实例ID/轮次ID
     * @return 试卷分析
     */
    PaperInstance getPaperAnalysis(String instanceId);

    /**
     * 获取用户特定试卷的最近一次提交的试卷版本
     * @param paperId 试卷ID
     * @param userAccessInfo 用户信息
     * @return 最近一次提交的试卷版本
     */
    String getLatestSubmittedPaperVersion(String paperId, UserAccessInfo userAccessInfo);

}
