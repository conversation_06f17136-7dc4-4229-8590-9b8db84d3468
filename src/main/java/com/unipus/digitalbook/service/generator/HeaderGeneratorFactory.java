package com.unipus.digitalbook.service.generator;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class HeaderGeneratorFactory {

    @Bean
    public HeaderGenerator headerGenerator() {
        return () -> Map.of(
                "Content-Type", "application/json",
                "Accept", "application/json"
        );
    }
}
