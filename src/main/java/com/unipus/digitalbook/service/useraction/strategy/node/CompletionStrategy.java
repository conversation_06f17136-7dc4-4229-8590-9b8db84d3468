package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;

import java.util.Set;

public interface CompletionStrategy {
    /**
     * 获取节点类型
     * @return
     */
    Set<String> getTypes();
    /**
     * 判断节点是否完成
     *
     * @param node
     * @param userAction
     * @return
     */
    boolean isCompleted(ContentNode node, UserAction userAction);

}
