package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class TextNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("base-paragraph", "list", "quote", "collapsible-block", "highlight-block");
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        if (node.getCjkWordCount() == null || node.getNonCjkWordCount() == null) {
            log.error("节点 {} 没有设置字符数", node.getId());
            return false;
        }
        Long duration = userAction.getDuration();
        if (duration == null || duration <= 0) return false;
        double required = 0;
        if (node.getCjkWordCount() != null) {
            // 600个字数=1分钟
            required += node.getCjkWordCount() / 600.0 * 60 * 1000;
        }
        if (node.getNonCjkWordCount() != null) {
            // 260个单词=1分钟
            required += node.getNonCjkWordCount() / 260.0 * 60 * 1000;
        }
        return duration >= required;
    }
}
