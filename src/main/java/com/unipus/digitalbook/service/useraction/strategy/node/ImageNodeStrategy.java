package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class ImageNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("insert-image");
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        return true;
    }
}
