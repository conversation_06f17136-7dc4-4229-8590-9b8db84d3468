package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

@Slf4j
@Component
public class HeaderNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("h1", "h2" ,"h3" ,"h4" ,"h5","h6" );
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        return false;
    }
}
