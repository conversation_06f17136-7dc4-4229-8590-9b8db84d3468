package com.unipus.digitalbook.service.useraction.strategy.content;

import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class UserContentActionFactory {
    private final Map<ContentTypeEnum, UserContentAction> USER_CONTENT_ACTION_MAP = new HashMap<>();

    @Autowired
    public UserContentActionFactory(List<UserContentAction> userContentActions) {
        for (UserContentAction action : userContentActions) {
            action.getTypes().forEach(type -> {
                log.debug("注册 action 节点类型：{}", type);
                USER_CONTENT_ACTION_MAP.put(type, action);
            });
        }
    }

    public UserContentAction getContentAction(ContentTypeEnum type) {
        return USER_CONTENT_ACTION_MAP.get(type);
    }
}
