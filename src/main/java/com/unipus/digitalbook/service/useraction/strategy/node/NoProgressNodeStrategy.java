package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class NoProgressNodeStrategy implements CompletionStrategy  {
    @Override
    public Set<String> getTypes() {
        return Set.of();
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        return false;
    }
}
