package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class VideoNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("insert-video");
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        if (node.getVideoDuration() == null) {
            log.error("节点 {} 没有设置时长", node.getId());
            return false;
        }
        Long duration = userAction.getDuration();
        if (duration == null || duration <= 0) return false;
        double required = node.getVideoDuration() * 0.1;
        return duration >= required;
    }
}
