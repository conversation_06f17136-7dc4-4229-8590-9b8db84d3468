package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.params.book.BookCollaboratorParam;
import com.unipus.digitalbook.model.params.book.CollaboratorUserParam;

import java.util.List;

/**
 * 教材权限接口
 */
public interface BookPermissionService {

    /**
     * 追加教材作者权限
     *
     * @param bookId    教材ID
     * @param creatorId 创建者ID
     * @return 是否追加成功
     */
    Boolean addBookOwnerPermission(String bookId, Long creatorId);

    /**
     * 查询当前组织非协作者用户
     *
     * @param param  查询参数
     * @param orgId  组织ID
     * @param userId 用户ID
     * @return 操作是否成功
     */
    List<ResourceUser> getNonCollaboratorUser(CollaboratorUserParam param, Long orgId, Long userId);

    /**
     * 查询教材的协作者
     *
     * @param param  查询参数
     * @param orgId  组织ID
     * @param userId 用户ID
     * @return 操作是否成功
     */
    List<ResourceUser> getBookCollaborator(CollaboratorUserParam param,Long orgId, Long userId);

    /**
     * 设置教材的协作者
     *
     * @param param 教材协作者参数
     * @param ownerId 教材作者用户ID
     * @return 操作是否成功
     */
    Boolean updateBookCollaborator(BookCollaboratorParam param, Long ownerId);


    /**
     * 判断用户是否有章节的编辑权限
     * @param userId
     * @param bookId
     * @param chapterId
     * @return
     */
    Boolean hasEditePermission(Long userId, String bookId, String chapterId);
}
