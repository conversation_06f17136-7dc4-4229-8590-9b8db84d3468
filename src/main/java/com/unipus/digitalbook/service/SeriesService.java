package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.Series;

import java.util.Collection;
import java.util.List;

/**
 * 系列业务接口
 */
public interface SeriesService {
    /**
     * 根据系列id获取系列信息
     * @param seriesId 系列id
     * @return 系列信息
     */
    Series getSeriesById(Long seriesId);

    /**
     * 根据系列id批量获取系列信息
     * @param seriesIds 系列id列表
     * @return 系列信息列表
     */
    List<Series> batchGetSeriesByIds(Collection<Long> seriesIds);

    /**
     * 获取所有系列信息
     * @return 系列信息列表
     */
    List<Series> getAllSeries();
}
