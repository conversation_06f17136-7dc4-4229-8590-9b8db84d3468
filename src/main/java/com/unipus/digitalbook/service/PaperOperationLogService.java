package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;

/**
 * 试卷操作日志服务接口
 */
public interface PaperOperationLogService {

    /**
     * 保存试卷操作日志
     * 1、当新增测试成功后，记录新增记录。显示内容：新增【类型】测试：【测试名称】
     * 6、当编辑测试名称成功后，记录编辑记录。显示内容：编辑【类型】测试的名称从“名称1”变更为“名称2”
     * @param oldPaper 旧试卷对象
     * @param newPaper 新试卷对象
     * @param userId 用户ID
     */
    void addPaperSaveOperation(Paper oldPaper, Paper newPaper, Long userId);

    /**
     * 删除试卷操作日志
     * 2、当删除测试测试成功后，记录删除记录。显示内容：删除【类型】测试：【测试名称】
     * @param paper 历史试卷对象
     * @param userId 当前用户ID
     */
    void addPaperDeleteOperation(Paper paper, Long userId);

    /**
     * 试卷内容操作日志
     * 7、当常规测试和诊断测试编辑时，点击保存按钮后，记录编辑测试记录，显示内容：编辑【类型】测试：【测试名称】
     * @param paper 试卷对象
     * @param userId 当前用户ID
     */
    void addPaperContentOperation(Paper paper, Long userId);

    /**
     * 保存题库操作日志
     * 3、当新增了知识挑战的题库，显示内容：新增题库：题库名称
     * 4、当编辑了题库的信息，显示内容：编辑题库信息，将题库名称从“名称1”变更为“名称2”。将题库每题分数由“值1”变更为“值2”，将每轮随机抽取题目数由“值1”变更为“值2”。
     * @param paper 试卷对象
     * @param questionBank 题库对象
     * @param oldQuestionBank 旧题库对象
     * @param userId 用户ID
     */
    void addBankSaveOperation(Paper paper, QuestionBank questionBank, QuestionBank oldQuestionBank, Long userId);

    /**
     * 删除题库操作日志
     * 5、当删除了题库成功后，记录日志。显示内容：删除题库：【题库名称】
     * @param questionBank 题库
     * @param userId 用户ID
     */
    void addBankDeleteOperation(QuestionBank questionBank, Long userId);

}
