package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.qrcode.QrCode;
import com.unipus.digitalbook.model.entity.qrcode.QrCodeList;
import com.unipus.digitalbook.model.enums.QrCodeSizeEnum;
import com.unipus.digitalbook.model.params.qrcode.QrCodeVerificationParam;
import com.unipus.digitalbook.model.params.qrcode.SearchQrCodeParam;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 二维码服务接口，定义二维码相关的业务操作
 */
public interface QrCodeService {

    /**
     * 批量创建二维码
     *
     * @param bookName 教材名称
     * @param bookId   教材ID
     * @param quantity 生成数量
     * @param sizeEnum 二维码尺寸
     * @return 创建的二维码列表
     */
    Boolean createQrCodes(String bookName, String bookId, Integer quantity, QrCodeSizeEnum sizeEnum, Long userId);

    /**
     * 更新二维码信息
     *
     * @param qrCode 二维码信息
     */
    Boolean updateQrCode(QrCode qrCode);

    /**
     * 删除二维码
     *
     * @param id     二维码ID
     * @param userId 用户ID
     * @return 删除是否成功
     */
    Boolean deleteQrCode(Long id, Long userId);

    /**
     * 根据ID获取二维码详情
     *
     * @param id 二维码ID
     * @return 二维码详情
     */
    QrCode getQrCodeById(Long id);

    /**
     * 分页查询二维码列表
     *
     * @param param 检索条件
     * @return 分页查询结果
     */
    QrCodeList searchQrCodes(SearchQrCodeParam param);

    /**
     * 验证二维码链接有效性
     *
     * @param results 二维码验证结果
     * @return 验证结果
     */
    Boolean verifyQrCodeLink(List<QrCodeVerificationParam.VerificationResult> results, Long userId);

    /**
     * 重新生成二维码图片
     *
     * @param id        二维码ID
     * @param pixelSize 二维码像素尺寸
     * @return 更新后的二维码信息
     */
    QrCode regenerateQrCode(Long id, Integer pixelSize);


    /**
     * 保存二维码图片
     *
     * @param id         二维码ID
     * @param imageBytes 图片字节数组
     * @return 图片访问URL
     */
    String saveQrCodeImage(Long id, byte[] imageBytes);

    /**
     * 导出二维码关系数据
     *
     * @param ids      二维码ID列表
     * @param userId   用户ID
     * @param response HTTP响应对象
     */
    void exportQrCodeRelational(List<Long> ids, Long userId, HttpServletResponse response);
}