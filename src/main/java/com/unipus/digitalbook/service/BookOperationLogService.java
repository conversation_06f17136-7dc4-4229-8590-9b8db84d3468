package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.book.AddBookOperationLog;
import com.unipus.digitalbook.model.entity.book.SearchOperationLogList;

import java.util.Date;

/**
 * <p>
 * 教材操作日志服务接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/16 14:23
 */
public interface BookOperationLogService {

    /**
     * 添加操作日志
     *
     * @param bookId           教材ID
     * @param operationUserId  操作用户ID
     * @param operationContent 操作内容
     * @param operationType    操作类型
     */
    void log(String bookId, Long operationUserId, String operationContent, Integer operationType);

    /**
     * 保存操作日志
     *
     * @param addBookOperationLog 描述添加图书操作的日志对象，包含操作的详细信息
     * @return 返回数据库生成的日志ID，用于唯一标识这条操作日志
     */
    Long saveOperationLogData(AddBookOperationLog addBookOperationLog);

    /**
     * 根据查询条件获取搜索操作日志列表
     *
     * @param bookId             教材ID
     * @param userName           操作用户名
     * @param operationStartTime 操作开始时间
     * @param operationEndTime   操作结束时间
     * @param pageParams         分页参数
     * @return 查询教材的操作日志列表，并返回操作日志的总数量
     */
    SearchOperationLogList searchOperationLogList(String bookId, String userName, Date operationStartTime, Date operationEndTime, PageParams pageParams);
}
