package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.PaginationResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.ResourceTag;

/**
 * 图谱创建相关接口
 */
@Deprecated
public interface KnowledgeLabelService {

    /**
     * 列表展示标签类型
     *
     * @return 标签类型列表响应
     */
    Response<PaginationResponse> labelList(String keyword, Integer pageNum, Integer pageSize);

    /**
     * 新增标签类型
     *
     * @param params 新增标签类型请求参数
     * @return 基础响应
     */
    Response<ResourceTag> labelAdd(ResourceTag params);

    /**
     * 更新标签类型
     *
     * @param params 更新标签类型请求参数
     * @return 基础响应
     */
    Response<ResourceTag> labelUpdate(ResourceTag params);

    /**
     * 删除标签类型
     *
     * @param labelId 标签类型ID
     * @return 基础响应
     */
    Response labelDelete(String labelId);


}
