package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.params.question.QuestionParseParam;

import java.util.List;
import java.util.Map;

/**
 * 题型服务接口
 */
public interface QuestionService {

    /**
     * 根据题型解析图片与文本内容
     *
     * @param param 参数
     * @return 解析后的内容，如果没有有效的内容则返回null
     */
    String questionParseContent(QuestionParseParam param);

    /**
     * 删除题组
     * @param groupId 组ID
     * @param userId        用户ID
     * @return 题组ID
     */
    Long deleteGroup(Long groupId, Long userId);


    /**
     * 保存大题
     * @param bigQuestions 大题列表
     * @return 答题ID列表
     */
    List<Long> batchSaveBigQuestions(List<BigQuestionGroup> bigQuestions);

    /**
     * 批量保存大题带删除
     * @param bigQuestions 大题列表
     * @param originBigQuestionIdMap 获取原来已有的的大题id列表，用于对比是否需要删除
     *                                  key：大题的业务id，value：大题id
     * @param userId        用户ID
     * @return 大题id列表
     */
    List<Long> batchSaveBigQuestionsWithDelete(List<BigQuestionGroup> bigQuestions, Map<String, Long> originBigQuestionIdMap, Long userId);

    /**
     * 保存大题
     * @param bigQuestion 大题
     * @return 答题ID
     */
    Long saveBigQuestion(BigQuestionGroup bigQuestion);
    /**
     * 批量删除大题
     * @param ids 大题ID
     * @return 是否删除成功
     */
    boolean batchDeleteBigQuestions(List<Long> ids, Long userId);
    /**
     * 根据业务id和版本获取大题
     *
     * @param bizBigQuestionId  业务ID
     * @param versionNumber 版本号 默认为最新版本
     * @return 大题
     */
    BigQuestionGroup getBigQuestion(String bizBigQuestionId, String versionNumber);

    /**
     * 根据大题ID获取大题
     * @param questionId 大题ID
     * @return 大题
     */
    BigQuestionGroup getBigQuestion(Long questionId);

    /**
     * 根据大题ID批量获取大题
     * @param groupIds 大题ID
     * @return 大题列表
     */
    List<BigQuestionGroup> batchGetBigQuestions(List<Long> groupIds);

    /**
     * 根据父业务ID和版本获取大题ID
     * @param parentId 父业务ID
     * @param parentVersion 父版本号
     * @param questionId 大题ID
     * @return 大题ID
     */
    Long getIdByBizParentIdAndVersion(String parentId, String parentVersion, String questionId);
}