package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO;

/**
 * 图谱创建相关接口
 */
public interface BookKnowledgeSourceService {

    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    int insert(BookKnowledgeResourceCompleteInfoPO record);

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    int insertSelective(BookKnowledgeResourceCompleteInfoPO record);

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    BookKnowledgeResourceCompleteInfoPO selectByPrimaryKey(Long id);

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKeySelective(BookKnowledgeResourceCompleteInfoPO record);

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKey(BookKnowledgeResourceCompleteInfoPO record);


}
