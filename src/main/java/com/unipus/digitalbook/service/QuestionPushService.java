package com.unipus.digitalbook.service;


import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
/**
 * 题目和作答推送第三方相关接口
 */
public interface QuestionPushService {
    /**
     * 异步推送题目
     * @param question 题目
     * @param tenantId 租户ID
     */
    void pushQuestionToThirdAsync(BigQuestionGroup question, Long tenantId);

    /**
     * 同步推送题目
     * @param question 题目
     * @param tenantId 租户ID
     */
    void pushQuestionToThirdSync(BigQuestionGroup question, Long tenantId);
}
