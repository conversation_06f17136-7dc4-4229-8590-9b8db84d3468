package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.user.SearchUserList;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务接口定义
 */
public interface UserService {

    /**
     * 根据手机号和机构ID和状态查询用户列表
     *
     * @param phone   手机号
     * @param orgId   机构ID
     * @param status  状态
     * @return 用户列表
     */
    List<UserInfo> searchByPhoneAndOrgAndStatus(String phone, Long orgId, Integer status, PageParams pageParams);

    /**
     * 根据机构ID和关键词查询有效的用户列表
     * @param orgId 机构ID（可以为空）
     * @param keyword 关键词（匹配用户名称或者手机号，可以为空）
     * @param page 分页参数(可以为空)
     * @return 有效的用户列表
     */
    List<UserInfo> selectValidUsersByOrgAndKeyword(Long orgId, String keyword, PageParams page);

    /**
     * 根据机构ID和用户ID列表查询用户的有效性
     * @param orgId 机构ID（可以为空）
     * @param userIds 用户ID列表（不为空）
     * @return 有效的用户ID列表
     */
    Set<Long> checkUserValidityWithUserIdsAndOrgId(Long orgId, List<Long> userIds);

    /**
     * 根据SSO ID获取用户信息
     *
     * @param ssoId SSO ID
     * @return 用户信息对象
     */
    UserInfo getUserInfoBySsoId(String ssoId);

    /**
     * 根据手机号获取用户信息
     *
     * @param cellPhone 用户的手机号
     * @return 返回用户信息对象，如果未找到则返回null
     */
    UserInfo getUserInfoByCellPhone(String cellPhone);

    /**
     * 获取登录用户信息
     *
     * @param ssoId SSO ID
     * @param mobile 手机号
     * @return 用户信息对象
     */
    Response<CurrentUserInfo> getLoginUserInfo(String ssoId, String mobile);

    /**
     * 根据条件搜索用户列表
     *
     * @param orgId      组织ID，用于限定搜索的组织范围
     * @param cellPhone  手机号，用于搜索特定的用户
     * @param userName   用户名，用于搜索特定的用户
     * @param status     用户状态，用于过滤用户
     * @param pageParams 分页参数，用于指定查询的页码和每页数量
     * @return SearchUserList 包含用户列表和总数量的对象
     */
    SearchUserList searchUserList(Long orgId, String cellPhone, Integer status, String userName, PageParams pageParams);

    /**
     * 激活用户
     *
     * @param userId 用户ID
     * @param ssoId 用户的SSO ID
     * @return 返回是否激活成功
     */
    Boolean activateUser(Long userId, String ssoId);

    /**
     * 添加用户,并添加到指定组织，并分配角色
     *
     * @param userInfo 用户信息对象，包含用户的详细信息
     * @param orgId    组织ID，用户将被添加到该组织
     * @param roleIds  角色ID列表，用户将被赋予这些角色
     * @param opUserId 操作者ID，执行此操作的用户ID
     * @return 操作是否成功
     */
    Boolean addUser(UserInfo userInfo, Long orgId, List<Long> roleIds, Long opUserId);


    /**
     * 编辑用户信息（管理员操作）
     *
     * @param userInfo 用户信息对象，包含需要更新的用户数据
     * @param opUserId 操作用户的ID，用于记录谁进行了此次更新
     * @return 返回布尔值，指示操作是否成功
     */
    Boolean editUser4Admin(UserInfo userInfo, Long opUserId);



    /**
     * 获取当前用户基本信息
     *
     * @param userId 用户ID
     * @return 用户信息对象
     */
    UserInfo getUserInfo(Long userId);

    /**
     * 获取当前用户信息（包含组织角色映射）
     *
     * @param userId 用户ID
     * @param orgId         组织ID
     * @return 用户信息对象
     */
    CurrentUserInfo getCurrentUserInfo(Long userId, Long orgId);

    /**
     * 更新当前用户名称
     *
     * @param userInfo 用户信息对象
     * @param opUserId 操作者ID
     * @return 更新是否成功
     */
    Boolean updateCurrentUserInfo(UserInfo userInfo, Long opUserId);

    /**
     * 获取用户信息MAP
     * @param userIds 用户ID列表
     * @return 用户信息对象MAP,key：用户id， value：用户信息对象
     */
    Map<Long, UserInfo> getUserMap(Collection<Long> userIds);

    /**
     * 根据用户ID查询用户名称
     * @param userIds 用户ID集合
     * @return 用户名称集合
     */
    Map<Long, String> getUserNames(List<Long> userIds);

    /**
     * 根据用户id列表 批量删除用户
     * @param userIdList 用户ID列表
     * @param opUserId 操作者ID
     * @return 删除是否成功
     */
    int deleteUsers(List<Long> userIdList, Long opUserId);
}

