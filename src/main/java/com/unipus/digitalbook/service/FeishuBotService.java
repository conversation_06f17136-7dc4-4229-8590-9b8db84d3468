package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuPostMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuTextMessage;

/**
 * 飞书机器人服务接口
 */
public interface FeishuBotService {
    
    /**
     * 发送文本消息
     *
     * @param text 文本内容
     * @return 是否发送成功
     */
    boolean sendTextMessage(String text);
    
    /**
     * 发送文本消息（支持@功能）
     *
     * @param textMessage 文本消息对象
     * @return 是否发送成功
     */
    boolean sendTextMessage(FeishuTextMessage textMessage);
    
    /**
     * 发送富文本消息
     *
     * @param postMessage 富文本消息对象
     * @return 是否发送成功
     */
    boolean sendPostMessage(FeishuPostMessage postMessage);
    
    /**
     * 发送卡片消息
     *
     * @param cardMessage 卡片消息对象
     * @return 是否发送成功
     */
    boolean sendCardMessage(FeishuCardMessage cardMessage);
    
    /**
     * 发送自定义消息
     *
     * @param messageType 消息类型
     * @param content 消息内容
     * @return 是否发送成功
     */
    boolean sendCustomMessage(String messageType, Object content);
    
    /**
     * 发送简单通知消息
     *
     * @param title 标题
     * @param content 内容
     * @return 是否发送成功
     */
    boolean sendNotification(String title, String content);
    
    /**
     * 发送错误通知
     *
     * @param error 错误信息
     * @param context 上下文信息
     * @return 是否发送成功
     */
    boolean sendErrorNotification(String error, String context);
    
    /**
     * 发送带Grafana按钮的错误通知
     *
     * @param error 错误信息
     * @param context 上下文信息
     * @param requestId 请求ID，用于构建Grafana链接
     * @return 是否发送成功
     */
    boolean sendErrorNotificationWithGrafanaButton(String error, String context, String requestId);


    boolean sendErrorNotificationWithGrafanaButton(String error, String context, String requestId, String title, String templateColor);

    String buildGrafanaUrl(String requestId);
}