package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;

public interface PaperScoreTemplateService {

    Long addTemplate(PaperScoreTemplate paperScoreTemplate, Long createBy);

    boolean editTemplate(PaperScoreTemplate paperScoreTemplate, Long createBy);

    boolean publishTemplate(Long currentUserId, Long id);

    PaperScoreTemplate getTemplateDetail(Long id, boolean showZeroInterval);

    PaperScoreTemplateList getTemplateList(String name, Integer type, Integer status, PageParams pageParams);
}
