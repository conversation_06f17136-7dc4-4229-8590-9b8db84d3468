package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.book.BookSettingLogList;
import com.unipus.digitalbook.model.params.book.BookSettingLogParam;
import com.unipus.digitalbook.model.params.book.BookSettingParam;
import com.unipus.digitalbook.model.po.book.BookSettingPO;

import java.util.List;
import java.util.Map;

public interface BookSettingService {

    /**
     * 更新或插入教材设置
     *
     * @param param          教材设置参数
     * @param currentUserId  当前用户ID
     * @return 发布日志ID
     */
    Long upsertSetting(BookSettingParam param, Long currentUserId);

    void upsertGreeting(String bookId, String greeting, Long currentUserId);

    String greeting(String bookId);

    BookSettingPO selectByBookId(String bookId);

    BookSettingLogList searchLog(BookSettingLogParam param);

    Map<String, BookSettingPO> selectByBookIds(List<String> bookIds);
}
