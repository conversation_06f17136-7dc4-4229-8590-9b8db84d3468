package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.TextMatchQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class TextMatchQuestionProcessor extends AbstractQuestionProcessor<TextMatchQuestion> {

    public TextMatchQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.TEXT_MATCH));
    }

    @Override
    public TextMatchQuestion toQuestion() {
        return new TextMatchQuestion();
    }
}