package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.WritingQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class WritingQuestionProcessor extends AbstractQuestionProcessor<WritingQuestion> {

    public WritingQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.WRITING));
    }

    @Override
    public WritingQuestion toQuestion() {
        return new WritingQuestion();
    }
}