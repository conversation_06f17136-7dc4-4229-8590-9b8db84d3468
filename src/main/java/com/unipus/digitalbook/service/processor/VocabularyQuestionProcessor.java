package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.VocabularyQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class VocabularyQuestionProcessor extends AbstractQuestionProcessor<VocabularyQuestion> {

    public VocabularyQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.VOCABULARY));
    }

    @Override
    public VocabularyQuestion toQuestion() {
        return new VocabularyQuestion();
    }
}