package com.unipus.digitalbook.service.processor.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.ApplicationContextUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.service.converter.tenant.BodyConverter;
import com.unipus.digitalbook.service.generator.HeaderGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.util.StringUtils;

import java.util.Map;

@Slf4j
public abstract class BaseMessageProcessor implements MessageProcessor {

    public <S, T> T convert(String converter, S source, TypeReference<T> typeReference) {

        if (!StringUtils.hasText(converter)) {
            String sourceTypeName = source.getClass().getTypeName();
            String targetTypeName = typeReference.getType().getTypeName();
            String stringTypeName = String.class.getTypeName();

            if (stringTypeName.equals(sourceTypeName) && !stringTypeName.equals(targetTypeName)) {
                return JsonUtil.parseObject((String)source, typeReference);
            }

            if (!stringTypeName.equals(sourceTypeName) && stringTypeName.equals(targetTypeName)) {
                return (T)JsonUtil.toJsonString(source);
            }

            return (T) source;

        }

        try {
            Object obj = ApplicationContextUtil.getBean(converter, BodyConverter.class)
                    .convert(source);

            log.info("source message {} to target message {}", source, JsonUtil.writeValueAsString(obj));
            return (T) obj;

        } catch (BeansException e) {
            throw new TenantMessageException(
                    String.format(ProduceResultEnum.CONVERTER_NOT_FOUND.getMessage(), converter), e);
        } catch (Exception e) {
            throw new TenantMessageException(e.getMessage(), e);
        }
    }

    public Map<String, String> generate(String generator) {
        try {
            return ApplicationContextUtil.getBean(generator, HeaderGenerator.class)
                    .generateHeaders();
        } catch (BeansException e) {
            throw new TenantMessageException(
                    String.format(ProduceResultEnum.GENERATOR_NOT_FOUND.getMessage(), generator), e);
        }
    }
}
