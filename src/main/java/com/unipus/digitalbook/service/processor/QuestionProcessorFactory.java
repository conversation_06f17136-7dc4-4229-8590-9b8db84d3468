package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.dao.QuestionGroupPOMapper;
import com.unipus.digitalbook.dao.QuestionPOMapper;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionTag;
import com.unipus.digitalbook.model.entity.question.SmallQuestion;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import com.unipus.digitalbook.model.po.question.QuestionPO;
import com.unipus.digitalbook.service.TagService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class QuestionProcessorFactory {

    private final Map<QuestionGroupTypeEnum, AbstractQuestionProcessor<? extends Question>> processorMap;

    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;

    @Resource
    private QuestionPOMapper questionPOMapper;

    @Resource
    private TagService tagService;

    @Autowired
    public QuestionProcessorFactory(List<AbstractQuestionProcessor> processors) {
        this.processorMap = new EnumMap<>(QuestionGroupTypeEnum.class);
        for (AbstractQuestionProcessor processor : processors) {
            processor.getQuestionGroupTypeEnums().forEach(questionGroupTypeEnum ->
                    processorMap.put((QuestionGroupTypeEnum) questionGroupTypeEnum, processor));
        }
    }

    public <T extends Question> AbstractQuestionProcessor<T> getProcessor(QuestionGroupTypeEnum type) {
        return (AbstractQuestionProcessor<T>) processorMap.get(type);
    }

    public List<Question> getSmallQuestions(Long parentId) {
        List<QuestionGroupPO> questionGroupPOS = questionGroupPOMapper.selectByParentId(parentId);
        if (questionGroupPOS.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> groupIds = questionGroupPOS.stream().map(QuestionGroupPO::getId).toList();
        Map<Long, List<QuestionPO>> questionMap = questionPOMapper.selectByGroupIds(groupIds).stream()
                .collect(Collectors.groupingBy(QuestionPO::getGroupId));
        Map<String, List<List<QuestionTag>>> questionTagsMap = getQuestionTagPaths(questionGroupPOS);
        return questionGroupPOS.stream().map(questionGroupPO -> {
            Long groupId = questionGroupPO.getId();
            SmallQuestion smallQuestion = questionGroupPO.toSmallQuestion();
            fillTags(smallQuestion, questionTagsMap);
            QuestionGroupTypeEnum type = QuestionGroupTypeEnum.getEnumByCode(questionGroupPO.getType());
            List<QuestionPO> questionPOS = questionMap.get(groupId);
            if (CollectionUtils.isEmpty(questionPOS)) {
                return null;
            }
            AbstractQuestionProcessor<Question> processor = getProcessor(type);
            List<Question> questionList = processor.toQuestionList(questionPOS);
            if (processor.hasSmallQuestion(questionList)) {
                questionList.addAll(getSmallQuestions(groupId));
            }
            smallQuestion.setQuestions(questionList);
            return smallQuestion;
        }).filter(Objects::nonNull).sorted(Comparator.comparing(Question::getSortOrder)).collect(Collectors.toList());
    }

    private void fillTags(Question question, Map<String, List<List<QuestionTag>>> questionTagsMap) {
        if (CollectionUtils.isEmpty(questionTagsMap)) {
            return;
        }
        List<List<QuestionTag>> tagPaths = questionTagsMap.get(question.getBizQuestionId());
        if (CollectionUtils.isEmpty(tagPaths)) {
            return;
        }
        question.setTags(tagPaths);
    }
    private Map<String, List<List<QuestionTag>>> getQuestionTagPaths(List<QuestionGroupPO> questionGroup) {
        if (CollectionUtils.isEmpty(questionGroup)) {
            return Collections.emptyMap();
        }
        List<String> bizQuestionIds = questionGroup.stream().map(QuestionGroupPO::getBizGroupId).toList();
        Map<String, List<Tag>> questionTags = tagService.getTagListByResourceIdsAndVersion(bizQuestionIds,
                TagResourceTypeEnum.QUESTION,
                questionGroup.getFirst().getVersionNumber());

        return questionTags.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .collect(Collectors.groupingBy(Tag::getTagType, LinkedHashMap::new, Collectors.toList()))
                                .values().stream()
                                .map(tagList -> tagList.stream()
                                        .map(QuestionTag::new)
                                        .toList()
                                ).toList()
                ));
    }
}