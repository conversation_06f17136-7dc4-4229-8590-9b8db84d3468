package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.DiscussionQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class DiscussionQuestionProcessor extends AbstractQuestionProcessor<DiscussionQuestion> {

    public DiscussionQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.DISCUSSION));
    }

    @Override
    public DiscussionQuestion toQuestion() {
        return new DiscussionQuestion();
    }
}