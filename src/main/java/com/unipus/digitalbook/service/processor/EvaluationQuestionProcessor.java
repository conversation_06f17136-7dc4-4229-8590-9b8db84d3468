package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.EvaluationQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class EvaluationQuestionProcessor extends AbstractQuestionProcessor<EvaluationQuestion> {

    public EvaluationQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.EVALUATION));
    }


    @Override
    public EvaluationQuestion toQuestion() {
        return new EvaluationQuestion();
    }
}