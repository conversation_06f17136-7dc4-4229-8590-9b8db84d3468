package com.unipus.digitalbook.service.processor.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;

public interface MessageProcessor {
    ChannelEnum supportChannel();
    <S,T> T process(String messageUuid, TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO,
                                           S message, TypeReference<T> targetType);

}
