package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class ChoiceQuestionProcessor extends AbstractQuestionProcessor<ChoiceQuestion> {

    public ChoiceQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.MULTI_CHOICE, QuestionGroupTypeEnum.SINGLE_CHOICE));
    }

    @Override
    public ChoiceQuestion toQuestion() {
        return new ChoiceQuestion();
    }
}