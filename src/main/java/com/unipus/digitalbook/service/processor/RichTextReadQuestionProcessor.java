package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.RichTextReadQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class RichTextReadQuestionProcessor extends AbstractQuestionProcessor<RichTextReadQuestion> {

    public RichTextReadQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.RICH_TEXT_READ));
    }

    @Override
    public RichTextReadQuestion toQuestion() {
        return new RichTextReadQuestion();
    }
}