package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.RolePlayQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class RolePlayQuestionProcessor extends AbstractQuestionProcessor<RolePlayQuestion> {

    public RolePlayQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.ROLE_PLAY_SCOOP));
    }


    @Override
    public RolePlayQuestion toQuestion() {
        return new RolePlayQuestion();
    }
}