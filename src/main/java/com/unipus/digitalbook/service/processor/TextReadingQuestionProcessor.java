package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.TextReadingQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class TextReadingQuestionProcessor extends AbstractQuestionProcessor<TextReadingQuestion> {

    public TextReadingQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.ORAL_SENTENCE_SCOOP));
    }


    @Override
    public TextReadingQuestion toQuestion() {
        return new TextReadingQuestion();
    }
}