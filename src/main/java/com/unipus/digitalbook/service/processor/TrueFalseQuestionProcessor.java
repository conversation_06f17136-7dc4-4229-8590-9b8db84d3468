package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.type.TrueFalseQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.List;

@Component
public class TrueFalseQuestionProcessor extends AbstractQuestionProcessor<TrueFalseQuestion> {

    public TrueFalseQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.TRUE_FALSE));
    }

    @Override
    public boolean hasSmallQuestion(List<Question> questions) {
        return true;
    }
    @Override
    public TrueFalseQuestion toQuestion() {
        return new TrueFalseQuestion();
    }
}