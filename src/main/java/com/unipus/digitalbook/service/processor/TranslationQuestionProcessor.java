package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.TranslationQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class TranslationQuestionProcessor extends AbstractQuestionProcessor<TranslationQuestion> {

    public TranslationQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.TRANSLATION));
    }

    @Override
    public TranslationQuestion toQuestion() {
        return new TranslationQuestion();
    }
}