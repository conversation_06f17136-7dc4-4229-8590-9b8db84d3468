package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.dao.ChoiceQuestionOptionPOMapper;
import com.unipus.digitalbook.dao.QuestionAnswerPOMapper;
import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO;
import com.unipus.digitalbook.model.po.question.QuestionAnswerPO;
import com.unipus.digitalbook.model.po.question.QuestionPO;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractQuestionProcessor<T extends Question> {
    private final Set<QuestionGroupTypeEnum> questionGroupTypeEnums;

    protected AbstractQuestionProcessor(Set<QuestionGroupTypeEnum> questionGroupTypeEnums) {
        this.questionGroupTypeEnums = questionGroupTypeEnums;
    }
    @Resource
    protected ChoiceQuestionOptionPOMapper choiceQuestionOptionPOMapper;

    @Resource
    protected QuestionAnswerPOMapper questionAnswerPOMapper;

    public Set<QuestionGroupTypeEnum> getQuestionGroupTypeEnums() {
        return questionGroupTypeEnums;
    }

    /**
     * 将QuestionPO转换为Question实体
     * 获取具体的题的类型
     * @return 具体题的类型
     */
    public abstract T toQuestion();

    public boolean hasSmallQuestion(List<Question> questions) {
        return questions.stream().anyMatch(question -> question.getQuestionText() != null && !CollectionUtils.isEmpty(question.getQuestionText().getRelevancy()));
    }


    public List<Question> toQuestionList(List<QuestionPO> questionPOS) {
        List<Long> questionIds = questionPOS.stream().map(QuestionPO::getId).toList();
        Map<Long, List<QuestionAnswer>> answerMap = questionAnswerPOMapper.selectByQuestionIds(questionIds).stream()
                .map(QuestionAnswerPO::toEntity).collect(Collectors.groupingBy(QuestionAnswer::getQuestionId));
        Map<Long, List<ChoiceQuestionOption>> choiceMap = choiceQuestionOptionPOMapper.selectByQuestionIds(questionIds).stream().
                map(ChoiceQuestionOptionPO::toEntity).collect(Collectors.groupingBy(ChoiceQuestionOption::getQuestionId));
        return questionPOS.stream()
                .map(questionPO -> {
                    Long id = questionPO.getId();
                    Question entity = questionPO.toEntity(this::toQuestion);
                    entity.setAnswers(answerMap.getOrDefault(id, Collections.emptyList()).stream().sorted(Comparator.comparing(QuestionAnswer::getSortOrder)).toList());
                    entity.setOptions(choiceMap.getOrDefault(id, Collections.emptyList()).stream().sorted(Comparator.comparing(ChoiceQuestionOption::getSortOrder)).toList());
                    return entity;
                }).sorted(Comparator.comparing(Question::getSortOrder)).collect(Collectors.toList());
    }
}
