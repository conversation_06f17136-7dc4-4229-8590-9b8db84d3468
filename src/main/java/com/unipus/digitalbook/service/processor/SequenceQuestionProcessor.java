package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.SequenceQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class SequenceQuestionProcessor extends AbstractQuestionProcessor<SequenceQuestion> {

    public SequenceQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.SEQUENCE));
    }

    @Override
    public SequenceQuestion toQuestion() {
        return new SequenceQuestion();
    }
}