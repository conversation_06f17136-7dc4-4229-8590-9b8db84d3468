package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.MultiMediaUploadQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class MultiMediaUploadQuestionProcessor extends AbstractQuestionProcessor<MultiMediaUploadQuestion> {

    public MultiMediaUploadQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.MULTI_MEDIA_UPLOAD));
    }


    @Override
    public MultiMediaUploadQuestion toQuestion() {
        return new MultiMediaUploadQuestion();
    }
}