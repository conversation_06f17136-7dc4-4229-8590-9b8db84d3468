package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.OralPersonalStateQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class OralPersonalStateQuestionProcessor extends AbstractQuestionProcessor<OralPersonalStateQuestion> {

    public OralPersonalStateQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.ORAL_PERSONAL_STATE));
    }

    @Override
    public OralPersonalStateQuestion toQuestion() {
        return new OralPersonalStateQuestion();
    }
}