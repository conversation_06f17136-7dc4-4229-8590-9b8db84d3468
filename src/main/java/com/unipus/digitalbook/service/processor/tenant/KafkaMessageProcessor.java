package com.unipus.digitalbook.service.processor.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.conf.tenant.TenantSubscribeConfig;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.InvalidProducerEpochException;
import org.apache.kafka.common.errors.OutOfOrderSequenceException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KafkaMessageProcessor extends BaseMessageProcessor {

    @Resource
    private TenantSubscribeConfig tenantSubscribeConfig;

    @Override
    public ChannelEnum supportChannel() {
        return ChannelEnum.KAFKA;
    }

    @Override
    @SneakyThrows
    public <S,T> T process(String messageUuid, TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO,
                                                  S message, TypeReference<T> targetType) {

        Long tenantId = tenantSubscribePO.getTenantId();
        String messageTopic = tenantSubscribePO.getMessageTopic();

        String bootstrapServers = tenantSubscribePO.getKafkaBootstrapServers();

        KafkaTemplate<String, String> kafkaTemplate = tenantSubscribeConfig.kafkaTemplate(bootstrapServers);
        try {
            String requestBody = convert(tenantChannelPO.getRequestBodyConverter(), message, new TypeReference<>() {});
            kafkaTemplate.send(tenantSubscribePO.getKafkaTopic(), requestBody);
//            SendResult<String, String> sendResult = future.get();
//            RecordMetadata metadata = sendResult.getRecordMetadata();
//            log.info("tenantId {} messageTopic {} requestBody {} sent successfully to topic {} partition {} offset {}",
//                    tenantId, messageTopic, requestBody, metadata.topic(), metadata.partition(), metadata.offset());
            return null;
        } catch (Exception e) {
            log.error("tenantId {} messageUuid {} messageTopic {} requestBody {} send fail", tenantId, messageUuid, messageTopic, JsonUtil.toJsonString(message), e);
            if (e instanceof OutOfOrderSequenceException || e instanceof InvalidProducerEpochException) {
                tenantSubscribeConfig.restartKafkaTemplate(bootstrapServers);
            }
            throw new TenantMessageException(ProduceResultEnum.FAIL.getMessage(), e);
        }
    }
}
