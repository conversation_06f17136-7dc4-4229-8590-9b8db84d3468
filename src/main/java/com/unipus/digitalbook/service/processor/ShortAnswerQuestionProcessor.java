package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.ShortAnswerQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class ShortAnswerQuestionProcessor extends AbstractQuestionProcessor<ShortAnswerQuestion> {

    public ShortAnswerQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.SHORT_ANSWER));
    }


    @Override
    public ShortAnswerQuestion toQuestion() {
        return new ShortAnswerQuestion();
    }
}