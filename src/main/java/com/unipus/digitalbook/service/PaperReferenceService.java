package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.PaperReference;

import java.util.List;
import java.util.Set;

/**
 * 试卷引用服务接口
 */
public interface PaperReferenceService {

    /**
     * 添加章节试卷引用关系
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    Boolean insertPaperReference(PaperReference paperReference, Long userId);

    /**
     * 删除教材中试卷引用
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    Boolean deletePaperReference(PaperReference paperReference, Long userId);

    /**
     * 删除指定教材章节中的所有编辑态试卷引用
     * @param bookId 教材ID
     * @param chapterId 章节ID
     * @param userId 当前用户ID
     */
    void removePaperReferenceInChapter(String bookId, String chapterId, Long userId);

    /**
     * 保存试卷与特定章节版本的关系
     *
     * @param chapterId        章节ID
     * @param versionChapterId 版本章节ID
     * @param currentUserId    当前用户ID
     */
    void savePaperReferenceWithChapterVersion(String chapterId, Long versionChapterId, Long currentUserId);

    /**
     * 检查试卷引用是否存在
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷列表ID
     */
    Set<String> checkPaperReferenceExist(List<String> paperIds) ;

    /**
     * 检查试卷引用是否已发布
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷IDj集合
     */
    Set<String> getVersionedPaperReference(List<String> paperIds);

    /**
     * 保存试卷引用关系
     *
     * @param paperReferences 试卷引用列表
     * @param userId          当前用户ID
     */
    void savePaperReference(List<PaperReference> paperReferences, Long userId);

    /**
     * 查询教材中试卷的引用列表
     * 输出最新发布版本的试卷引用列表，否则输出保存的试卷引用列表
     *
     * @param bookId        教材ID
     */
    List<PaperReference> getPaperReferenceList(String bookId);
}
