package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.menu.Menu;

import java.util.List;

public interface MenuService {

    /**
     * 获取全部菜单列表
     * @return 菜单列表
     */
    List<Menu> all(Long roleId);

    /**
     * 获取用户可见的全部菜单列表
     * @param userId 用户ID
     * @param orgId 机构ID
     * @return 菜单树
     */
    List<Menu> list(Long userId, Long orgId);

    /**
     * 保存菜单
     * @param menu 菜单（包含新增或修改的信息）
     * @return 返回菜单id
     */
    Long save(Menu menu);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 是否删除成功
     */
    boolean delete(Long userId, Long menuId);

    /**
     * 改变菜单顺序
     * @param sortParams 包含排序信息的参数列表
     * @return 是否排序成功
     */
    boolean changePosition(List<Long> sortParams);
}
