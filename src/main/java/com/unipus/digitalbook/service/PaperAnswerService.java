package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.params.paper.UserPaperAnswerParam;

import java.util.List;

/**
 * 试卷作答记录服务接口
 */
public interface PaperAnswerService {

    /**
     * 获取预览作答记录
     * @param param 用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return List<UserAnswer> 用户试卷作答记录
     */
    UserPaperAnswer getPreviewPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo);

    /**
     * 保存用户作答记录
     * @param param 用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    UserPaperInfo saveUserPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo);

    /**
     * 获取用户试卷作答记录
     * @param queryPaperAnswer 试卷成绩查询参数
     */
    UserPaperAnswer getUserPaperAnswer(QueryPaperAnswer queryPaperAnswer);

    /**
     * 提交试卷作答记录
     * @param param 用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    UserPaperInfo submitUserPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo);

    /**
     * 提交用户挑战卷成绩
     * @param userPaperInfo 用户试卷成绩提交参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    UserPaperInfo submitUserChallengeScore(UserPaperInfo userPaperInfo, UserAccessInfo userAccessInfo);

    /**
     * 获取用户试卷成绩
     * @param userPaperInfo 用户试卷成绩提交参数
     * @return UserPaperInfo 用户试卷得分信息
     */
    UserPaperScore getUserPaperScore(UserPaperInfo userPaperInfo, UserAccessInfo userAccessInfo);

    /**
     * 获取用户试卷成绩提交记录
     * @param userPaperInfo 用户试卷成绩提交参数
     * @return 用户试卷成绩提交列表
     */
    List<PaperScoreHistory> getUserPaperSubmitHistory(UserPaperInfo userPaperInfo);

}
