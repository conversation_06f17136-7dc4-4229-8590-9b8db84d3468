package com.unipus.digitalbook.service.remote.restful.qrcode.model;

import java.io.Serializable;

/**
 * 二维码接口响应结果
 *
 * <AUTHOR>
 * @date 2025/2/13 16:56
 */
public class QrCodeApiResponse<T> {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    public Boolean isSuccess() {
        return this.code != null && (this.code == 200 || this.code == 0);
    }

    /**
     * 响应数据
     */
    private T data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
