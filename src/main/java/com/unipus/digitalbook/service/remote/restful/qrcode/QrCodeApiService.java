package com.unipus.digitalbook.service.remote.restful.qrcode;

import com.unipus.digitalbook.service.remote.restful.qrcode.model.AddQrCodeRequest;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeApiResponse;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeInfo;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.UpdateQrCodeRequest;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;
import java.util.Map;

/**
 * cms2二维码接口
 *
 * <AUTHOR>
 * @date 2025/2/13 15:35
 */
@HttpExchange("/studio/course")
public interface QrCodeApiService {

    // token 生成接口
    @PostExchange("/jwt/gen")
    QrCodeApiResponse<String> genToken(@RequestBody Map<String, Object> params);

    // 添加二维码
    @PostExchange("/qrcode/add")
    QrCodeApiResponse<List<QrCodeInfo>> add(@RequestBody AddQrCodeRequest addQrCodeRequest);

    // 更新二维码
    @PostExchange("/qrcode/update")
    QrCodeApiResponse<QrCodeInfo> update(@RequestBody UpdateQrCodeRequest updateQrCodeRequest);

    // 搜索二维码
    @GetExchange("/qrcode/search")
    QrCodeApiResponse<List<QrCodeInfo>> search(@RequestParam String courseId, @RequestParam String randomCode);

    // 二维码列表
    @GetExchange("/qrcode/list")
    QrCodeApiResponse<List<QrCodeInfo>> list(@RequestParam String courseId);

    // 查看二维码
    @GetExchange("/qrcode/display")
    ResponseEntity<Resource> display(@RequestParam("id") Long id);

    // 下载二维码
    @GetExchange("/qrcode/download")
    ResponseEntity<Resource> download(@RequestParam("id") Long id);

    // 打包下载二维码
    @GetExchange("/qrcode/download/bach")
    ResponseEntity<Resource> downloadBach(@RequestParam("courseId") String courseId);

    // 打包下载二维码
    @GetExchange("/qrcode/downloadBach")
    ResponseEntity<Resource> downloadBach(@RequestBody Map<String, Object> params);

    //导入二维码
    @GetExchange("/qrcode/import")
    QrCodeApiResponse<Boolean> importQrcode(@RequestHeader("X-Annotator-Auth-Token") String authToken, @RequestParam("courseId") String courseId, @RequestPart("file") MultipartFile file);

    //导出二维码
    @GetExchange("/qrcode/export")
    ResponseEntity<Resource> exportQrcode(@RequestHeader("X-Annotator-Auth-Token") String authToken, @RequestParam("courseId") String courseId);
}
