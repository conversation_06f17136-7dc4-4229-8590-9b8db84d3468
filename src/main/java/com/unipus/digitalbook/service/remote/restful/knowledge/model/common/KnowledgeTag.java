package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.io.Serializable;
import java.util.List;

public class KnowledgeTag implements Serializable {
    private String id;
    private String content;
    private String typeId;
    private String type;
    private String colour;
    private List<KnowledgeTag> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getColour() {
        return colour;
    }

    public void setColour(String colour) {
        this.colour = colour;
    }

    public List<KnowledgeTag> getChildren() {
        return children;
    }

    public void setChildren(List<KnowledgeTag> children) {
        this.children = children;
    }
}