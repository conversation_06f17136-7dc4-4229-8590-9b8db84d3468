package com.unipus.digitalbook.service.remote.restful;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

import java.time.Duration;

@Slf4j
public class BaseApiConfig {

    @Value("${remote.api.timeout:10}")
    private int timeout;

    /**
     * 创建通用的 WebClient.Builder，提供给子类使用
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .filter(loggingFilter());
    }

    /**
     * 通用的日志拦截器
     */
    @Bean
    public ExchangeFilterFunction loggingFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            String fullUrl = clientRequest.url().toString();
            log.info("Request: {} {}", clientRequest.method(), fullUrl);
            clientRequest.headers().forEach((name, values) ->
                    values.forEach(value -> log.info("Request Header: {}={}", name, value)));
            return Mono.just(clientRequest);
        });
    }

    /**
     * 创建 WebClientAdapter
     */
    protected WebClientAdapter createWebClientAdapter(WebClient webClient) {
        WebClientAdapter adapter = WebClientAdapter.create(webClient);
        adapter.setBlockTimeout(Duration.ofSeconds(timeout)); // 设置超时时间
        return adapter;
    }

    /**
     * 创建 HTTP 代理工厂
     */
    protected HttpServiceProxyFactory createHttpServiceProxyFactory(WebClientAdapter adapter) {
        return HttpServiceProxyFactory.builderFor(adapter).build();
    }
}
