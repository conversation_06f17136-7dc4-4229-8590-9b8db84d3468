package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.util.List;

/**
 * └label String Y 无 资源类型：教材资源/拓展资源
 * └source String Y 无 来源
 * └name String Y 无 资源名称
 * └url String Y 无 资源地址
 * └dir String Y 无 所属目录
 * └labels List<String>Y 无 资源标签
 */
public class Resource {
    private String id;
    private String label;
    private String source;
    private String name;
    private String url;
    private String dir;
    private List<KnowledgeLabel> knowledgeLabels;

    // Getters and Setters
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public List<KnowledgeLabel> getLabels() {
        return knowledgeLabels;
    }

    public void setLabels(List<KnowledgeLabel> knowledgeLabels) {
        this.knowledgeLabels = knowledgeLabels;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
