package com.unipus.digitalbook.service.remote.restful.assistant;

import com.unipus.digitalbook.service.remote.restful.BaseApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import reactor.core.publisher.Mono;

import java.time.Duration;

@Configuration
@Slf4j
public class AssistantClientConfig extends BaseApiConfig {

    @Value("${remote.assistant.host}")
    private String host;

    @Bean AssistantApiService assistantApiService(WebClient.Builder webClientBuilder) {
        ExchangeFilterFunction logFilter = ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            // 获取完整的URL（包含查询参数）
            String fullUrl = clientRequest.url().toString();
            // 打印请求方法和URL
            log.info("Request: {} {}", clientRequest.method(), fullUrl);
            // 如果需要打印请求头
            clientRequest.headers().forEach((name, values) -> {
                values.forEach(value -> log.info("Request Header: {}={}", name, value));
            });
            return Mono.just(clientRequest);
        });

        // 创建 WebClient
        WebClient webClient = webClientBuilder
                .codecs(configurer -> configurer
                .defaultCodecs()
                .maxInMemorySize(16 * 1024 * 1024))
                .baseUrl(host)
                .filter(logFilter) // 添加请求日志过滤器
                .build();
        // 创建 WebClientAdapter
        WebClientAdapter adapter = createWebClientAdapter(webClient);
        adapter.setBlockTimeout(Duration.ofSeconds(10));
        // 创建 HTTP 代理工厂并返回 UaiClientConfig 实例
        return createHttpServiceProxyFactory(adapter).createClient(AssistantApiService.class);
    }


}
