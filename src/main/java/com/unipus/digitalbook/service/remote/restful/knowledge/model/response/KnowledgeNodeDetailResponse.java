package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeTag;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月29日 16:53
 */
public class KnowledgeNodeDetailResponse implements Serializable {

    @JsonAlias(value = "知识标签")
    private List<String> knowledgeTags;

    @JsonAlias(value = "$index")
    private Integer index;

    private Resources resources;

    private int resourcesCount;

    @JsonAlias(value = "知识类型")
    private Integer knowledgeType;

    @JsonAlias(value = "学习目标")
    private String studyTarget;

    private String name;

    @JsonAlias(value = "知识描述")
    private String knowledgeDesc;

    private String graphId;

    @JsonAlias(value = "$label")
    private String label;

    @JsonAlias(value = "$id")
    private Long id;

    private Integer childrenCount;


    public List<String> getKnowledgeTags() {
        return knowledgeTags;
    }

    public void setKnowledgeTags(List<String> knowledgeTags) {
        this.knowledgeTags = knowledgeTags;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Resources getResources() {
        return resources;
    }

    public void setResources(Resources resources) {
        this.resources = resources;
    }

    public int getResourcesCount() {
        return resourcesCount;
    }

    public void setResourcesCount(int resourcesCount) {
        this.resourcesCount = resourcesCount;
    }

    public Integer getKnowledgeType() {
        return knowledgeType;
    }

    public void setKnowledgeType(Integer knowledgeType) {
        this.knowledgeType = knowledgeType;
    }

    public String getStudyTarget() {
        return studyTarget;
    }

    public void setStudyTarget(String studyTarget) {
        this.studyTarget = studyTarget;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKnowledgeDesc() {
        return knowledgeDesc;
    }

    public void setKnowledgeDesc(String knowledgeDesc) {
        this.knowledgeDesc = knowledgeDesc;
    }

    public String getGraphId() {
        return graphId;
    }

    public void setGraphId(String graphId) {
        this.graphId = graphId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(Integer childrenCount) {
        this.childrenCount = childrenCount;
    }


    public static class CourseResource {
        @JsonAlias(value = "资源标签")
        private List<KnowledgeTag> resourceTags;

        @JsonAlias(value = "来源")
        private String source;

        @JsonAlias(value = "所属目录")
        private String dir;

        @JsonAlias(value = "$index")
        private Integer index;

        private String name;

        private String graphId;

        @JsonAlias(value = "资源地址")
        private String resourceUrl;

        @JsonAlias(value = "$label")
        private String label;

        @JsonAlias(value = "$id")
        private int id;

        public List<KnowledgeTag> getResourceTags() {
            return resourceTags;
        }

        public void setResourceTags(List<KnowledgeTag> resourceTags) {
            this.resourceTags = resourceTags;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getDir() {
            return dir;
        }

        public void setDir(String dir) {
            this.dir = dir;
        }

        public Integer getIndex() {
            return index;
        }

        public void setIndex(Integer index) {
            this.index = index;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getGraphId() {
            return graphId;
        }

        public void setGraphId(String graphId) {
            this.graphId = graphId;
        }

        public String getResourceUrl() {
            return resourceUrl;
        }

        public void setResourceUrl(String resourceUrl) {
            this.resourceUrl = resourceUrl;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class CourseExtResource {

        @JsonAlias(value = "资源标签")
        private List<KnowledgeTag> resourceTags;

        @JsonAlias(value = "来源")
        private String source;

        @JsonAlias(value = "$index")
        private int index;

        private String name;

        private String graphId;

        @JsonAlias(value = "资源地址")
        private String resourceUrl;

        @JsonAlias(value = "$label")
        private String label;

        @JsonAlias(value = "$id")
        private int id;

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getGraphId() {
            return graphId;
        }

        public void setGraphId(String graphId) {
            this.graphId = graphId;
        }

        public String getResourceUrl() {
            return resourceUrl;
        }

        public void setResourceUrl(String resourceUrl) {
            this.resourceUrl = resourceUrl;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public List<KnowledgeTag> getResourceTags() {
            return resourceTags;
        }

        public void setResourceTags(List<KnowledgeTag> resourceTags) {
            this.resourceTags = resourceTags;
        }
    }

    public static class Resources {

        @JsonAlias(value = "教材资源")
        private List<CourseResource> courseResourceList;

        @JsonAlias(value = "拓展资源")
        private List<CourseExtResource> courseExtResourceList;

        public List<CourseResource> getCourseResourceList() {
            return courseResourceList;
        }

        public void setCourseResourceList(List<CourseResource> courseResourceList) {
            this.courseResourceList = courseResourceList;
        }

        public List<CourseExtResource> getCourseExtResourceList() {
            return courseExtResourceList;
        }

        public void setCourseExtResourceList(List<CourseExtResource> courseExtResourceList) {
            this.courseExtResourceList = courseExtResourceList;
        }
    }


    public static void main(String[] args) {
        String json = "{\"知识标签\":[\"一级子节点-1\"],\"$index\":1,\"relatedNodes\":{},\"resources\":{\"教材资源\":[{\"资源标签\":[{\"colour\":\"{\\\"backgroundColor\\\":\\\"#F0F5FF\\\",\\\"color\\\":\\\"#015CFF\\\",\\\"borderColor\\\":\\\"#015CFF\\\"}\",\"content\":\"资源标签\",\"id\":\"85aa84b801354a01b07e61aaef5573fd\",\"type\":\"资源标签1\",\"typeId\":\"ce7808d20ee148a1b5422de0e1a5c091\"}],\"所属目录\":\"\",\"来源\":\"其他1\",\"$index\":1,\"name\":\"一级教材资源1\",\"graphId\":\"d18a093ec9c042d88a941ce4ff8794f4\",\"资源地址\":\"一级教材资源url1\",\"$label\":\"教材资源\",\"$id\":55}],\"拓展资源\":[{\"资源标签\":[{\"colour\":\"{\\\"backgroundColor\\\":\\\"#F0F5FF\\\",\\\"color\\\":\\\"#015CFF\\\",\\\"borderColor\\\":\\\"#015CFF\\\"}\",\"content\":\"资源标签\",\"id\":\"85aa84b801354a01b07e61aaef5573fd\",\"type\":\"资源标签1\",\"typeId\":\"ce7808d20ee148a1b5422de0e1a5c091\"}],\"来源\":\"添加链接\",\"$index\":1,\"name\":\"拓展资源\",\"graphId\":\"d18a093ec9c042d88a941ce4ff8794f4\",\"资源地址\":\"url\",\"$label\":\"拓展资源\",\"$id\":57},{\"资源标签\":[{\"colour\":\"{\\\"backgroundColor\\\":\\\"#F0F5FF\\\",\\\"color\\\":\\\"#015CFF\\\",\\\"borderColor\\\":\\\"#015CFF\\\"}\",\"content\":\"资源标签2\",\"id\":\"f5cf1c19d2a34e598dd79c7c342a0cde\",\"type\":\"资源标签1\",\"typeId\":\"ce7808d20ee148a1b5422de0e1a5c091\"}],\"来源\":\"添加文件\",\"$index\":2,\"name\":\"1\",\"graphId\":\"d18a093ec9c042d88a941ce4ff8794f4\",\"资源地址\":\"http://10.103.3.187:9000/platform/knowledge/file/1748509590885/1.jpg\",\"$label\":\"拓展资源\",\"$id\":58}]},\"resourcesCount\":3,\"知识类型\":2,\"学习目标\":\"学习1\",\"name\":\"第一级子节点-1\",\"知识描述\":\"内容1\",\"graphId\":\"d18a093ec9c042d88a941ce4ff8794f4\",\"$label\":\"知识点\",\"$id\":40,\"childrenCount\":0}";
        KnowledgeNodeDetailResponse knowledgeNodeDetailResponse = JSON.parseObject(json, KnowledgeNodeDetailResponse.class);
        System.out.println(JSON.toJSONString(knowledgeNodeDetailResponse));
    }
}
