package com.unipus.digitalbook.service.remote.restful.ucontent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "数字人模板信息")
public class AssistantTemplateResponse implements Serializable {
    @Schema(description = "唯一id")
    private String id;

    @Schema(description = "模板id")
    private String templateId;

    @Schema(description = "模板名称")
    private String title;

    @Schema(description = "助教类型")
    private Integer assistantType;

    @Schema(description = "模板配置内容")
    private String config;

    @Schema(description = "模板版本")
    private Integer version;

    @Schema(description = "模板实例版本")
    private Integer instanceVersion;

    @Schema(description = "模板状态")
    private Integer status;
}
