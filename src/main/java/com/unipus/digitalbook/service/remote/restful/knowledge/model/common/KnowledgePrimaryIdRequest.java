package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgePrimaryIdRequest {

    private Long id;
    private String knowledgeId;
    private String description;
    private String userName;
    // Getters and Setters


    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }


}
