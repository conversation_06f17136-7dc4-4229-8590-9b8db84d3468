package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ResourceTag implements Serializable {
    private String id;
    private String type;
    private String notes;
    private Date gmtCreate;
    private Date gmtModified;
    private String colour;
    private List<KnowledgeTag> labelSet;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public List<KnowledgeTag> getLabelSet() {
        return labelSet;
    }

    public void setLabelSet(List<KnowledgeTag> labelSet) {
        this.labelSet = labelSet;
    }


    public String getColour() {
        return colour;
    }

    public void setColour(String colour) {
        this.colour = colour;
    }
}
