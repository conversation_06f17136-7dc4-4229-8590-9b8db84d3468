package com.unipus.digitalbook.service.remote.restful.assistant;

import com.unipus.digitalbook.service.remote.restful.ucontent.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;

@HttpExchange("/studio/assistant")
public interface AssistantApiService {

    @PostExchange(value = "/access/token", contentType = "application/json")
    BaseResponse<String> assistantToken(@RequestBody AssistantTokenRequest request);

    @PostExchange(value = "/latest/instance", contentType = "application/json")
    BaseResponse<AssistantLatestInstanceResponse> latestInstance(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantLatestInstanceRequest request);

    @PostExchange(value = "/latest/instance/list", contentType = "application/json")
    BaseResponse<List<AssistantLatestInstanceResponse>> latestInstanceList(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantLatestInstanceListRequest request);

    @PostExchange(value = "/publish/all", contentType = "application/json")
    BaseResponse<Void> publishAll(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantPublishAllRequest request);

    @PostExchange(value = "/save", contentType = "application/json")
    BaseResponse<AssistantSaveResponse> saveAssistant(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantSaveRequest request);

    @PostExchange(value = "/query", contentType = "application/json")
    BaseResponse<AssistantLatestInstanceResponse> queryAssistant(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantLatestInstanceRequest request);

    @PostExchange(value = "/template/list", contentType = "application/json")
    BaseResponse<AssistantTemplateSearchResponse> templateList(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantTemplateSearchRequest request);

    @PostExchange(value = "/template/query", contentType = "application/json")
    BaseResponse<AssistantTemplateResponse> queryTemplate(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantTemplateQueryRequest request);

    @PostExchange(value = "/system/info", contentType = "application/json")
    BaseResponse<AssistantTypeListResponse> assistantType(
            @RequestHeader("x-annotator-auth-token") String jwtToken);

    @PostExchange(value = "/conversation/create", contentType = "application/json")
    BaseResponse<AssistantConversationCreateResponse> createConversation(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantConversationCreateRequest request);

    @PostExchange(value = "/conversation/message/history", contentType = "application/json")
    BaseResponse<AssistantConversationHistoryMessageResponse> historyMessage(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantConversationHistoryMessageRequest request);

    @PostExchange(value = "/remain/times", contentType = "application/json")
    BaseResponse<AssistantConversationRemainTimesResponse> remainTimes(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantConversationHistoryMessageRequest request);

    @PostExchange(value = "/conversation/message/rate", contentType = "application/json")
    BaseResponse<Void> messageRate(
            @RequestHeader("x-annotator-auth-token") String jwtToken,
            @RequestBody AssistantMessageRateRequest request);

}
