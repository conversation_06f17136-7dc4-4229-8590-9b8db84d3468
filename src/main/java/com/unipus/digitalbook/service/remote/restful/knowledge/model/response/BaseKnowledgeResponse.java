package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;

public class BaseKnowledgeResponse<T> implements Serializable {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    public Boolean isSuccess() {
        return this.code != null && (this.code == 200 || this.code == 0);
    }

    /**
     * 响应数据
     */
    private T result;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public static BaseKnowledgeResponse<Object> fromJsonString(String jsonString) {
        try {
            // 使用 Jackson 解析字符串内容
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(jsonString, new TypeReference<BaseKnowledgeResponse<Object>>() {});
        } catch (JsonProcessingException e) {
            BaseKnowledgeResponse<Object> response = new BaseKnowledgeResponse<>();
            response.setCode(500);
            response.setMessage("Failed to parse response: " + e.getMessage());
            response.setResult(jsonString);
            return response;
        }
    }
}
