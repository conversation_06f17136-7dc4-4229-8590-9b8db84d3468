package com.unipus.digitalbook.service.remote.restful.qrcode.model;

/**
 * 添加二维码 入参
 *
 * <AUTHOR>
 * @date 2025/2/13 17:05
 */
public class AddQrCodeRequest {
    private String courseId; // 教程ID
    private Integer qrcodeNum; // 生成二维码数量
    private Integer qrcodeSize; // 二维码尺寸
    private Integer linkType; // 链接类型 课程类型：1   其他类型：2

    // Getters and Setters
    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getQrcodeNum() {
        return qrcodeNum;
    }

    public void setQrcodeNum(Integer qrcodeNum) {
        this.qrcodeNum = qrcodeNum;
    }

    public Integer getQrcodeSize() {
        return qrcodeSize;
    }

    public void setQrcodeSize(Integer qrcodeSize) {
        this.qrcodeSize = qrcodeSize;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }
}
