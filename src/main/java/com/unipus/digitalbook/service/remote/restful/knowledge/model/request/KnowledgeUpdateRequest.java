package com.unipus.digitalbook.service.remote.restful.knowledge.model.request;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeGraphInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgeUpdateRequest {

    private Long id;
    private String knowledgeId;
    private String name;
    private String description;
    private String background;
    private List<KnowledgeGraphInfo> graphList;

    // Getters and Setters


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return graphList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> graphList) {
        this.graphList = graphList;
    }
}
