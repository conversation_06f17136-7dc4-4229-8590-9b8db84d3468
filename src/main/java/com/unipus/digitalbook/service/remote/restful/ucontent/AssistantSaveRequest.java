package com.unipus.digitalbook.service.remote.restful.ucontent;

import com.unipus.digitalbook.model.constants.AssistantConstant;
import lombok.Data;

import java.io.Serializable;

@Data
public class AssistantSaveRequest implements Serializable {

    private String title;

    private Integer configType;

    private Integer assistantType;

    private String scene;

    private String entityId;

    private final String platform = AssistantConstant.PLATFORM_IPUBLISH;

    private String templateId;

    private String config;
}
