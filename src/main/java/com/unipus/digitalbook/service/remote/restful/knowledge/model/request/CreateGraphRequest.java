package com.unipus.digitalbook.service.remote.restful.knowledge.model.request;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeGraphInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月27日 16:32
 */
public class CreateGraphRequest {
    private String name;
    private String description;
    private int status;
    private String background;
    private List<KnowledgeGraphInfo> graphList;
    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return graphList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> graphList) {
        this.graphList = graphList;
    }
}
