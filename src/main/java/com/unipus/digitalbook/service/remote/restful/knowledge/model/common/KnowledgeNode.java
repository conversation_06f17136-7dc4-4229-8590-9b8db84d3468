package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 *     focusId String Y 无 选中知识点 id
 *     graphId String Y 无 子图 id
 *     name String Y 无 名称
 *     type Integer Y 无 1-10
 *     labels List<String> N 无 知识标签
 *     study String N 无 学习目标
 *     content String N 无 知识描述
 *     resources List<Resource> N 无 关联资源列表
 */
public class KnowledgeNode {
    private String nodeId;
    private String focusId;
    private String graphId;
    private String name;
    private Integer type;
    private List<String> labels;
    private String study;
    private String content;
    private List<Resource> resources;

    // Getters and Setters
    public String getFocusId() {
        return focusId;
    }

    public void setFocusId(String focusId) {
        this.focusId = focusId;
    }

    public String getGraphId() {
        return graphId;
    }

    public void setGraphId(String graphId) {
        this.graphId = graphId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    public String getStudy() {
        return study;
    }

    public void setStudy(String study) {
        this.study = study;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<Resource> getResources() {
        return resources;
    }

    public void setResources(List<Resource> resources) {
        this.resources = resources;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
}

