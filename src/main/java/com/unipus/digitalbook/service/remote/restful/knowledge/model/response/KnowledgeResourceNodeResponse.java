package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import lombok.Data;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日 10:46
 */
@Data
public class KnowledgeResourceNodeResponse implements Serializable {
    private String graphId;
    private String knowledgeId;
    private String name;
    List<ResourceNode> nodes;

    @Data
    public static class ResourceNode implements Serializable {
        private String id;
        private String name;
        private List<ResourceNode> children;
    }
}
