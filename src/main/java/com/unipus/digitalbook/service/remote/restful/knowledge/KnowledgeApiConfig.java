package com.unipus.digitalbook.service.remote.restful.knowledge;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeException;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * 引擎的接口配置类
 */
@Slf4j
@Configuration
public class KnowledgeApiConfig {

    @Value("${remote.knowledge.host}")
    private String knowledgeHost;

    @Value("${remote.knowledge.timeout:5}")
    private Long knowledgeTimeOut;

    @Bean
    public KnowledgeApiService knowledgeApiService() {
        ExchangeFilterFunction logFilter = ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            // 获取完整的URL（包含查询参数）
            String fullUrl = clientRequest.url().toString();
            // 打印请求方法和URL
            log.info("Request: {} {}", clientRequest.method(), fullUrl);
            // 如果需要打印请求头
            clientRequest.headers().forEach((name, values) -> {
                values.forEach(value -> log.info("Request Header: {}={}", name, value));
            });
            return Mono.just(clientRequest);
        });

        // 添加响应处理过滤器
        ExchangeFilterFunction responseFilter = (request, next) ->
                next.exchange(request)
                        .flatMap(response -> {
                            return response.bodyToMono(String.class)
                                    .flatMap(body -> {
                                        log.info("response:{}", JSON.toJSONString(body));
                                        // 尝试解析字符串内容是否为 JSON
                                        if (body.startsWith("{") && body.endsWith("}")) {
                                            // 使用自定义方法解析
                                            BaseKnowledgeResponse<Object> result = BaseKnowledgeResponse.fromJsonString(body);
                                            if (!result.isSuccess()) {
                                                throw new KnowledgeException(result.getCode(), result.getMessage(), result.getResult());
                                            }

                                            // 创建新的响应对象
                                            return Mono.just(ClientResponse.create(response.statusCode())
                                                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                                                    .body(serializeToJson(result))
                                                    .build());
                                        } else {
                                            // 纯文本处理
                                            BaseKnowledgeResponse<String> result = new BaseKnowledgeResponse<>();
                                            result.setResult(body);
                                            result.setCode(Integer.valueOf(response.statusCode().value()));
                                            if (!result.isSuccess()) {
                                                throw new KnowledgeException(result.getCode(), result.getMessage());
                                            }
                                            return Mono.just(ClientResponse.create(response.statusCode())
                                                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                                                    .body(serializeToJson(result))
                                                    .build());
                                        }

                                    });
                        });

        WebClient webClient = WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .baseUrl(knowledgeHost)
                .filter(logFilter)
                .filter(responseFilter) // 添加响应处理器
                .build();

        WebClientAdapter adapter = WebClientAdapter.create(webClient);
        adapter.setBlockTimeout(Duration.ofSeconds(knowledgeTimeOut));

        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter)
                .build();

        return factory.createClient(KnowledgeApiService.class);
    }

    private String serializeToJson(Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON serialization error", e);
        }
    }
}
