package com.unipus.digitalbook.service.remote.restful.knowledge.model.request;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgeNodeBaseAddRequest {

    private String conceptName;
    private String graphId;
    private String name;
    private Integer type;

    // Get<PERSON> and Setters
    public String getConceptName() {
        return conceptName;
    }

    public void setConceptName(String conceptName) {
        this.conceptName = conceptName;
    }

    public String getGraphId() {
        return graphId;
    }

    public void setGraphId(String graphId) {
        this.graphId = graphId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}

