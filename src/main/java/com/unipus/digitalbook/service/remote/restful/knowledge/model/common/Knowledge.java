package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.io.Serializable;
import java.util.List;

public class Knowledge implements Serializable {
    private Long courseKnowledgeId;
    private String knowledgeId;
    private String name;
    private String description;
    private String background;
    private Integer status;
    private String userId;
    private String userName;
    private String gmtCreate;
    private String gmtModified;
    private String dr;
    private List<KnowledgeGraphInfo> graphList;

    public String getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(String gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(String gmtModified) {
        this.gmtModified = gmtModified;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return graphList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> graphList) {
        this.graphList = graphList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDr() {
        return dr;
    }

    public void setDr(String dr) {
        this.dr = dr;
    }

    public Long getCourseKnowledgeId() {
        return courseKnowledgeId;
    }

    public void setCourseKnowledgeId(Long courseKnowledgeId) {
        this.courseKnowledgeId = courseKnowledgeId;
    }
}