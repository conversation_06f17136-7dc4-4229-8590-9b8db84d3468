package com.unipus.digitalbook.service.remote.restful.ucontent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "数字人信息")
public class AssistantLatestInstanceResponse implements Serializable {
    @Schema(description = "数字人id")
    private String assistantId;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "场景")
    private String scene;

    @Schema(description = "实体")
    private String entityId;

    @Schema(description = "数字人实例id")
    private String assistantInstanceId;

    @Schema(description = "名称")
    private String title;

    @Schema(description = "内容")
    private String config;

    @Schema(description = "查询标志")
    private String searchFlag;

    @Schema(description = "业务标志")
    private String businessFlag;
}
