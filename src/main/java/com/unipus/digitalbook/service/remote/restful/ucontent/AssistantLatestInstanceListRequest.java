package com.unipus.digitalbook.service.remote.restful.ucontent;

import com.unipus.digitalbook.model.constants.AssistantConstant;
import lombok.Getter;

import java.util.List;

@Getter
public class AssistantLatestInstanceListRequest {
    private final String platform = AssistantConstant.PLATFORM_IPUBLISH;
    private final String scene;
    private final List<String> entityIds;

    public AssistantLatestInstanceListRequest(String scene, List<String> entityIds) {
        this.scene = scene;
        this.entityIds = entityIds;
    }
}
