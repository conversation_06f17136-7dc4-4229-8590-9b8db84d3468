package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年05月30日 09:55
 */
public class KnowledgeRelationQueryResponse implements Serializable {

    @JsonAlias(value = "domainId")
    private String graphId;
    private String id;
    private String name;

    private String score;
    public String getGraphId() {
        return graphId;
    }

    public void setGraphId(String graphId) {
        this.graphId = graphId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }
}
