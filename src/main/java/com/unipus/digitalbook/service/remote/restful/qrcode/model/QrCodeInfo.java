package com.unipus.digitalbook.service.remote.restful.qrcode.model;

/**
 * 二维码详情
 *
 * <AUTHOR>
 * @date 2025/2/13 16:50
 */
public class QrCodeInfo {
    private Long id; // 二维码主键ID
    private String courseId; // 教程ID
    private String codeShortUrl; // 短码
    private String randomCode; // 随机码
    private String resourceUrl; // 资源地址
    private Integer pageNum; // 页码
    private Integer supportVx; // 是否支持微信
    private Integer linkType; // 链接类型
    private String remark; // 备注
    private String created; // 创建时间
    private String modified; // 修改时间

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCodeShortUrl() {
        return codeShortUrl;
    }

    public void setCodeShortUrl(String codeShortUrl) {
        this.codeShortUrl = codeShortUrl;
    }

    public String getRandomCode() {
        return randomCode;
    }

    public void setRandomCode(String randomCode) {
        this.randomCode = randomCode;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getSupportVx() {
        return supportVx;
    }

    public void setSupportVx(Integer supportVx) {
        this.supportVx = supportVx;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getModified() {
        return modified;
    }

    public void setModified(String modified) {
        this.modified = modified;
    }
}
