package com.unipus.digitalbook.service.remote.restful.knowledge.model.common;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月30日 14:51
 */
public class PaginationResponse<T> implements Serializable {
    private int pageNum;
    private int pageSize;
    private long total;
    private int totalPage;
    private List<T> list;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
