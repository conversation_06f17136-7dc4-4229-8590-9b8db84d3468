package com.unipus.digitalbook.service.remote.restful.developer.model;

import com.unipus.digitalbook.model.entity.tenant.TenantMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Data
public class DeveloperSyncRequest extends TenantMessage implements Serializable {
    private String env;

    // todo 完成转换
    public DeveloperSyncRequest(TenantMessage tenantMessage, String env) {
        // 将 TenantMessage 的各个属性值设置到 DeveloperSyncRequest 对象中
        super.setId(tenantMessage.getId());
        super.setTenantId(tenantMessage.getTenantId());
        super.setMessageTopic(tenantMessage.getMessageTopic());
        super.setMessage(tenantMessage.getMessage());
        super.setMessageUuid(tenantMessage.getMessageUuid());
        super.setCreateTime(tenantMessage.getCreateTime());
        super.setUpdateTime(tenantMessage.getUpdateTime());
        super.setCreateBy(tenantMessage.getCreateBy());
        super.setUpdateBy(tenantMessage.getUpdateBy());
        super.setEnable(tenantMessage.getEnable());
        super.setAsync(tenantMessage.getAsync());
        super.setMessageObj(tenantMessage.getMessageObj());

        // 设置 env 属性
        this.env = env;
    }

    @Override
    public String toString() {
        return "DeveloperSyncRequest{" +
                "env='" + env + '\'' +
                ", id=" + getId() +
                ", tenantId=" + getTenantId() +
                ", messageTopic='" + getMessageTopic() + '\'' +
                ", message='" + getMessage() + '\'' +
                ", messageUuid='" + getMessageUuid() + '\'' +
                ", createTime=" + getCreateTime() +
                ", updateTime=" + getUpdateTime() +
                ", createBy=" + getCreateBy() +
                ", updateBy=" + getUpdateBy() +
                ", enable=" + getEnable() +
                ", async=" + getAsync() +
                ", messageObj=" + getMessageObj() +
                '}';
    }
}

