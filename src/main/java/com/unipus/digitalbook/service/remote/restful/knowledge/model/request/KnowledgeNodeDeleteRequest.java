package com.unipus.digitalbook.service.remote.restful.knowledge.model.request;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月29日 15:17
 * graphId String Y 无 子图 id
 * nodeIds List<String> Y 无 删除知识点 id 列表
 */
public class KnowledgeNodeDeleteRequest {
    private String graphId;
    private List<String> nodeIds;

    public String getGraphId() {
        return graphId;
    }

    public void setGraphId(String graphId) {
        this.graphId = graphId;
    }

    public List<String> getNodeIds() {
        return nodeIds;
    }

    public void setNodeIds(List<String> nodeIds) {
        this.nodeIds = nodeIds;
    }
}
