package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeGraphInfo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgeGetResponse implements Serializable {

    private String knowledgeId;
    private String name;
    private String description;
    private String background;
    private Integer status;
    private Long gmtCreate;
    private Long gmtModified;
    private List<KnowledgeGraphInfo> knowledgeGraphInfoList;

    public String getKnowledgeId() {
        return knowledgeId;
    }

    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Long getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return knowledgeGraphInfoList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> knowledgeGraphInfoList) {
        this.knowledgeGraphInfoList = knowledgeGraphInfoList;
    }
}
