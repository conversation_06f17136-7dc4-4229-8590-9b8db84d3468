package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@ToString
public class GraphNodeInfoResponse implements Serializable {

    private List<GraphNode> nodes; // 节点列表

    private List<Relation> relations; // 关系列表


    public List<GraphNode> getNodes() {
        return nodes;
    }

    public void setNodes(List<GraphNode> nodes) {
        this.nodes = nodes;
    }

    public List<Relation> getRelations() {
        return relations;
    }

    public void setRelations(List<Relation> relations) {
        this.relations = relations;
    }

    public static class GraphNode implements Serializable {

        @JsonAlias("$id")
        private Long id; // 实体 id
        @JsonAlias("$label")
        private String label; // 实体类型：教材、知识点、教材资源、拓展资源
        private String name; // 实体名称
        private String graphId;
        @JsonAlias("知识类型")
        private String knowledgeType; // 知识类型 0-10
        @JsonAlias("知识标签")
        private List<String> knowledgeTags; // 知识标签
        @JsonAlias("学习目标")
        private String learningObjective; // 学习目标
        @JsonAlias("知识描述")
        private String knowledgeDescription; // 知识描述

        private Map resourcesCount; // 挂载资源数

        // Getters and Setters

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getGraphId() {
            return graphId;
        }

        public void setGraphId(String graphId) {
            this.graphId = graphId;
        }

        public String getKnowledgeType() {
            return knowledgeType;
        }

        public void setKnowledgeType(String knowledgeType) {
            this.knowledgeType = knowledgeType;
        }

        public List<String> getKnowledgeTags() {
            return knowledgeTags;
        }

        public void setKnowledgeTags(List<String> knowledgeTags) {
            this.knowledgeTags = knowledgeTags;
        }

        public String getLearningObjective() {
            return learningObjective;
        }

        public void setLearningObjective(String learningObjective) {
            this.learningObjective = learningObjective;
        }

        public String getKnowledgeDescription() {
            return knowledgeDescription;
        }

        public void setKnowledgeDescription(String knowledgeDescription) {
            this.knowledgeDescription = knowledgeDescription;
        }

        public Map getResourcesCount() {
            return resourcesCount;
        }

        public void setResourcesCount(Map resourcesCount) {
            this.resourcesCount = resourcesCount;
        }
    }

    public static class Relation implements Serializable {
        @JsonAlias("$id")
        private Long id; // 关系 id
        @JsonAlias("$type")
        private String type; // 关系名称：包含、关联资源、其他用户自定义关系
        @JsonAlias("$startId")
        private Long startId; // 首实体 id
        @JsonAlias("$startName")
        private String startName; // 首实体名称
        @JsonAlias("$endId")
        private Long endId; // 尾实体 id
        @JsonAlias("$endName")
        private String endName; // 尾实体名称
        @JsonAlias("$index")
        private Integer index; // 包含关系顺序数

        // Getters and Setters

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Long getStartId() {
            return startId;
        }

        public void setStartId(Long startId) {
            this.startId = startId;
        }

        public String getStartName() {
            return startName;
        }

        public void setStartName(String startName) {
            this.startName = startName;
        }

        public Long getEndId() {
            return endId;
        }

        public void setEndId(Long endId) {
            this.endId = endId;
        }

        public String getEndName() {
            return endName;
        }

        public void setEndName(String endName) {
            this.endName = endName;
        }

        public Integer getIndex() {
            return index;
        }

        public void setIndex(Integer index) {
            this.index = index;
        }
    }

    public static void main(String[] args) {
        String json = "{\"nodes\":[{\"id\":69,\"label\":\"教材\",\"name\":\"梁教材知识图谱名称\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":0,\"knowledgeTags\":null,\"learningObjective\":null,\"knowledgeDescription\":null,\"resourcesCount\":0},{\"id\":226,\"label\":\"知识点\",\"name\":\"1-4节点\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":1,\"knowledgeTags\":[\"1-4标签\"],\"learningObjective\":\"1-4学习目标\",\"knowledgeDescription\":\"1-4内容\",\"resourcesCount\":1},{\"id\":224,\"label\":\"知识点\",\"name\":\"1-3节点\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":1,\"knowledgeTags\":[\"1-3标签\"],\"learningObjective\":\"1-3学习目标\",\"knowledgeDescription\":\"1-3内容\",\"resourcesCount\":1},{\"id\":73,\"label\":\"知识点\",\"name\":\"1-2节点\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":1,\"knowledgeTags\":[\"1-2标签\"],\"learningObjective\":\"1-2学习目标\",\"knowledgeDescription\":\"1-2内容\",\"resourcesCount\":1},{\"id\":71,\"label\":\"知识点\",\"name\":\"1-1节点\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":1,\"knowledgeTags\":[\"1-1标签\"],\"learningObjective\":\"1-1学习目标\",\"knowledgeDescription\":\"1-1内容\",\"resourcesCount\":1},{\"id\":72,\"label\":\"教材资源\",\"name\":\"教材资源name\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":null,\"knowledgeTags\":null,\"learningObjective\":null,\"knowledgeDescription\":null,\"resourcesCount\":null},{\"id\":74,\"label\":\"教材资源\",\"name\":\"教材资源name\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":null,\"knowledgeTags\":null,\"learningObjective\":null,\"knowledgeDescription\":null,\"resourcesCount\":null},{\"id\":225,\"label\":\"教材资源\",\"name\":\"教材资源name1\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":null,\"knowledgeTags\":null,\"learningObjective\":null,\"knowledgeDescription\":null,\"resourcesCount\":null},{\"id\":227,\"label\":\"教材资源\",\"name\":\"教材资源name\",\"graphId\":\"49361897ebbc4062a3df984a6e02f367\",\"knowledgeType\":null,\"knowledgeTags\":null,\"learningObjective\":null,\"knowledgeDescription\":null,\"resourcesCount\":null}],\"relations\":[{\"id\":212,\"type\":\"包含\",\"startId\":69,\"startName\":\"梁教材知识图谱名称\",\"endId\":226,\"endName\":\"1-4节点\",\"index\":4},{\"id\":59,\"type\":\"包含\",\"startId\":69,\"startName\":\"梁教材知识图谱名称\",\"endId\":224,\"endName\":\"1-3节点\",\"index\":3},{\"id\":57,\"type\":\"包含\",\"startId\":69,\"startName\":\"梁教材知识图谱名称\",\"endId\":73,\"endName\":\"1-2节点\",\"index\":2},{\"id\":55,\"type\":\"包含\",\"startId\":69,\"startName\":\"梁教材知识图谱名称\",\"endId\":71,\"endName\":\"1-1节点\",\"index\":1},{\"id\":56,\"type\":\"关联资源\",\"startId\":71,\"startName\":\"1-1节点\",\"endId\":72,\"endName\":\"教材资源name\",\"index\":1},{\"id\":58,\"type\":\"关联资源\",\"startId\":73,\"startName\":\"1-2节点\",\"endId\":74,\"endName\":\"教材资源name\",\"index\":1},{\"id\":211,\"type\":\"关联资源\",\"startId\":224,\"startName\":\"1-3节点\",\"endId\":225,\"endName\":\"教材资源name1\",\"index\":1},{\"id\":213,\"type\":\"关联资源\",\"startId\":226,\"startName\":\"1-4节点\",\"endId\":227,\"endName\":\"教材资源name\",\"index\":1}]}";

        GraphNodeInfoResponse graphNodeInfoResponse = JSON.parseObject(json, GraphNodeInfoResponse.class);
        List<GraphNode> nodes = graphNodeInfoResponse.getNodes();
        List<Relation> relations = graphNodeInfoResponse.getRelations();

    }
}
