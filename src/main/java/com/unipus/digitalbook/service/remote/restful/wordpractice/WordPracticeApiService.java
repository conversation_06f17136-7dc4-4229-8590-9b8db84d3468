package com.unipus.digitalbook.service.remote.restful.wordpractice;

import com.unipus.digitalbook.service.remote.restful.wordpractice.model.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.Map;

/**
 * 词汇学练接口
 *
 * <AUTHOR>
 * @date 2025/7/15 19:33
 */
@HttpExchange("/api/vocabulary")
public interface WordPracticeApiService {

    @PostExchange(value = "/manage/v2/saveQuestion")
    WordPracticeApiResponse saveQuestion(@RequestBody AddWordPracticeRequest addWordPracticeRequest, @RequestHeader Map<String, String> headers);

    @PostExchange(value = "/manage/v2/updateQuestion")
    WordPracticeApiResponse updateQuestion(@RequestBody UpdateWordPracticeRequest updateWordPracticeRequest, @RequestHeader Map<String, String> headers);

    @PostExchange(value = "/manage/ques/publish")
    WordPracticeApiResponse publishQuestion(@RequestBody PublishWordPracticeRequest publishWordPracticeRequest, @RequestHeader Map<String, String> headers);

    @PostExchange(value = "/manage/detail")
    WordPracticeApiResponse detail(@RequestBody DetailWordPracticeRequest detailWordPracticeRequest, @RequestHeader Map<String, String> headers);

    @PostExchange(value = "/manage/del")
    WordPracticeApiResponse delQuestion(@RequestBody DelWordPracticeRequest delWordPracticeRequest, @RequestHeader Map<String, String> headers);
}
