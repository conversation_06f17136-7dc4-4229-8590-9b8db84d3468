package com.unipus.digitalbook.service.remote.restful.qrcode.model;

/**
 * 更新二维码 入参
 *
 * <AUTHOR>
 * @date 2025/2/13 17:05
 */
public class UpdateQrCodeRequest {
    private Long id; // 二维码ID
    private String courseId; // 教程ID
    private Integer pageNum; // 页码
    private Integer supportVx; // 是否支持微信
    private String remark; // 备注
    private String resourceUrl; // 资源地址
    private Integer linkType; // 链接类型

    // Get<PERSON> and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getSupportVx() {
        return supportVx;
    }

    public void setSupportVx(Integer supportVx) {
        this.supportVx = supportVx;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }
}
