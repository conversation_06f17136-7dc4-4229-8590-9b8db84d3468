package com.unipus.digitalbook.service.remote.restful.knowledge.model.response;

import lombok.Data;

import java.io.Serializable;

/**
 * https://docs.qq.com/doc/DSmxjd2RLdkRZbVVL?no_promotion=1
 *
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
@Data
public class KnowledgeVersionListResponse implements Serializable {
    private String versionId;
    private String description;
    private String gmtCreate;
    private String gmtModified;
    private String userId;
    private String userName;
    /**
     * 0：未发布
     * * 1：已发布
     * * 2：发布失败
     * * 3：发布中
     * * 4：已上线
     * * 5：检测中
     * * 6：检测失败
     */
    private Integer status;

}
