package com.unipus.digitalbook.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * 业务异常信息
 */
@Getter
@AllArgsConstructor
public enum ResultMessage {
    // 系统错误:0-999
    SYS_INTERNAL_SERVER_ERROR(500, "系统内部错误"),
    SYS_ONLY_DEV_ENV_USE_ERROR(550, "仅本地开发环境可使用的接口。{%s}"),

    // 业务错误:1000-1999
    BIZ_RESOURCE_PERMISSION_ADD_FAIL(1000, "%s权限追加失败"),
    BIZ_MEDIA_REFERENCE_ADD_FAIL(1002, "教材媒体引用关系追加失败:%s"),
    BIZ_READ_TEMPLATE_FILE_FAIL(1003, "%s模板文件读取失败"),
    BIZ_LOCAL_ADD_QUESTION_FAIL(1004, "题目本地创建失败，题目ID: {%s}"),
    BIZ_REMOTE_ADD_QUESTION_FAIL(1005, "题目远程同步失败，题目ID: {%s}"),
    BIZ_LOCAL_UPDATE_QUESTION_FAIL(1006, "题目本地更新失败，题目ID: {%s}"),
    BIZ_REMOTE_UPDATE_QUESTION_FAIL(1007, "题目远程更新失败，题目ID: {%s}"),
    BIZ_QUES_PARAM_IS_NULL(1008, "题目参数为空" ),
    BIZ_TASK_IS_RUNNING(1009, "任务处理中，请稍后重试。" );

    private final Integer code;
    private final String message;

    /**
     * 取得填充参数消息
     * @param args ：消息参数列表
     * @return 消息内容
     */
    public String getMessage(Object... args){
        return ObjectUtils.isEmpty(args) ? this.message : String.format(this.getMessage(), args);
    }

    /**
     * 根据code获取消息
     * @param code ：消息编码
     * @return 消息对象
     */
    public static ResultMessage getMessage(Integer code){
        return Arrays.stream(ResultMessage.values()).filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }
}