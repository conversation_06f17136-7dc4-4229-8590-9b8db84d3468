package com.unipus.digitalbook.model.entity.cos;

public class MediaTranscodeMessage {

    /**
     * 文件URL
     */
    private String url;

    /**
     * 文件hash值
     */
    private String hash;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    public MediaTranscodeMessage() {
    }

    public MediaTranscodeMessage(String url, String hash) {
        this.url = url;
        this.hash = hash;
        this.retryCount = 0;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
}