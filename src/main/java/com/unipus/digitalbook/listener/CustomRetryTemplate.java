package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.exception.business.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Component;

import java.util.function.Function;

/**
 * 方法自动重试模板类
 * 自动重试自定次数，重试次数超过后自动执行@Recover注解的方法
 * customRetryListener 是自定义的RetryListener，用于记录重试日志
 */
@Component
@Slf4j
public class CustomRetryTemplate {

    /**
     * 实现重试机制的方法(默认拦截RuntimeException及其子类，如果有其他异常需要拦截设置retryFor参数)
     * maxAttempts = 3: 最多重试3次（1次初始 + 2次重试）
     * backoff: 定义重试的延迟策略（multiplier：设置递增延迟的倍数）
     * listeners: 是自定义的RetryListener，用于记录重试日志
     */
    @Retryable(
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            retryFor = {RuntimeException.class, Exception.class, Error.class},
            listeners = {"customRetryListener"}
    )
    public <T, V> V handle(T event, Function<T, V> processor, String callerInfo) throws BizException {
        // 获取当前的 RetryContext
        RetryContext retryContext = RetrySynchronizationManager.getContext();
        if (retryContext != null) {
            retryContext.setAttribute("CALLER_INFO", callerInfo);
        }
        // 执行业务处理
        return processor.apply(event);
    }

    /**
     * 重试3次后仍然失败,自动执行此方法
     * @param e 异常
     * @param event 事件
     */
    @Recover
    protected void recoverProcess(RuntimeException e, Object event) {
        log.error("重试3次后仍然失败, 触发事件: {}, 异常信息: {}", event, e.getMessage(), e);
        // todo 增加数据库日志输出
        // 这里可以添加告警通知、补偿逻辑等，如发送告警邮件、消息通知等
    }
}