package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;
import com.unipus.digitalbook.service.TenantMessageService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class TenantMessageListener {

    @Resource
    private TenantMessageService tenantMessageService;

    @SneakyThrows
    @KafkaListener(topics = "${kafka.topic.tenantMessageSaveTopic}")
    public void saveTenantMessage(String jsonMessage){
        log.info("tenantMessageSaveTopic {}", jsonMessage);
        TenantMessagePO tenantMessagePO = JsonUtil.parseObject(jsonMessage, TenantMessagePO.class);
        tenantMessageService.insertMessage(tenantMessagePO);
    }

    @SneakyThrows
    @KafkaListener(topics = "${kafka.topic.tenantMessageDeleteTopic}")
    public void deleteTenantMessage(String messageId){
        log.info("tenantMessageDeleteTopic {}", messageId);
        tenantMessageService.deleteById(Long.valueOf(messageId));
    }
}
