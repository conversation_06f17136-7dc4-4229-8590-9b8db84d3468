package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.remote.restful.soe.SoeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * clio引擎消费者
 *
 * <AUTHOR>
 * @date 2025/3/26 11:17
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable", havingValue = "true", matchIfMissing = true)
public class ClioResultListener {

    private static final String MESSAGE_FIELD = "message";
    private static final String FINAL_RESULT_FIELD = "finalResult";
    private static final String UUID_FIELD = "uuid";
    private static final String SOE_LIMIT_FIELD = "soeLimit";
    private static final String OPEN_ID_FIELD = "openId";

    @Resource
    UserAnswerService userAnswerService;
    @Resource
    private SoeService soeService;

    /**
     * 处理Clio评测结果消息
     * 消息格式：
     * {
     * "message": {
     * "finalResult": {
     * "uuid": "业务答案ID",
     * ...评测详细信息
     * }
     * }
     * }
     *
     * @param message 消息内容
     */
    @KafkaListener(topics = "${kafka.topic.clioResult}")
    public void processMessage(String message) {
        log.info("received subscribe message: {}", message);
        if (!JSON.isValid(message)) {
            log.warn("invalid message format: {}", message);
            return;
        }
        try {
            // 解析消息主体
            JSONObject data = JSON.parseObject(message);
            String messageContent = data.getString(MESSAGE_FIELD);
            if (!JSON.isValid(messageContent)) {
                log.warn("invalid message content format: {}", messageContent);
                return;
            }
            // 解析Clio评测结果
            JSONObject clioResult = JSON.parseObject(messageContent);
            JSONObject finalResult = clioResult.getJSONObject(FINAL_RESULT_FIELD);
            if (finalResult == null || finalResult.isEmpty()) {
                log.warn("empty final result in message: {}", messageContent);
                return;
            }
            // 提取业务答案ID和评测结果
            String evaluation = clioResult.toString();
            String bizAnswerId = finalResult.getString(UUID_FIELD);
            if (!StringUtils.hasText(bizAnswerId)) {
                log.warn("empty bizAnswerId in finalResult: {}", finalResult);
                return;
            }
            // 处理评测结果：同步日志并更新用户答案评测结果
            JSONObject callbackBody = userAnswerService.getAnswerCallback(bizAnswerId);
            if (callbackBody == null || callbackBody.isEmpty()) {
                log.warn("no user answer found for bizAnswerId: {}", bizAnswerId);
                return;
            }
            String soeLimit = callbackBody.getString(SOE_LIMIT_FIELD);
            String openId = callbackBody.getString(OPEN_ID_FIELD);
            if (!StringUtils.hasText(soeLimit) || !StringUtils.hasText(openId)) {
                log.warn("missing required fields in userAnswer: soeLimit={}, openId={}", soeLimit, openId);
                return;
            }
            soeService.syncClioLog(soeLimit, openId, evaluation);
            Boolean done = userAnswerService.setAnswerCallbackEvaluation(bizAnswerId, evaluation);
            if (Boolean.FALSE.equals(done)) {
                log.warn("failed to update evaluation for bizAnswerId: {}", bizAnswerId);
            } else {
                log.info("successfully processed evaluation for bizAnswerId: {}", bizAnswerId);
            }
        } catch (Exception e) {
            log.error("failed to process message: {}, error: {}", message, e.getMessage(), e);
        }
    }
}
