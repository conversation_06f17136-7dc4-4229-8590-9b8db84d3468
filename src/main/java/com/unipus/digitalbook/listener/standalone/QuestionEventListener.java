package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.QuestionEvent;
import com.unipus.digitalbook.service.QuestionPushService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QuestionEventListener {

    @Resource
    private QuestionPushService questionPushService;

    @EventListener
    public void handleQuestionEvent(QuestionEvent event) {
        log.debug("收到题目事件, questionId: {}, event: {}", event.getQuestionGroup().getId(), event.getEventType());
        if (event.getEventType() == EventTypeEnum.ADD_VERSION) {
            // todo 题目同步的时候还没有租户的概念，目前线默认同步给uai租户的题库服务 1
            questionPushService.pushQuestionToThirdSync(event.getQuestionGroup(), 1L);
        }
    }
}
