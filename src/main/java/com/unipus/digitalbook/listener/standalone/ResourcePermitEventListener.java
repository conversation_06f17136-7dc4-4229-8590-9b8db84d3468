package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.book.BookUserActivity;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.enums.UserActivityTypeEnum;
import com.unipus.digitalbook.model.events.ResourcePermitChangeEvent;
import com.unipus.digitalbook.service.BookUserActivityService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ResourcePermitEventListener {
    @Resource
    private BookUserActivityService bookUserActivityService;

    @EventListener
    public void handlePermitChangeEvent(ResourcePermitChangeEvent event) {
        log.info("ResourcePermitEventListener handlePermitChangeEvent: {}", JsonUtil.toJsonString(event));
        List<ResourcePermission> params = event.getParams();
        if (params == null || params.isEmpty()) {
            return;
        }
        List<BookUserActivity> activities = new ArrayList<>();
        params.stream().filter(paramsItem -> ResourceTypeEnum.BOOK.getCode().equals(paramsItem.getResourceType()))
                .filter(paramsItem -> paramsItem.getPermissionType() != null)
                .forEach(paramsItem -> {
                    Integer prePermissionType = paramsItem.getOldPermissionType();
                    Integer permissionType = paramsItem.getPermissionType();
                    Boolean preHasSharePermission = PermissionTypeEnum.SHARE.match(prePermissionType);
                    Boolean hasSharePermission = PermissionTypeEnum.SHARE.match(permissionType);
                    Boolean preHasReadPermission = PermissionTypeEnum.READ.match(prePermissionType);
                    Boolean hasReadPermission = PermissionTypeEnum.READ.match(permissionType);
                    Boolean ownerPermission = PermissionTypeEnum.OWNER.match(prePermissionType);
                    // 分享权限的变革
                    if (!preHasSharePermission.booleanValue() && hasSharePermission.booleanValue()) {
                        activities.add(new BookUserActivity(paramsItem.getUserId(),
                                paramsItem.getResourceId(),
                                UserActivityTypeEnum.JOIN_PREVIEW.getCode(),
                                new Date(event.getTimestamp()),
                                event.getOpUserId(), true));
                    }
                    // 教材阅读权限的变更, 编者不参与变更
                    if (!ownerPermission.booleanValue()) {
                        if (!preHasReadPermission.booleanValue() && hasReadPermission.booleanValue()) {
                            // 新增或者更新用户的加入编辑记录
                            activities.add(new BookUserActivity(paramsItem.getUserId(),
                                    paramsItem.getResourceId(),
                                    UserActivityTypeEnum.JOIN_EDIT.getCode(),
                                    new Date(event.getTimestamp()),
                                    event.getOpUserId(), true));
                        }
                        if (preHasReadPermission.booleanValue() && !hasReadPermission.booleanValue()) {
                            // 删除用户的编辑记录
                            activities.add(new BookUserActivity(paramsItem.getUserId(),
                                    paramsItem.getResourceId(),
                                    UserActivityTypeEnum.EDIT.getCode(),
                                    new Date(event.getTimestamp()),
                                    event.getOpUserId(), false));
                        }
                    }
                });
        bookUserActivityService.batchSaveActivity(activities);
    }
}
