package com.unipus.digitalbook.listener.standalone;


import com.unipus.digitalbook.model.events.UserDeleteEvent;
import com.unipus.digitalbook.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
public class UserEventListener {

    private final UserService userService; // 注入用户服务

    public UserEventListener(UserService userService) {
        this.userService = userService;
    }

    @EventListener
    @Transactional
    public void handleUserDeleteEvent(UserDeleteEvent event) {
        List<Long> userIds = event.getUserIds();
        log.info("接收到用户删除事件，待删除用户ID: {}", userIds);

        try {
            // 执行用户删除逻辑
            int rows= userService.deleteUsers(userIds, event.getOpUserId());
            log.info("用户删除成功，已删除 {} 个用户", rows);
        } catch (Exception e) {
            //todo 添加错误记录
            //todo 添加重试机制

            log.error("用户删除失败", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }
}
