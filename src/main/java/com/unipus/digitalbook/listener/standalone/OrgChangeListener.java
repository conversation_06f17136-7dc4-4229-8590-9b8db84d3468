package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.common.utils.RedisUtil;
import com.unipus.digitalbook.listener.CustomRetryTemplate;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.events.TableChangeEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织变更事件监听器
 */
@Component
@Slf4j
public class OrgChangeListener{

    private static final List<String> TARGET_TABLE_LIST = List.of("organization");

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CustomRetryTemplate customRetryTemplate;

    /**
     * 处理表变更事件
     * @param event 表变更事件
     */
    @EventListener
    public void handleTableChangeEvent(TableChangeEvent event) {
        if(!TARGET_TABLE_LIST.contains(event.getTableName())) {
            return;
        }
        customRetryTemplate.handle(event, e -> {
            log.debug("正在删除组织缓存, 触发事件: {}", event.getSource());
            redisUtil.deleteFromBucket(CacheConstant.REDIS_ORG_KEY);
            log.debug("组织缓存删除成功");
            return null;
        }, getCallerInfo());
    }

    private String getCallerInfo(){
        return this.getClass().getSimpleName() + ".handleTableChangeEvent";
    }
}
