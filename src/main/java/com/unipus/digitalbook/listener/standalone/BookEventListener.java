package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.book.BookUserActivity;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.enums.UserActivityTypeEnum;
import com.unipus.digitalbook.model.events.BookEvent;
import com.unipus.digitalbook.service.BookUserActivityService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class BookEventListener {
    @Resource
    private BookUserActivityService bookUserActivityService;

    @EventListener
    public void handleBookEvent(BookEvent event) {
        log.debug("收到教材事件: {}", JsonUtil.toJsonString(event));
        EventTypeEnum eventType = event.getEventType();
        List<BookUserActivity> activities = new ArrayList<>();
        if (EventTypeEnum.ADD.equals(eventType)) {
            activities.add(new BookUserActivity(event.getOpsUserId(),
                    event.getBookId(),
                    UserActivityTypeEnum.JOIN_EDIT.getCode(),
                    new Date(event.getTimestamp()),
                    event.getOpsUserId(), true));
            activities.add(new BookUserActivity(event.getOpsUserId(),
                    event.getBookId(),
                    UserActivityTypeEnum.EDIT.getCode(),
                    new Date(event.getTimestamp()),
                    event.getOpsUserId(), true));
        } else if (EventTypeEnum.EDIT.equals(eventType)) {
            activities.add(new BookUserActivity(event.getOpsUserId(),
                    event.getBookId(),
                    UserActivityTypeEnum.EDIT.getCode(),
                    new Date(event.getTimestamp()),
                    event.getOpsUserId(), true));
        }
        bookUserActivityService.batchSaveActivity(activities);
    }
}
