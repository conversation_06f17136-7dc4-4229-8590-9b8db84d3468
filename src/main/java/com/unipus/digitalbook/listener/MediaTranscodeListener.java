package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.model.ciModel.job.MediaJobObject;
import com.qcloud.cos.utils.StringUtils;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeMessage;
import com.unipus.digitalbook.model.enums.MediaTranscodeFormatEnum;
import com.unipus.digitalbook.service.COSService;
import jakarta.annotation.Resource;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 媒体转码任务监听器
 *
 * <AUTHOR>
 * @date 2024/8/27 10:00
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable", havingValue = "true", matchIfMissing = true)
public class MediaTranscodeListener {

    @Resource
    private COSService cosService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${kafka.topic.mediaTranscode}")
    private String topicMediaTranscode;

    private static final int MAX_RETRY_COUNT = 2; // 最大全局重试2次
    private static final int MAX_WAIT_MINUTES = 5; // 最大等待5分钟
    private static final int CHECK_INTERVAL_SECONDS = 10; // 每10秒检查一次
    private static final int MAX_TASK_RETRY_COUNT = 2; // 单个转码任务最大重试次数
    private static final int RETRY_WAIT_BASE_MS = 2000; // 重试等待基础时间

    @KafkaListener(topics = "${kafka.topic.mediaTranscode}")
    public void processMessage(String message) {
        if (!JSON.isValid(message)) {
            log.warn("无效的消息格式: {}", message);
            return;
        }
        MediaTranscodeMessage mediaTranscodeMessage = JsonUtil.parseObject(message, MediaTranscodeMessage.class);
        String url = mediaTranscodeMessage.getUrl();
        String hash = mediaTranscodeMessage.getHash();
        Integer retryCount = mediaTranscodeMessage.getRetryCount();
        // 获取需要转码的格式列表
        List<MediaTranscodeFormatEnum> formats = cosService.getVideoMediaTranscodeFormats(url);
        if (formats.isEmpty()) {
            log.info("没有需要转码的格式，url: {}", url);
            return;
        }
        log.info("开始处理视频转码任务，url: {}, hash: {}, 全局重试次数: {}", url, hash, retryCount);
        try {
//            // 串行处理每个转码格式
//            for (MediaTranscodeFormatEnum format : formats) {
//                String formatName = format.name();
//                processTranscodeFormat(formatName, url, hash);
//            }
            // 使用Lists.partition和虚拟线程处理
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
                int groupSize = formats.size() / 5 + (formats.size() % 5 == 0 ? 0 : 1);
                List<CompletableFuture<Void>> futures = Lists.partition(formats, groupSize).stream()
                        .map(group -> CompletableFuture.runAsync(() -> {
                            group.forEach(format -> {
                                String formatName = format.name();
                                processTranscodeFormat(formatName, url, hash);
                            });
                        }, executor))
                        .toList();
                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
            // 生成并上传HLS主播放列表
            List<MediaTranscodeFormatEnum> hlsFormats = formats.stream()
                    .filter(f -> f.name().startsWith("HLS_"))
                    .toList();
            if (!hlsFormats.isEmpty()) {
                cosService.generateHlsMasterPlaylist(hlsFormats, url, hash);
            }
            // 等待所有任务完成
            waitAllJobComplete(url);
            log.info("视频转码任务处理完成，url: {}, 全局重试次数: {}", url, retryCount);
        } catch (Exception e) {
            log.error("处理视频转码任务失败，url: {}, 全局重试次数: {}", url, retryCount, e);
            // 全局重试逻辑
            if (mediaTranscodeMessage.getRetryCount() < MAX_RETRY_COUNT) {
                // 全局重试：增加重试次数后重新发送到同一个队列
                mediaTranscodeMessage.setRetryCount(mediaTranscodeMessage.getRetryCount() + 1);
                kafkaTemplate.send(topicMediaTranscode, JsonUtil.toJsonString(mediaTranscodeMessage));
                log.info("视频转码任务将进行全局重试，url: {}, 全局重试次数: {}/{}, 异常: {}",
                        mediaTranscodeMessage.getUrl(), mediaTranscodeMessage.getRetryCount(), MAX_RETRY_COUNT, e.getMessage());
            } else {
                log.error("视频转码任务最终失败，url: {}, 已达到最大全局重试次数: {}, 异常: {}",
                        mediaTranscodeMessage.getUrl(), MAX_RETRY_COUNT, e.getMessage());
            }
        }
    }

    /**
     * 处理单个转码格式，包含任务级别的重试逻辑
     *
     * @param formatName 转码格式名称
     * @param url        视频URL
     * @param hash       视频hash
     */
    private void processTranscodeFormat(String formatName, String url, String hash) {
        int retryCount = 0;
        boolean isFirstAttempt = true;
        while (retryCount <= MAX_TASK_RETRY_COUNT) {
            try {
                if (isFirstAttempt) {
                    log.info("开始处理转码格式，url: {}, format: {}", url, formatName);
                    isFirstAttempt = false;
                } else {
                    log.info("重试处理转码格式，url: {}, format: {}, 第{}次重试", url, formatName, retryCount);
                }
                // 检查是否已存在转码文件
                if (!(StringUtils.isNullOrEmpty(hash) ||
                        StringUtils.isNullOrEmpty(cosService.checkMediaTranscodeJob(formatName, url, hash)))) {
                    log.info("转码文件已存在，跳过创建，url: {}, format: {}", url, formatName);
                    return;
                }
                // 创建转码任务
                String jobId = cosService.createAndMarkMediaTranscodeJob(formatName, url, hash);
                if (jobId != null) {
                    // 等待任务完成
                    waitJobComplete(jobId, formatName, url);
                    return;
                }
            } catch (CosClientException e) {
                log.error("COS异常，url: {}, format: {}, 重试次数: {}", url, formatName, retryCount, e);
                if (retryCount >= MAX_TASK_RETRY_COUNT) {
                    log.error("COS异常已达到最大重试次数，将触发全局重试，url: {}, format: {}, 最大重试次数: {}",
                            url, formatName, MAX_TASK_RETRY_COUNT);
                    throw new RuntimeException("COS异常重试失败，format: " + formatName + ", url: " + url, e);
                }
                // 任务重试前等待一段时间（递增等待）
                retryCount++;
                int waitTime = RETRY_WAIT_BASE_MS * retryCount;
                log.info("任务重试前等待 {}ms，url: {}, format: {}", waitTime, url, formatName);
                ThreadUtil.sleep(waitTime);
            }
        }
    }

    /**
     * 等待单个转码任务完成
     */
    private void waitJobComplete(String jobId, String formatName, String url) {
        log.info("开始等待转码任务完成，url: {}, format: {}, jobId: {}", url, formatName, jobId);
        int maxChecks = (MAX_WAIT_MINUTES * 60) / CHECK_INTERVAL_SECONDS;
        for (int checkCount = 1; checkCount <= maxChecks; checkCount++) {
            COSMediaTranscodeJob job = cosService.getMediaTranscodeJob(jobId);
            String state = job.getState();
            log.info("检查转码任务状态，url: {}, format: {}, jobId: {}, status: {}, 第{}次检查",
                    url, formatName, jobId, state, checkCount);
            if ("Success".equals(state)) {
                log.info("转码任务成功完成，url: {}, jobId: {}, format: {}", url, jobId, formatName);
                return;
            } else if ("Failed".equals(state)) {
                log.error("转码任务失败，url: {}, jobId: {}, format: {}, 错误信息: {}",
                        url, jobId, formatName, job.getMessage());
                throw new COSException("转码任务失败: " + job.getMessage());
            } else if ("Cancel".equals(state)) {
                log.warn("转码任务被取消，url: {}, jobId: {}, format: {}", url, jobId, formatName);
                return;
            }
            // 等待指定时间后再次检查
            ThreadUtil.sleep(CHECK_INTERVAL_SECONDS * 1000);
        }
    }

    /**
     * 等待所有转码任务完成
     */
    private void waitAllJobComplete(String url) {
        int maxChecks = (MAX_WAIT_MINUTES * 60) / CHECK_INTERVAL_SECONDS;
        for (int checkCount = 1; checkCount <= maxChecks; checkCount++) {
            List<MediaJobObject> jobObjectList = cosService.getMediaTranscodeJobs();
            if (jobObjectList == null || jobObjectList.isEmpty()) {
                return;
            }
            log.info("任务状态统计-url:{}, 第{}次检查, 总数: {}, 已提交: {}, 执行中: {}",
                    url,
                    checkCount,
                    jobObjectList.size(),
                    jobObjectList.stream().filter(job -> "Submitted".equals(job.getState())).count(),
                    jobObjectList.stream().filter(job -> "Running".equals(job.getState())).count());
            // 等待指定时间后再次检查
            ThreadUtil.sleep(CHECK_INTERVAL_SECONDS * 1000);
        }
    }
}