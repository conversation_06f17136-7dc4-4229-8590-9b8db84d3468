package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.model.ciModel.job.MediaJobObject;
import com.qcloud.cos.utils.StringUtils;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeMessage;
import com.unipus.digitalbook.model.enums.MediaTranscodeFormatEnum;
import com.unipus.digitalbook.service.COSService;
import jakarta.annotation.Resource;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 媒体转码任务监听器
 *
 * <AUTHOR>
 * @date 2024/8/27 10:00
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable", havingValue = "true", matchIfMissing = true)
public class MediaTranscodeListener {

    @Resource
    private COSService cosService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${kafka.topic.mediaTranscode}")
    private String topicMediaTranscode;

    @Value("${app.env}")
    private String env;

    private static final int MAX_RETRY_COUNT = 2; // 最大全局重试2次
    private static final int MAX_WAIT_MINUTES = 3; // 最大等待3分钟（调整为3分钟）
    private static final int CHECK_INTERVAL_SECONDS = 10; // 每10秒检查一次
    private static final int MAX_TASK_RETRY_COUNT = 2; // 单个转码任务最大重试次数
    private static final int RETRY_WAIT_BASE_MS = 2000; // 重试等待基础时间
    private static final int ASYNC_TIMEOUT_MINUTES = 3; // 异步任务超时时间（3分钟）

    // Redis处理标识相关常量
    private static final String REDIS_PROCESSING_KEY_PREFIX = "media:transcode:processing:";
    private static final int REDIS_PROCESSING_EXPIRE_SECONDS = 600; // 10分钟过期

    @KafkaListener(topics = "${kafka.topic.mediaTranscode}", containerFactory = "manualAckKafkaListenerContainerFactory")
    public void processMessage(String message, Acknowledgment ack) {
        if (!JSON.isValid(message)) {
            log.warn("无效的消息格式: {}", message);
            ack.acknowledge(); // 无效消息直接ACK
            return;
        }

        MediaTranscodeMessage mediaTranscodeMessage = JsonUtil.parseObject(message, MediaTranscodeMessage.class);
        String url = mediaTranscodeMessage.getUrl();
        String hash = mediaTranscodeMessage.getHash();
        Integer retryCount = mediaTranscodeMessage.getRetryCount();

        // 生成Redis处理标识key
        String processingKey = getProcessingKey(url, hash);

        // 检查是否正在处理中
        if (isProcessing(processingKey)) {
            log.info("任务正在处理中，放弃消费并重新发送到队列，url: {}, hash: {}", url, hash);
            // 不ACK，让消息重新回到队列
            return;
        }

        // 获取需要转码的格式列表
        List<MediaTranscodeFormatEnum> formats = cosService.getVideoMediaTranscodeFormats(url);
        if (formats.isEmpty()) {
            log.info("没有需要转码的格式，url: {}", url);
            ack.acknowledge(); // 无需处理的消息直接ACK
            return;
        }

        log.info("开始处理视频转码任务，url: {}, hash: {}, 全局重试次数: {}", url, hash, retryCount);

        // 设置处理中标识
        setProcessing(processingKey);

        try {
            // 异步处理转码任务，最多等待3分钟
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processTranscodeTask(mediaTranscodeMessage, formats);
            });

            // 等待异步任务完成，最多3分钟
            future.get(ASYNC_TIMEOUT_MINUTES, TimeUnit.MINUTES);

            log.info("视频转码任务处理完成，url: {}, 全局重试次数: {}", url, retryCount);
            ack.acknowledge(); // 处理成功，手动ACK

        } catch (Exception e) {
            log.error("处理视频转码任务失败或超时，url: {}, 全局重试次数: {}", url, retryCount, e);

            // 处理失败或超时，根据重试次数决定是否重试
            if (retryCount < MAX_RETRY_COUNT) {
                // 重新发送到队列进行重试
                mediaTranscodeMessage.setRetryCount(retryCount + 1);
                kafkaTemplate.send(topicMediaTranscode, JsonUtil.toJsonString(mediaTranscodeMessage));
                log.info("视频转码任务将进行全局重试，url: {}, 全局重试次数: {}/{}", url, retryCount + 1, MAX_RETRY_COUNT);
            } else {
                log.error("视频转码任务最终失败，url: {}, 已达到最大全局重试次数: {}", url, MAX_RETRY_COUNT);
            }

            ack.acknowledge(); // 无论成功失败都ACK，避免无限重复
        } finally {
            // 清除处理中标识
            clearProcessing(processingKey);
        }
    }

    /**
     * 生成Redis处理标识key
     */
    private String getProcessingKey(String url, String hash) {
        String key = url + ":" + (hash != null ? hash : "");
        return env + ":" + REDIS_PROCESSING_KEY_PREFIX + key.hashCode();
    }

    /**
     * 检查是否正在处理中
     */
    private boolean isProcessing(String processingKey) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.hasKey(processingKey));
        } catch (Exception e) {
            log.warn("检查Redis处理标识失败，默认允许处理: {}", processingKey, e);
            return false; // Redis异常时默认允许处理
        }
    }

    /**
     * 设置处理中标识
     */
    private void setProcessing(String processingKey) {
        try {
            stringRedisTemplate.opsForValue().set(processingKey, "processing", REDIS_PROCESSING_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.debug("设置处理中标识: {}", processingKey);
        } catch (Exception e) {
            log.warn("设置Redis处理标识失败: {}", processingKey, e);
        }
    }

    /**
     * 清除处理中标识
     */
    private void clearProcessing(String processingKey) {
        try {
            stringRedisTemplate.delete(processingKey);
            log.debug("清除处理中标识: {}", processingKey);
        } catch (Exception e) {
            log.warn("清除Redis处理标识失败: {}", processingKey, e);
        }
    }

    /**
     * 处理转码任务的核心逻辑
     */
    private void processTranscodeTask(MediaTranscodeMessage mediaTranscodeMessage, List<MediaTranscodeFormatEnum> formats) {
        String url = mediaTranscodeMessage.getUrl();
        String hash = mediaTranscodeMessage.getHash();

        try {
            // 使用Lists.partition和虚拟线程处理
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
                int groupSize = formats.size() / 5 + (formats.size() % 5 == 0 ? 0 : 1);
                List<CompletableFuture<Void>> futures = Lists.partition(formats, groupSize).stream()
                        .map(group -> CompletableFuture.runAsync(() -> {
                            group.forEach(format -> {
                                String formatName = format.name();
                                processTranscodeFormat(formatName, url, hash);
                            });
                        }, executor))
                        .toList();
                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }

            // 生成并上传HLS主播放列表
            List<MediaTranscodeFormatEnum> hlsFormats = formats.stream()
                    .filter(f -> f.name().startsWith("HLS_"))
                    .toList();
            if (!hlsFormats.isEmpty()) {
                cosService.generateHlsMasterPlaylist(hlsFormats, url, hash);
            }

            // 等待所有任务完成
            waitAllJobComplete(url);

        } catch (Exception e) {
            log.error("转码任务处理异常，url: {}", url, e);
            throw new RuntimeException("转码任务处理失败", e);
        }
    }

    /**
     * 处理单个转码格式，包含任务级别的重试逻辑
     *
     * @param formatName 转码格式名称
     * @param url        视频URL
     * @param hash       视频hash
     */
    private void processTranscodeFormat(String formatName, String url, String hash) {
        int retryCount = 0;
        boolean isFirstAttempt = true;
        while (retryCount <= MAX_TASK_RETRY_COUNT) {
            try {
                if (isFirstAttempt) {
                    log.info("开始处理转码格式，url: {}, format: {}", url, formatName);
                    isFirstAttempt = false;
                } else {
                    log.info("重试处理转码格式，url: {}, format: {}, 第{}次重试", url, formatName, retryCount);
                }
                // 检查是否已存在转码文件
                if (!(StringUtils.isNullOrEmpty(hash) ||
                        StringUtils.isNullOrEmpty(cosService.checkMediaTranscodeJob(formatName, url, hash)))) {
                    log.info("转码文件已存在，跳过创建，url: {}, format: {}", url, formatName);
                    return;
                }
                // 创建转码任务
                String jobId = cosService.createAndMarkMediaTranscodeJob(formatName, url, hash);
                if (jobId != null) {
                    // 等待任务完成
                    waitJobComplete(jobId, formatName, url);
                    return;
                }
            } catch (CosClientException e) {
                log.error("COS异常，url: {}, format: {}, 重试次数: {}", url, formatName, retryCount, e);
                if (retryCount >= MAX_TASK_RETRY_COUNT) {
                    log.error("COS异常已达到最大重试次数，将触发全局重试，url: {}, format: {}, 最大重试次数: {}",
                            url, formatName, MAX_TASK_RETRY_COUNT);
                    throw new RuntimeException("COS异常重试失败，format: " + formatName + ", url: " + url, e);
                }
                // 任务重试前等待一段时间（递增等待）
                retryCount++;
                int waitTime = RETRY_WAIT_BASE_MS * retryCount;
                log.info("任务重试前等待 {}ms，url: {}, format: {}", waitTime, url, formatName);
                ThreadUtil.sleep(waitTime);
            }
        }
    }

    /**
     * 等待单个转码任务完成
     */
    private void waitJobComplete(String jobId, String formatName, String url) {
        log.info("开始等待转码任务完成，url: {}, format: {}, jobId: {}", url, formatName, jobId);
        int maxChecks = (MAX_WAIT_MINUTES * 60) / CHECK_INTERVAL_SECONDS;
        for (int checkCount = 1; checkCount <= maxChecks; checkCount++) {
            COSMediaTranscodeJob job = cosService.getMediaTranscodeJob(jobId);
            String state = job.getState();
            log.info("检查转码任务状态，url: {}, format: {}, jobId: {}, status: {}, 第{}次检查",
                    url, formatName, jobId, state, checkCount);
            if ("Success".equals(state)) {
                log.info("转码任务成功完成，url: {}, jobId: {}, format: {}", url, jobId, formatName);
                return;
            } else if ("Failed".equals(state)) {
                log.error("转码任务失败，url: {}, jobId: {}, format: {}, 错误信息: {}",
                        url, jobId, formatName, job.getMessage());
                throw new COSException("转码任务失败: " + job.getMessage());
            } else if ("Cancel".equals(state)) {
                log.warn("转码任务被取消，url: {}, jobId: {}, format: {}", url, jobId, formatName);
                return;
            }
            // 等待指定时间后再次检查
            ThreadUtil.sleep(CHECK_INTERVAL_SECONDS * 1000);
        }
    }

    /**
     * 等待所有转码任务完成
     */
    private void waitAllJobComplete(String url) {
        int maxChecks = (MAX_WAIT_MINUTES * 60) / CHECK_INTERVAL_SECONDS;
        for (int checkCount = 1; checkCount <= maxChecks; checkCount++) {
            List<MediaJobObject> jobObjectList = cosService.getMediaTranscodeJobs();
            if (jobObjectList == null || jobObjectList.isEmpty()) {
                return;
            }
            log.info("任务状态统计-url:{}, 第{}次检查, 总数: {}, 已提交: {}, 执行中: {}",
                    url,
                    checkCount,
                    jobObjectList.size(),
                    jobObjectList.stream().filter(job -> "Submitted".equals(job.getState())).count(),
                    jobObjectList.stream().filter(job -> "Running".equals(job.getState())).count());
            // 等待指定时间后再次检查
            ThreadUtil.sleep(CHECK_INTERVAL_SECONDS * 1000);
        }
    }
}