package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.book.AddBookOperationLog;
import com.unipus.digitalbook.service.BookOperationLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <p>
 * Kafka消费者
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/17 10:42
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class BookOperationLogListener {

    @Resource
    private BookOperationLogService bookOperationLogService;

    /**
     * 教材操作日志
     *
     * @param message 消息内容
     */
    @KafkaListener(topics = "${kafka.topic.operationLog}")
    public void processMessage(String message) {
        log.info("received subscribe message: {}", message);
        try {
            AddBookOperationLog addBookOperationLog = JSON.parseObject(message, AddBookOperationLog.class);
            bookOperationLogService.saveOperationLogData(addBookOperationLog);
        } catch (Exception e) {
            log.error("message info error, message: {}", message, e);
        }
    }
}
