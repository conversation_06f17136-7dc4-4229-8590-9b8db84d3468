package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.BookPublishPackagePOMapper;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import com.unipus.digitalbook.model.events.UaiResourcePublishEvent;
import com.unipus.digitalbook.model.events.UseBookEvent;
import com.unipus.digitalbook.model.po.BookPublishPackagePO;
import com.unipus.digitalbook.model.po.book.BookPublishItem;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentAction;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 也无法教材发布(使用)后监听消息
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable", havingValue = "true", matchIfMissing = true)
public class BookUseListener {
    @Resource
    private UserContentActionFactory userContentActionFactory;
    @Resource
    private BookPublishPackagePOMapper bookPublishPackagePOMapper;

    @KafkaListener(topics = "${kafka.topic.useBookTopic}")
    public void processMessage(String message) {
        log.info("received useBookTopic message: {}", message);
        UseBookEvent useBookEvent = JsonUtil.parseObject(message, UseBookEvent.class);
        String bookId = useBookEvent.getBookId();
        String bookVersion = useBookEvent.getBookVersion();
        Long tenantId = useBookEvent.getTenantId();
        log.info("start migrate progress: {}, {}, {}", tenantId, bookId, bookVersion);
        // 查询当前版本发布的教材的章节列表
        BookPublishPackagePO bookPublishPackagePO = bookPublishPackagePOMapper.selectByBookIdAndVersionNumber(bookId, bookVersion);
        if (bookPublishPackagePO == null) {
            log.info("no book publish package found for bookId: {}, version: {}", bookId, bookVersion);
            return;
        }
        List<BookPublishItem> publishPackage = bookPublishPackagePO.getPublishPackage();
        if (publishPackage == null || publishPackage.isEmpty()) {
            log.debug("no publish package found for bookId: {}, version: {}", bookId, bookVersion);
            return;
        }
        List<BookPublishItem> publishChapterList = publishPackage.stream().filter(item -> PublishContentTypeEnum.CHAPTER_CONTENT.match(item.getTypeCode())).toList();
        if (publishChapterList.isEmpty()) {
            log.debug("no publish chapter found for bookId: {}, version: {}", bookId, bookVersion);
            return;
        }
        UserContentAction contentAction = userContentActionFactory.getContentAction(ContentTypeEnum.CHAPTER);
        publishChapterList.forEach(item -> {
            log.debug("postPublishProcess {}, {}", item.getResourceId(), item.getVersionId());
            contentAction.postPublishProcess(tenantId, item.getResourceId(), item.getVersionId());
        });
        log.info("migrate progress end");
    }
}
