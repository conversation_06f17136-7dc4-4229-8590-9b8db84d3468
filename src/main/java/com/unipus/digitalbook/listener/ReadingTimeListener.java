package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.book.AddBookOperationLog;
import com.unipus.digitalbook.model.events.ReadingTimeEvent;
import com.unipus.digitalbook.service.BookOperationLogService;
import com.unipus.digitalbook.service.ReadingTimeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 阅读时长Kafka消费者
 * </p>
 *
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class ReadingTimeListener {

    @Resource
    private ReadingTimeService readingTimeService;

    /**
     * 阅读时长计时
     *
     * @param message 消息内容
     */
    @KafkaListener(topics = "${kafka.topic.readingTime}")
    public void processMessage(String message) {
        log.info("received readingTime subscribe message: {}", message);

        try {
            ReadingTimeEvent readingTimeEvent = JSON.parseObject(message, ReadingTimeEvent.class);
            readingTimeService.saveRecordAndCalTotalTime(readingTimeEvent);
        } catch (Exception e) {
            log.error("readingTime message info error, message: {}", message, e);
        }
    }
}
