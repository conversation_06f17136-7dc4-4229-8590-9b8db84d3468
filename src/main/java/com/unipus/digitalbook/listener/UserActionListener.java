package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentAction;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class UserActionListener {
    @Resource
    private UserContentActionFactory userContentActionFactory;

    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    @KafkaListener(topics = "${kafka.topic.userAction}", containerFactory = "batchKafkaListenerContainerFactory")
    public void processMessages(List<String> messages, Acknowledgment ack) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        messages.forEach(this::processSingleMessage);
        ack.acknowledge();
        stopwatch.stop();
        log.info("Processed {} messages in {} ms", messages.size(), stopwatch.elapsed().toMillis());
    }
    private void processSingleMessage(String message) {
        try {
            log.debug("process user action message: {}", message);
            UserActionEvent userActionEvent = JSON.parseObject(message, UserActionEvent.class);
            UserAction userAction = userActionEvent.toUserAction();
            UserContentAction contentAction = userContentActionFactory.getContentAction(userActionEvent.getContentType());
            if (contentAction == null) {
                log.error("received user action message is error : {}", message);
                return;
            }
            contentAction.postProcessNode(userAction, userActionEvent.getContentNode());
        } catch (Exception e) {
            log.error("Failed to process message: {}", message, e);
        }
    }
}
