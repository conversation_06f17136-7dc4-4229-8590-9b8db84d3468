package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.AsyncFailLog;
import com.unipus.digitalbook.service.AsyncFailLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.retry.support.Args;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;
import java.util.Objects;

/**
 * 重试监听器实现
 * 直接实现 RetryListener接口
 */
@Component("customRetryListener")
@Slf4j
public class CustomRetryListener implements RetryListener {

    public static final int MAX_LOG_LENGTH = 1024;

    @Resource
    private AsyncFailLogService asyncFailLogService;

    @Override
    public <F, E extends Throwable> boolean open(RetryContext context, RetryCallback<F, E> callback) {
        String methodName = getMethodName(context);
        log.info("开始重试操作, 方法名称: {}", methodName);
        // 返回true允许重试
        return true;
    }

    @Override
    public <F, E extends Throwable> void onError(RetryContext context, RetryCallback<F, E> callback, Throwable throwable) {
        log.warn("重试操作失败, 方法: {}, 当前重试次数: {}, 异常信息: {}", getMethodName(context), context.getRetryCount(), throwable.getMessage());
        // 输出重试日志到数据库
        outputLog(context, throwable);
    }

    @Override
    public <F, E extends Throwable> void close(RetryContext context, RetryCallback<F, E> callback, Throwable throwable) {
        if (throwable == null) {
             log.info("重试操作成功完成, 方法: {}, 总重试次数: {}", getMethodName(context), context.getRetryCount());
            // 输出重试成功日志到数据库
            // 成功则无须记录日志
            // outputLog(context, null);
        } else {
            log.error("重试操作最终失败, 方法: {}, 总重试次数: {}, 失败原因: {}", getMethodName(context), context.getRetryCount(), throwable.getMessage());
        }
    }

    /**
     * 输出异步处理日志到数据库
     * @param context 上下文
     * @param throwable 异常
     */
    private void outputLog(RetryContext context, Throwable throwable) {
        Pair<String, String> paramInfo = getParamInfo(context);
        AsyncFailLog asyncFailLog = AsyncFailLog.builder()
                .setOp(getMethodName(context))
                .setType(paramInfo==null ? "" : paramInfo.getFirst())
                .setObject(paramInfo==null ? "" : paramInfo.getSecond())
                .setFailReason(getStackTraceAsString(throwable))
                .setStatus(context.getRetryCount())
                .setCreateTime(new Date())
                .setSuccessTime(throwable==null ? new Date():null)
                .setRetryFailTime(new Date())
                .build();
        asyncFailLogService.addAsyncFailLog(asyncFailLog);
    }

    /**
     * 取得异步处理参数类型和参数值
     * @param context 上下文
     * @return 参数类型和参数值
     */
    private Pair<String, String> getParamInfo(RetryContext context) {
        // 获取方法参数信息
        Args args = (Args) context.getAttribute("ARGS");
        if(args==null ||args.getArgs() == null || args.getArgs().length == 0){
            return null;
        }

        // 取得第一个参数
        Object arg = args.getArgs()[0];
        String parameterType = getParamType(arg);
        String parameterContent = JsonUtil.toJsonString(arg);

        // 打印参数信息
        log.debug("参数类型: {}", parameterType);
        log.debug("参数值: {}", parameterContent);
        return Pair.of(parameterType, parameterContent);
    }

    private String getMethodName(RetryContext context) {
        return Objects.toString(context.getAttribute("CALLER_INFO"), "unknown");
    }

    private String getParamType(Object arg){
        if(arg==null){
            return "";
        }
        String typeFullName = arg.getClass().getName();
        return typeFullName.substring(typeFullName.lastIndexOf(".")+1);
    }

    private String getStackTraceAsString(Throwable throwable) {
        if(throwable==null){
            return "";
        }
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return StringUtils.truncate(sw.toString(), MAX_LOG_LENGTH);
    }
}

