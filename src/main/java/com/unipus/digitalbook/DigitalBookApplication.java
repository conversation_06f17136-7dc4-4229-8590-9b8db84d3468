package com.unipus.digitalbook;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan("com.unipus.digitalbook.dao")
@EnableRetry
@EnableScheduling
public class DigitalBookApplication {

    public static void main(String[] args) {
        SpringApplication.run(DigitalBookApplication.class, args);
    }

}