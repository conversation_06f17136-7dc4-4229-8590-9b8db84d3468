package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.ApiListDTO;
import com.unipus.digitalbook.model.dto.ControllerPermissionListDTO;
import com.unipus.digitalbook.model.dto.user.LoginUserDTO;
import com.unipus.digitalbook.model.params.DoAuthParam;
import com.unipus.digitalbook.service.AuthService;
import com.unipus.digitalbook.service.InterfaceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.naming.NoPermissionException;

/**
 * <AUTHOR>
 * @since 2024/10/16 下午3:01
 */
@Tag(name = "认证与权限管理模块",description = "认证与权限相关的配置服务，包含权限、角色相关操作")
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {

    @Resource
    AuthService authService;

    @Resource
    InterfaceService interfaceService;
    @PostMapping(value = "/validate")
    @Operation(
            summary = "登录",
            description = "用户登录,构建用户权限数据，返回jwt",
            method = "POST"
    )
    public Response<LoginUserDTO> validate(@RequestBody DoAuthParam param) {
        return authService.validLogin(param);
    }

    @Operation(summary = "获取全部API列表",
            description = "自动获取所有的controller下的方法",
            method = "GET"
    )
    @GetMapping("/allApiList")
    public Response<ApiListDTO> getAllPermissions() {
        ControllerPermissionListDTO response = interfaceService.getAllPermissions();
        return Response.success(new ApiListDTO(response.getControllerResourceList()));
    }
    @GetMapping("getToken4CoEdit")
    @Operation(
            summary = "获取协同token",
            description = "获取协同token，返回jwt",
            method = "POST"
    )
    public Response<String> getToken4CoEdit(@RequestParam("bookId") String bookId) throws NoPermissionException {
        if (!StringUtils.hasText(bookId)) {
            return Response.fail("bookId不能为空");
        }
        return authService.generateToken4CoEdit(UserUtil.getCurrentUser(), bookId);
    }
}
