package com.unipus.digitalbook.controller;

import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.GetClioSigDTO;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.clio.ClioSig;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.question.GetClioSigParam;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.UserService;
import com.unipus.digitalbook.service.remote.restful.soe.SoeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/soe")
@Tag(name = "SOE相关功能", description = "SOE相关接口")
public class SoeController extends BaseController {

    @Resource
    private UserService userService;
    @Resource
    private SoeService soeService;
    @Resource
    UserAnswerService userAnswerService;

    @PostMapping("/getClioSig")
    @Operation(summary = "获取clio签名配置", description = "获取clio签名配置", method = "POST")
    public Response<GetClioSigDTO> getClioSig(@RequestBody GetClioSigParam param) {
        // 根据用户id获取openId
        Long currentUserId = getCurrentUserId();
        UserInfo userInfo = userService.getUserInfo(currentUserId);
        String openId = userInfo.getSsoId();
        Long tenantId = getCurrentOrgId();

        // 生成clio请求tokenId
        String tokenId = UUID.randomUUID().toString().replace("-", "");
        // 获取clio配置
        ClioSig clioSig = soeService.getClioSig(tokenId, openId, param.getSoeQuestionType());
        String soeLimit = clioSig.getSoeLimit();
        // 缓存用户异步作答回调记录
        JSONObject callbackBody = new JSONObject();
        callbackBody.put("bizQuestionId", param.getBizQuestionId());
        callbackBody.put("questionType", QuestionTypeEnum.getCodeByName(param.getQuestionType()));
        callbackBody.put("tenantId", tenantId);
        callbackBody.put("openId", openId);
        callbackBody.put("soeLimit", soeLimit);
        userAnswerService.setAnswerCallback(tokenId, callbackBody.toJSONString());
        return Response.success(new GetClioSigDTO(clioSig));
    }
}