package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.user.ReaderInitInfoDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Objects;


@RestController
@RequestMapping("reader/auth")
@Tag(name = "读者认证相关接口", description = "证相关接口")
public class ReaderAuthController extends BaseController {
    @GetMapping("/init")
    @Tag(name = "获取认证初始化信息", description = "获取认证初始化信息")
    public Response<ReaderInitInfoDTO> info() {
        String openId = getOpenId();
        ReaderInitInfoDTO info = new ReaderInitInfoDTO();
        info.setOpenId(openId);
        info.setReaderType(Objects.requireNonNull(getReaderType()).getCode());
        return Response.success(info);
    }
}
