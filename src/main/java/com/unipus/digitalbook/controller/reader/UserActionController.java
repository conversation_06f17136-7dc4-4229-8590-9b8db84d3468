package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.params.action.UserActionParam;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("reader/action")
@RestController
@Slf4j
@Tag(name = "读者行为接口相关功能", description = "行为接口相关接口")
public class UserActionController extends BaseController {

    @Resource
    private UserContentActionFactory userContentActionFactory;

    @Resource
    private ChapterService chapterService;

    @PostMapping("finishNode")
    public Response<Boolean> finishNode(@RequestBody UserActionParam param) {
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(param.getChapterId(), param.getChapterVersionNumber());
        if (chapterVersionId == null) {
            log.error("章节版本不存在 {}, {}", param.getChapterId(), param.getChapterVersionNumber());
            return Response.fail("章节版本不存在");
        }
        UserAction userAction = param.toEntity(getTenantId(), getOpenId(), chapterVersionId, getDataPackage(), getClientIp(), getEnvPartition());
        return userContentActionFactory.getContentAction(ContentTypeEnum.CHAPTER).finishNode(userAction);
    }
}
