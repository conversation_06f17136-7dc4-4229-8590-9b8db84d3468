package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.params.user.UserCustomContentActionParam;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("reader/customContent/action")
@RestController
@Slf4j
@Tag(name = "自定义内容读者行为接口相关功能", description = "行为接口相关接口")
public class UserCustomContentActionController extends BaseController {

    @Resource
    private UserContentActionFactory userContentActionFactory;

    @PostMapping("finishNode")
    public Response<Boolean> finishNode(@RequestBody UserCustomContentActionParam param) {
        UserAction userAction = param.toEntity(getTenantId(), getOpenId(), getDataPackage(), getClientIp(), getEnvPartition());
        return userContentActionFactory.getContentAction(ContentTypeEnum.CUSTOM_CONTENT).finishNode(userAction);
    }
}
