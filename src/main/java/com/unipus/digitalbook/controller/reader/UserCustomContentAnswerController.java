package com.unipus.digitalbook.controller.reader;


import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.AnswerDTO;
import com.unipus.digitalbook.model.dto.question.GetClioSigDTO;
import com.unipus.digitalbook.model.dto.question.GetUserAnswerEvaluationDTO;
import com.unipus.digitalbook.model.dto.question.JudgeTaskDTO;
import com.unipus.digitalbook.model.dto.question.JudgeTaskListDTO;
import com.unipus.digitalbook.model.dto.question.QuestionAnalysisDTO;
import com.unipus.digitalbook.model.dto.question.UserAnswerResponseDTO;
import com.unipus.digitalbook.model.dto.question.UserAnswerResultDTO;
import com.unipus.digitalbook.model.entity.clio.ClioSig;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.question.AnswerCustomContentScoreParam;
import com.unipus.digitalbook.model.params.question.AnswerScoreParam;
import com.unipus.digitalbook.model.params.question.GetClioSigParam;
import com.unipus.digitalbook.model.params.question.UserAnswerListParam;
import com.unipus.digitalbook.service.QuestionService;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.remote.restful.soe.SoeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 用户作答接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("reader/customContent/answer")
@Tag(name = "读者作答接口相关功能", description = "作答接口相关接口")
public class UserCustomContentAnswerController extends BaseController {
    @Resource
    private UserAnswerService userAnswerService;
    @Resource
    private QuestionService questionService;

    @PostMapping("/submitScore")
    @Operation(summary = "自定义内容用户提交作答", description = "提交用户答案进行判分并保存用户作答记录，所有题型通用", method = "POST")
    public Response<UserAnswerResponseDTO> submitScore(@RequestBody AnswerCustomContentScoreParam request) {
        String openId = getOpenId();
        Long tenantId = getTenantId();
        String envPartition = getEnvPartition();
        // 构建题组
        BigQuestionGroup bigQuestion = questionService.getBigQuestion(request.getGroupId(), request.getVersionNumber());
        UserAnswerList userAnswerList = request.toEntity(openId, tenantId, envPartition);
        SubmitAnswerContext context = request.toContext(tenantId, openId, envPartition, getClientIp(), getDataPackage());
        UserAnswerList resultAnswerList = userAnswerService.submitScore(bigQuestion, userAnswerList, context);
        if (resultAnswerList == null) {
            return Response.fail("作答失败, 请检查题目信息");
        }
        // 返回用户答题结果
        List<UserAnswerResultDTO> userAnswerResultList = resultAnswerList.getUserAnswers().stream().map(UserAnswerResultDTO::new).toList();
        UserAnswerResponseDTO userAnswerResponseDTO = new UserAnswerResponseDTO();
        // 用户百分制得分
        userAnswerResponseDTO.setScore(ScoreUtil.keepOneDecimalForPercent(bigQuestion.getScore(), resultAnswerList.getScore()));
        // 获取从题型里解析正确答案列表
        userAnswerResponseDTO.setCorrectAnswers(AnswerDTO.toDTOList(bigQuestion.fetchCorrectAnswers()));
        userAnswerResponseDTO.setUserAnswersResult(userAnswerResultList);
        userAnswerResponseDTO.setQuestionAnalysis(QuestionAnalysisDTO.toDTOList(bigQuestion.fetchQuestionAnalysis()));
        return Response.success(userAnswerResponseDTO);
    }
}
