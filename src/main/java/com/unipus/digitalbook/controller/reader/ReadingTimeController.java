package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.params.learn.ReadingTimeHeartbeatParam;
import com.unipus.digitalbook.model.params.learn.ReadingTimeStartParam;
import com.unipus.digitalbook.service.ReadingTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学习时长记录控制器
 */
@RestController
@RequestMapping("reader/readingtime")
@Tag(name = "学习时长记录", description = "用户学习时长记录相关接口")
public class ReadingTimeController extends BaseController {

    @Resource
    private ReadingTimeService readingTimeService;

    /**
     * 开始学习时长记录
     *
     * @param param 开始学习参数
     * @return 操作结果
     */
    @PostMapping("/start")
    @Operation(summary = "开始学习时长记录", description = "用户开始学习某个章节时调用，创建学习时长记录")
    public Response<Boolean> start(@RequestBody ReadingTimeStartParam param) {
        // 获取当前用户ID
        String openId = getOpenId();
        if (StringUtil.isBlank(openId)) {
            return Response.fail("用户未登录");
        }

        boolean result = readingTimeService.startReading(openId, param.getBookId(), param.getChapterId(), getTenantId());

        if (result) {
            return Response.success("开始记录学习时长成功", true);
        } else {
            return Response.fail("开始记录学习时长失败");
        }
    }

    /**
     * 学习时长心跳记录
     *
     * @param param 心跳参数
     * @return 操作结果
     */
    @PostMapping("/heartbeat")
    @Operation(summary = "学习时长心跳记录", description = "定期发送心跳信号，更新学习时长记录")
    public Response<Boolean> heartbeat(@RequestBody ReadingTimeHeartbeatParam param) {
        // 获取当前用户ID
        String openId = getOpenId();
        if (StringUtil.isBlank(openId)) {
            return Response.fail("用户未登录");
        }

        boolean result = readingTimeService.recordHeartbeat(openId, param.getBookId(), param.getChapterId(), getTenantId());

        if (result) {
            return Response.success("心跳记录成功", true);
        } else {
            return Response.fail("心跳记录失败");
        }
    }
}