package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.AssistantInfoDTO;
import com.unipus.digitalbook.model.dto.book.BookBasicInfoDTO;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.BookSettingService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("reader/book")
@Tag(name = "读者学习教材相关接口", description = "读者学习教材相关接口")
public class ReaderBookController extends BaseController {
    @Resource
    private BookService bookService;

    @Resource
    private BookSettingService bookSettingService;

    @Resource
    private AssistantService assistantService;

    @GetMapping("/getBookBasicInfo")
    @Tag(name = "获取教材的基本信息", description = "获取教材的基本信息")
    public Response<BookBasicInfoDTO> getBookBasicInfo(String bookId, String bookVersionNumber) {
        Book book = bookService.getBookBasicInfoByIdAndVersion(bookId, bookVersionNumber);
        return Response.success(new BookBasicInfoDTO(book, Collections.emptyMap()));
    }

    @GetMapping("/getBookAssistant")
    @Tag(name = "获取教材的数字人信息", description = "获取教材的数字人信息")
    public Response<AssistantInfoDTO> getBookAssistantInfo(String bookId) {
        BookSettingPO bookSettingPO = bookSettingService.selectByBookId(bookId);
        AssistantInfoDTO info = new AssistantInfoDTO();
        if (bookSettingPO != null) {
            info.setOpened(bookSettingPO.getAssistantOpened());
        }
        return Response.success(info);
    }
}
