package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.QuestionQuerySourceEnum;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("reader/question")
@Tag(name = "读者题目接口", description = "读者题目接口")
public class ReaderQuestionController extends BaseController {
    @Resource
    private QuestionService questionService;
    @Resource
    private ChapterService chapterService;

    @GetMapping("/get/{questionId}")
    @Operation(summary = "根据题目id以及教材版本获取题目信息", description = "根据题目id以及教材版本获取题目信息", method = "GET")
    public Response<BigQuestionGroupDTO> getQuestion(@PathVariable String questionId, String source, String sourceId, String versionNumber) {
        QuestionQuerySourceEnum querySource = QuestionQuerySourceEnum.getQuerySource(source);
        Long questionGroupId = switch (querySource) {
            case CHAPTER -> chapterService.getQuestionId(sourceId, versionNumber, questionId);
            case PAPER -> questionService.getIdByBizParentIdAndVersion(sourceId, versionNumber, questionId);
        };
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        if (questionGroupId == null) {
            return Response.fail("未查询到题目信息");
        }
        BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionGroupId);
        return Response.success(new BigQuestionGroupDTO(bigQuestion, readerType == ReaderTypeEnum.TEACHER));
    }
}
