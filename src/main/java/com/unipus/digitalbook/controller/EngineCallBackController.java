package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.remote.EnginCorrectCallbackParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/engine")
@Tag(name = "引擎接口回调", description = "引擎接口回调")
@Slf4j
public class EngineCallBackController extends BaseController {

    @Operation(summary = "写作/翻译引擎回调接口")
    @PostMapping("writeCorrectCallback")
    public Response<BooleanDTO> writeCorrectCallback(@RequestBody EnginCorrectCallbackParam param) {
        log.info("writeCorrectCallback : {}", param);
        List<UserAnswer> userAnswers = new ArrayList<>();
        if (StringUtils.hasText(param.getIcorrectId())) {
            UserAnswer userAnswer = new UserAnswer();
            userAnswer.setBizAnswerId(param.getIcorrectId());
            userAnswer.setQuestionType(QuestionTypeEnum.WRITING.getCode());
            userAnswers.add(userAnswer);
        }
        if (StringUtils.hasText(param.getAiTranslateId())) {
            UserAnswer userAnswer = new UserAnswer();
            userAnswer.setBizAnswerId(param.getAiTranslateId());
            userAnswer.setQuestionType(QuestionTypeEnum.TRANSLATION.getCode());
            userAnswers.add(userAnswer);
        }
        new UserAnswerList(userAnswers).endJudgeTask();
        return Response.success(new BooleanDTO(true));
    }
}
