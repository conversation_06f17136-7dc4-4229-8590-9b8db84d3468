package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookThemeListDTO;
import com.unipus.digitalbook.model.dto.book.ThemeListDTO;
import com.unipus.digitalbook.model.entity.book.BookTheme;
import com.unipus.digitalbook.model.entity.book.Theme;
import com.unipus.digitalbook.model.params.IdListParams;
import com.unipus.digitalbook.model.params.book.*;
import com.unipus.digitalbook.service.BookThemeService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/bookThemes")
public class BookThemeController extends BaseController {

    @Resource
    private BookThemeService bookThemeService;

    @Operation(summary = "创建主题", description = "创建主题(返回ID)")
    @PostMapping("/createTheme")
    public Response<Long> createTheme(@RequestBody ThemeCreateParam param) {
        return Response.success(bookThemeService.createTheme(param.toEntity(), getCurrentUserId()));
    }

    @Operation(summary = "编辑主题", description = "编辑主题")
    @PostMapping("/editTheme")
    public Response<Boolean> editTheme(@RequestBody ThemeEditParam param) {
        boolean result = bookThemeService.editTheme(param.toEntity(), getCurrentUserId());
        return Response.success(result);
    }

    @Operation(summary = "查询主题列表", description = "查询主题列表")
    @PostMapping("/getThemeList")
    public Response<ThemeListDTO> getThemeByIds(@RequestBody ThemeSearchParam param) {
        List<Theme> themes = bookThemeService.getThemeList(param.toEntity());
        return Response.success(new ThemeListDTO(themes));
    }

    @Operation(summary = "通过ID获取主题", description = "通过ID获取主题")
    @PostMapping("/getThemeByIds")
    public Response<ThemeListDTO> getThemeByIds(@RequestBody IdListParams param) {
        List<Theme> themes = bookThemeService.getThemeByIds(param.getIdList());
        return Response.success(new ThemeListDTO(themes));
    }

    @Operation(summary = "创建教材主题", description = "创建教材主题(返回ID)")
    @PostMapping("/createBookTheme")
    public Response<Long> createBookTheme(@RequestBody BookThemeCreateParam param) {
        return Response.success(bookThemeService.createBookTheme(param.toEntity(), getCurrentUserId()));
    }

    @Operation(summary = "编辑教材主题", description = "编辑教材主题")
    @PostMapping("/editBookTheme")
    public Response<Boolean> editBookTheme(@RequestBody BookThemeEditParam param) {
        return Response.success(bookThemeService.editBookTheme(param.toEntity(), getCurrentUserId()));
    }

    @Operation(summary = "取得教材主题列表", description = "取得教材主题列表")
    @PostMapping("/getBookThemes")
    public Response<BookThemeListDTO> getBookThemes(@RequestBody BookThemeSearchParam param) {
        List<BookTheme> bookThemes = bookThemeService.getBookThemes(param.toEntity());
        return Response.success(new BookThemeListDTO(bookThemes));
    }

}
