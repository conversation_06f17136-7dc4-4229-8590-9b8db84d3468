package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.AssistantBookSwitchDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantBookSwitchListDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantTemplateReferenceListDTO;
import com.unipus.digitalbook.model.params.assistant.AssistantBookSwitchParam;
import com.unipus.digitalbook.model.params.assistant.AssistantTemplateReferenceParam;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.BookSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/backend/assistant")
@Tag(name = "教材数字人相关功能", description = "教材数字人相关功能")
public class BEAssistantController extends BaseController {

    @Resource
    private AssistantService assistantService;

    @Resource
    private BookSettingService bookSettingService;

    @PostMapping("/template/reference")
    @Tag(name = "教材模板引用关系", description = "教材模板引用关系")
    @Operation(summary = "教材模板引用关系", description = "教材模板引用关系")
    public Response<AssistantTemplateReferenceListDTO> templateReference(
            @Valid @RequestBody AssistantTemplateReferenceParam param) {
        return Response.success(assistantService.templateReference(param));
    }

    @PostMapping("/book/switch")
    @Tag(name = "教材数字人开关", description = "教材数字人开关")
    @Operation(summary = "教材数字人开关", description = "教材数字人开关")
    public Response<AssistantBookSwitchListDTO> bookSwitch(
            @RequestBody AssistantBookSwitchParam param) {
        List<String> bookIds = param.getBookIds();
        Map<String, BookSettingPO> map = bookSettingService.selectByBookIds(bookIds);

        List<AssistantBookSwitchDTO> list = bookIds.stream().map(bookId -> new AssistantBookSwitchDTO(bookId,
                Optional.ofNullable(map.get(bookId)).map(BookSettingPO::getAssistantOpened).orElse(Boolean.FALSE))).toList();
        AssistantBookSwitchListDTO result = new AssistantBookSwitchListDTO();
        result.setList(list);
        return Response.success(result);
    }

}
