package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.user.BackendUserDTO;
import com.unipus.digitalbook.model.params.BackendAuthParam;
import com.unipus.digitalbook.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "内部访问认证与权限管理模块", description = "内部访问认证与权限相关的配置服务")
@RestController
@RequestMapping("/backend/auth")
@Slf4j
public class BEAuthController {

    @Resource
    private AuthService authService;

    @Operation(
            summary = "获取内部访问token",
            description = "内部用户获取访问认证,缓存用户信息,返回jwt",
            method = "POST"
    )
    @PostMapping("/getAccessToken")
    public Response<BackendUserDTO> getAccessToken(@RequestBody BackendAuthParam param) {
        return authService.getBackendAccessToken(param.toEntity());
    }
}
