package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeGraphService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeNode;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeBaseAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeDeleteRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeMoveRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.GraphNodeInfoResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeAddResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeDetailResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@Deprecated
@RestController
@RequestMapping("/knowledge/graph")
@Tag(name = "知识图谱子图相关功能", description = "知识图谱子图相关功能")
public class KnowledgeGraphController extends BaseController {
    @Resource
    KnowledgeGraphService knowledgeGraphService;

    @GetMapping("/graphNodeList/{graphId}")
    @Operation(summary = "查询子图知识点列表",
            description = "查询子图知识点列表",
            method = "Get"
    )
    public Response<GraphNodeInfoResponse> knowledgeGraphNodeList(@PathVariable String graphId) {
        return knowledgeGraphService.knowledgeGraphNodeList(graphId);
    }


    /**
     * 新增根节点
     *
     * @param params 知识图谱节点参数
     * @return 知识图谱节点ID
     */
    @PostMapping("/base/add")
    @Operation(summary = "新增根节点", description = "新增根节点")
    public Response<KnowledgeNodeAddResponse> knowledgeNodeAdd(@RequestBody KnowledgeNodeBaseAddRequest params) {
        return knowledgeGraphService.knowledgeBaseNodeAdd(params);
    }

    /**
     * 更新根节点
     *
     * @param params 知识图谱节点参数
     * @return 更新结果
     */
    @PostMapping("/base/update")
    @Operation(summary = "更新根节点", description = "更新根节点")
    public Response knowledgeNodeSimpleUpdate(@RequestBody KnowledgeNodeUpdateRequest params) {
        return knowledgeGraphService.knowledgeBaseNodeUpdate(params);
    }

    /**
     * 新增同级知识点
     *
     * @param params 知识图谱节点参数
     * @return 知识图谱节点ID
     */
    @PostMapping("/sameLevel/add")
    @Operation(summary = "新增同级知识点", description = "新增同级知识点")
    public Response<String> knowledgeNodeSameLevelAdd(@RequestBody KnowledgeNode params) {
        return knowledgeGraphService.knowledgeNodeSameLevelAdd(params);
    }

    /**
     * 新增子级知识点
     *
     * @param params 知识图谱节点参数
     * @return 知识图谱节点ID
     */
    @PostMapping("/subLevel/add")
    @Operation(summary = "新增子级知识点", description = "新增子级知识点")
    public Response<String> knowledgeSubNodeAdd(@RequestBody KnowledgeNode params) {
        return knowledgeGraphService.knowledgeSubNodeAdd(params);
    }

    /**
     * 更新知识点
     *
     * @param params 知识图谱节点参数
     * @return 更新结果
     */
    @PostMapping("/node/update")
    @Operation(summary = "更新知识点", description = "更新知识点")
    public Response knowledgeNodeUpdate(@RequestBody KnowledgeNode params) {
        return knowledgeGraphService.knowledgeNodeUpdate(params);
    }

    /**
     * 移动知识点
     *
     * @param params 知识图谱节点参数
     * @return 移动结果
     */
    @PostMapping("/move")
    @Operation(summary = "移动知识点", description = "移动知识点")
    public Response<String> knowledgeNodeMove(@RequestBody KnowledgeNodeMoveRequest params) {
        return knowledgeGraphService.knowledgeNodeMove(params);
    }

    /**
     * 删除知识点
     *
     * @param params 知识图谱节点ID
     * @return 删除结果
     */
    @PostMapping("/node/delete")
    @Operation(summary = "删除知识点", description = "删除知识点")
    public Response knowledgeNodeDelete(@RequestBody KnowledgeNodeDeleteRequest params) {
        return knowledgeGraphService.knowledgeNodeDelete(params);
    }

    /**
     * 查看知识点
     *
     * @param nodeId 知识图谱节点ID
     * @return 知识图谱节点信息
     */
    @GetMapping("/detail/{nodeId}")
    @Operation(summary = "查看知识点", description = "查看知识点")
    public Response<KnowledgeNodeDetailResponse> knowledgeNodeDetail(@PathVariable String nodeId) {
        return knowledgeGraphService.knowledgeNodeDetail(nodeId);
    }

}
