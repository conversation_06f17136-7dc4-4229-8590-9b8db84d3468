package com.unipus.digitalbook.controller;


import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.service.BookUserActivityService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/activity")
@Tag(name = "用户教材活动操作", description = "记录用户教材活动操作")
public class BookUserActivityController extends BaseController {
    @Resource
    private BookUserActivityService bookUserActivityService;

    @PostMapping("/recordBookEditActivity")
    public Response<Boolean> recordBookEditActivity(@RequestBody IdParam<String> param) {
        return Response.success(true);
    }
}
