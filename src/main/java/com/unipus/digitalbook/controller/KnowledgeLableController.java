package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeLabelService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.PaginationResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.ResourceTag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@Deprecated
@RestController
@RequestMapping("/knowledge/label")
@Tag(name = "知识图谱子图关系相关功能", description = "知识图谱子图关系相关功能")
public class KnowledgeLableController extends BaseController {
    @Resource
    KnowledgeLabelService knowledgeLabelService;

    /**
     * 列表展示标签类型
     *
     * @param keyword  搜索关键词
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 标签类型列表响应
     */
    @GetMapping("/label/list")
    @Operation(summary = "列表展示标签类型", description = "列表展示标签类型")
    public Response<PaginationResponse> labelList(@RequestParam(required = false, defaultValue = "") String keyword,
                                                  @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                  @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        return knowledgeLabelService.labelList(keyword, pageNum, pageSize);
    }

    /**
     * 新增标签类型
     * <p>
     * {
     * "type": "拓展标签type",
     * "notes": "拓展标签notes",
     * "labelSet": [{
     * <p>
     * "content": "拓展标签content",
     * "type": "拓展标签type"
     * <p>
     * }]
     * }
     *
     * @param params 新增标签类型请求参数
     * @return 基础响应
     */
    @PostMapping("/label/add")
    @Operation(summary = "新增标签类型", description = "新增标签类型")
    public Response<ResourceTag> labelAdd(@RequestBody ResourceTag params) {
        return knowledgeLabelService.labelAdd(params);
    }

    /**
     * 更新标签类型
     *
     * @param params 更新标签类型请求参数
     * @return 基础响应
     */
    @PostMapping("/label/update")
    @Operation(summary = "更新标签类型", description = "更新标签类型")
    public Response<ResourceTag> labelUpdate(@RequestBody ResourceTag params) {
        return knowledgeLabelService.labelUpdate(params);
    }

    /**
     * 删除标签类型
     *
     * @param labelId 标签类型ID
     * @return 基础响应
     */
    @GetMapping("/label/delete")
    @Operation(summary = "删除标签类型", description = "删除标签类型")
    public Response labelDelete(@RequestParam String labelId) {
        return knowledgeLabelService.labelDelete(labelId);
    }

}
