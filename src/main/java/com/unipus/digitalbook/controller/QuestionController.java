package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.QuestionQuerySourceEnum;
import com.unipus.digitalbook.model.params.question.BigQuestionGroupParam;
import com.unipus.digitalbook.model.params.question.QuestionParseParam;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.QuestionPushService;
import com.unipus.digitalbook.service.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/question")
@Tag(name = "题型相关功能", description = "题型相关接口")
public class QuestionController extends BaseController {

    @Resource
    private QuestionService questionService;

    @Resource
    private ChapterService chapterService;

    @Resource
    private QuestionPushService questionPushService;

    @PostMapping("/questionParseContent")
    @Operation(summary = "根据题型解析图片与文本内容", description = "根据题型解析图片与文本内容", method = "POST")
    public Response<String> questionParseContent(@RequestBody QuestionParseParam param) {
        String content = questionService.questionParseContent(param);
        return Response.success(content);
    }

    @PostMapping("/saveBigQuestion")
    @Operation(summary = "保存大题", description = "保存大题", method = "POST")
    public Response<Long> saveBigQuestion(@RequestBody BigQuestionGroupParam request) {
        Long currentUserId = getCurrentUserId();
        Long groupId = questionService.saveBigQuestion(request.toEntity(currentUserId, null));
        return Response.success(groupId);
    }

    @GetMapping("/get/{questionId}")
    @Operation(summary = "根据大题id以及来源id和来源版本获取题目信息", description = "根据大题id以及来源id和来源版本获取题目信息", method = "GET")
    public Response<BigQuestionGroupDTO> getBigQuestion(@PathVariable String questionId, String sourceId, String versionNumber, String source) {
        QuestionQuerySourceEnum querySource = QuestionQuerySourceEnum.getQuerySource(source);
        Long questionGroupId = switch (querySource) {
            case CHAPTER -> chapterService.getQuestionId(sourceId, versionNumber, questionId);
            case PAPER -> questionService.getIdByBizParentIdAndVersion(sourceId, versionNumber, questionId);
        };
        if (questionGroupId == null) {
            return Response.fail("未查询到题目信息");
        }
        BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionGroupId);
        return Response.success(new BigQuestionGroupDTO(bigQuestion, true));
    }

    @GetMapping("/getByIdAndVersionNumber")
    @Operation(summary = "根据大题id和版本获取题目信息", description = "根据大题id和版本获取题目信息", method = "GET")
    public Response<BigQuestionGroupDTO> getBigQuestion(String questionId, String versionNumber) {
        BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionId, versionNumber);
        if (bigQuestion == null) {
            return Response.fail("未查询到题目信息");
        }
        return Response.success(new BigQuestionGroupDTO(bigQuestion, true));
    }

}
