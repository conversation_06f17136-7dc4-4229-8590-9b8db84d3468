package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.organization.OrganizationListDTO;
import com.unipus.digitalbook.model.dto.organization.OrganizationSearchListDTO;
import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.enums.StatusEnum;
import com.unipus.digitalbook.model.params.ChangeStatusParam;
import com.unipus.digitalbook.model.params.organization.AddSubOrgParam;
import com.unipus.digitalbook.model.params.organization.AddTopOrgParam;
import com.unipus.digitalbook.model.params.organization.EditOrgParam;
import com.unipus.digitalbook.model.params.organization.OrgSearch4AdminParam;
import com.unipus.digitalbook.model.params.user.DeleteUserParam;
import com.unipus.digitalbook.service.OrgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/org")
@Tag(name = "组织相关功能", description = "组织相关接口")
public class OrgController extends BaseController{

    @Resource
    OrgService orgService;

    @PostMapping("getOrgTree")
    @Operation(summary = "获取组织树", description = "超管的系统管理中，" +
            "parentOrgId:指定父组织ID作为根节点，如果为null则返回整个组织树。" +
            "subLevel:子节点层级数量(0:全部，1:一级子节点，2:二级子节点，以此类推,如果为null则默认为全部)。" +
            "status:状态，1:启用，0:禁用, 空值默认全部", method = "POST")
    Response<OrganizationListDTO> getOrgTree(
            @RequestParam(value = "parentOrgId", required = false, defaultValue = "0") Long parentOrgId,
            @RequestParam(value = "subLevel", required = false, defaultValue = "0" ) Integer subLevel,
            @RequestParam(value = "status", required = false, defaultValue = "") Integer status ) {
        return Response.success(new OrganizationListDTO(orgService.getOrganizationTree(parentOrgId, subLevel, status)));
    }

    @PostMapping("getOrgList")
    @Operation(summary = "查询组织", description = "超管的系统管理中，同时可以根据名称和状态，进行查询。", method = "POST")
    Response<OrganizationSearchListDTO> getOrgList(@RequestBody OrgSearch4AdminParam param) {
        Integer total = orgService.countOrganizations(param.getName(), param.getStatus());
        if (total == 0) {
            return Response.success(new OrganizationSearchListDTO(Collections.emptyList(), 0));
        }
        PageParams pageParams = param.getPageParams();
        List<Organization> organizations = orgService.searchOrganizations(
                param.getName(), param.getStatus(), pageParams.getOffset(), pageParams.getLimit());
        return Response.success(new OrganizationSearchListDTO(organizations, total));
    }

    @PostMapping("addTopOrg")
    @Operation(summary = "新建一级机构", description = "超管的系统管理中，新建跟节点的机构", method = "POST")
    Response<Boolean> addTopOrganization(@RequestBody AddTopOrgParam addTopOrgParam) {
        // 检查传入的参数是否为空，如果为空则返回一个表示缺少参数的响应
        if (addTopOrgParam == null) {
            return Response.noParam();
        }
        // 通过orgService服务调用addTopOrganization方法尝试添加新的顶级组织，并接收返回的结果
        Boolean res = orgService.addTopOrganization(addTopOrgParam.toEntity(),getCurrentUserId());
        if (Boolean.TRUE.equals(res)) {
            return Response.success("新增成功", true);
        }
        return Response.fail();
    }

    @PostMapping("addSubOrg")
    @Operation(summary = "新建下级机构", description = "超管的系统管理中，新建非一级机构", method = "POST")
    Response<Boolean> addSubOrg(@RequestBody AddSubOrgParam addSubOrgParam) {
        // 检查传入的参数是否为空，如果为空则返回一个表示缺少参数的响应
        if (addSubOrgParam == null) {
            return Response.noParam();
        }
        // 通过orgService服务调用addTopOrganization方法尝试添加新的顶级组织，并接收返回的结果
        Boolean res = orgService.addSubOrganization(addSubOrgParam.toEntity(),getCurrentUserId());
        if (Boolean.TRUE.equals(res)) {
            return Response.success("新增成功", true);
        }
        return Response.fail();
    }

    @PostMapping("editOrg")
    @Operation(summary = "编辑机构", description = "超管的系统管理中，编辑机构，涉及到移动组织操作", method = "PUT" )
    Response<Boolean> editOrg(@RequestBody EditOrgParam editOrgParam) {
        Boolean result = orgService.editOrganization(editOrgParam.toEntity(), getCurrentUserId());
        return Boolean.TRUE.equals(result) ? Response.success("编辑成功", true) : Response.fail();
    }

    @PutMapping("batchChangeStatus")
    @Operation(summary = "变更机构状态", description = "超管的系统管理中，批量变更机构状态", method = "PUT")
    public Response<Boolean> batchChangeStatus(@RequestBody ChangeStatusParam changeStatusParam) {
        // 检查传入的参数是否为空，如果为空则返回一个表示缺少参数的响应
        if (changeStatusParam == null) {
            return Response.noParam();
        }
        // 通过orgService服务调用addTopOrganization方法尝试添加新的顶级组织，并接收返回的结果
        Boolean res = orgService.batchChangeOrganizationStatus(changeStatusParam.getIdList(),changeStatusParam.getStatus(),getCurrentUserId());
        if (Boolean.TRUE.equals(res)) {
            return Response.success(changeStatusParam.getStatus().equals(StatusEnum.ENABLE.getCode()) ? "启用成功": "禁用成功", true);
        }
        return Response.success();
    }

    // 获取用户所属组织列表
    @GetMapping("getUserOrgs")
    @Operation(summary = "获取用户所属组织列表", description = "获取用户所属组织列表", method = "GET")
    public Response<OrganizationListDTO> getUserOrgs() {
        OrganizationListDTO organizationListDTO = OrganizationListDTO.fromEntity(orgService.getUserOrgs(getCurrentUserId()));
        return Response.success(organizationListDTO);
    }

    @PostMapping("/batchDeleteRelationByUserIdList")
    @Operation(summary = "批量删除用户与机构关系",
            description = "通过用户id列表，组织结构id，批量删除机构下多个用户的关系",
            method = "POST"
    )
    public Response<Boolean> batchDeleteByIdList(@RequestBody DeleteUserParam params){
        Boolean res= orgService.batchDeleteRelationsByUserIdListAndOrgId(params.getUserIdList(), params.getOrgId(), getCurrentUserId());
        return Response.success("删除成功", res);
    }
}
