package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeRelationService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeRelationAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeRelationUpdateRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@Deprecated
@RestController
@RequestMapping("/knowledge/relation")
@Tag(name = "知识图谱子图关系相关功能", description = "知识图谱子图关系相关功能")
public class KnowledgeRelationController extends BaseController {
    @Resource
    KnowledgeRelationService knowledgeRelationService;

    /**
     * 新增关系
     *
     * @param params 新增关系请求参数
     * @return 基础响应
     */
    @PostMapping("/add")
    @Operation(summary = "新增关系", description = "新增关系")
    public Response relationAdd(@RequestBody KnowledgeRelationAddRequest params) {
        return knowledgeRelationService.relationAdd(params);
    }

    /**
     * 更新关系
     *
     * @param params 更新关系请求参数
     * @return 基础响应
     */
    @PostMapping("/update")
    @Operation(summary = "更新关系", description = "更新关系")
    public Response relationUpdate(@RequestBody KnowledgeRelationUpdateRequest params) {
        return knowledgeRelationService.relationUpdate(params);
    }

    /**
     * 删除关系
     *
     * @param relationId 关系ID
     * @return 基础响应
     */
    @PostMapping("/delete")
    @Operation(summary = "删除关系", description = "删除关系")
    public Response relationDelete(@RequestParam String relationId) {
        return knowledgeRelationService.relationDelete(relationId);
    }
}
