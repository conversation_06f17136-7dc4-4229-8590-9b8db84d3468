package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookSettingInfoDTO;
import com.unipus.digitalbook.model.dto.book.BookSettingLogDTO;
import com.unipus.digitalbook.model.dto.book.BookSettingLogListDTO;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookSettingLogList;
import com.unipus.digitalbook.model.params.book.BookSettingLogParam;
import com.unipus.digitalbook.model.params.book.BookSettingParam;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.BookSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/book/setting")
@Tag(name = "教材相关设置", description = "教材相关设置")
public class BookSettingController extends BaseController {

    @Resource
    private BookSettingService bookSettingService;

    @Resource
    private BookService bookService;

    @PostMapping("/set")
    @Operation(summary = "教材相关设置", description = "教材相关设置", method = "POST")
    public Response<Long> set(@Valid @RequestBody BookSettingParam param) {
        return Response.success(bookSettingService.upsertSetting(param, getCurrentUserId()));
    }

    @GetMapping("/info")
    @Operation(summary = "获取教材设置信息", description = "获取教材设置信息", method = "GET")
    public Response<BookSettingInfoDTO> info(@RequestParam("bookId") String bookId) {
        BookSettingPO bookSettingPO = bookSettingService.selectByBookId(bookId);
        boolean assistantOpened = (bookSettingPO == null) || bookSettingPO.getAssistantOpened();
        BookSettingInfoDTO info = new BookSettingInfoDTO(assistantOpened);
        return Response.success(info);
    }

    @Deprecated(forRemoval = true)
    @PostMapping("/log")
    @Operation(summary = "教材设置日志", description = "教材设置日志", method = "POST")
    public Response<BookSettingLogListDTO> log(@Valid @RequestBody BookSettingLogParam param) {
        Book book = bookService.getBookById(param.getBookId());
        BookSettingLogList bookSettingLogList = bookSettingService.searchLog(param);
        List<BookSettingLogDTO> list = bookSettingLogList.getLogList().stream().map(BookSettingLogDTO::fromEntity).toList();
        BookSettingLogListDTO dto = new BookSettingLogListDTO(list, bookSettingLogList.getTotal(), book.getChineseName());
        return Response.success(dto);
    }

    @PostMapping("/searchLog")
    @Operation(summary = "教材设置日志", description = "教材设置日志", method = "POST")
    public Response<BookSettingLogListDTO> searchLog(@Valid @RequestBody BookSettingLogParam param) {
        Book book = bookService.getBookById(param.getBookId());
        BookSettingLogList bookSettingLogList = bookSettingService.searchLog(param);
        List<BookSettingLogDTO> list = bookSettingLogList.getLogList().stream().map(BookSettingLogDTO::fromEntity).toList();
        BookSettingLogListDTO dto = new BookSettingLogListDTO(list, bookSettingLogList.getTotal(), book.getChineseName());
        return Response.success(dto);
    }

}
