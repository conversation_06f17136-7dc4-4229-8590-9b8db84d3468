package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookTemporarySnapshotDTO;
import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.model.params.book.BookTemporarySnapshotParam;
import com.unipus.digitalbook.service.BookTemporarySnapshotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/bookTemporarySnapshot")
@Tag(name = "教材临时快照相关功能", description = "教材临时快照相关接口")
public class BookTemporarySnapshotController extends BaseController {

    @Resource
    private BookTemporarySnapshotService bookTemporarySnapshotService;

    @PostMapping("/save")
    @Operation(summary = "保存教材临时快照", description = "保存教材临时快照")
    public Response<Boolean> saveSnapshot(@RequestBody BookTemporarySnapshotParam param) {
        Boolean result = bookTemporarySnapshotService.saveSnapshot(param.toEntity(), getCurrentUserId());
        return Response.success("保存成功", result);
    }

    @PostMapping("/get")
    @Operation(summary = "获取教材临时快照", description = "通过bookId和chapterId获取教材临时快照")
    public Response<BookTemporarySnapshotDTO> get(@RequestBody IdParam<Long> param) {
        BookTemporarySnapshot bookTemporarySnapshot = bookTemporarySnapshotService.getSnapshotById(param.getId());
        return Response.success(new BookTemporarySnapshotDTO(bookTemporarySnapshot));
    }

    @PostMapping("/getLatest")
    @Operation(summary = "获取教材临时快照", description = "通过bookId和chapterId获取教材临时快照")
    public Response<BookTemporarySnapshotDTO> getLatest(@RequestBody BookTemporarySnapshotParam param) {
        BookTemporarySnapshot bookTemporarySnapshot = bookTemporarySnapshotService.getLatestSnapshotByBookIdAndChapterId(param.toEntity());
        return Response.success(new BookTemporarySnapshotDTO(bookTemporarySnapshot));
    }
}
