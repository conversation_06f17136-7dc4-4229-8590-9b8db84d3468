package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.params.thirdpartyuser.SaveThirdPartyUserParam;
import com.unipus.digitalbook.service.ThirdPartyUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/thirdPartyUser")
@Tag(name = "第三方用户", description = "第三方用户相关接口")
public class ThirdPartyUserController extends BaseController  {

    @Resource
    private ThirdPartyUserService thirdPartyUserService;

    @PostMapping("/save")
    @Operation(summary = "保存第三方用户", description = "保存第三方用户")
    public Response<IdDTO<String>> saveThirdPartyUser(@RequestBody SaveThirdPartyUserParam saveThirdPartyUserParam) {
        // 保存试卷基本信息
        String id = thirdPartyUserService.saveThirdPartyUser(saveThirdPartyUserParam.toEntity(), getCurrentUserId());
        return Response.success(new IdDTO<>(id));
    }


}
