package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeDTO;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeListDTO;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeSearchListDTO;
import com.unipus.digitalbook.model.entity.wordpractice.WordPracticeSearchList;
import com.unipus.digitalbook.model.params.wordpractice.SaveWordPracticeParam;
import com.unipus.digitalbook.model.params.wordpractice.SearchWordPracticeParam;
import com.unipus.digitalbook.service.WordPracticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/wordPractice")
@Tag(name = "词汇学练接口", description = "词汇学练相关功能")
public class WordPracticeController extends BaseController {

    @Resource
    private WordPracticeService wordPracticeService;

    @PostMapping("/getPageList")
    @Operation(summary = "查询词汇学练列表",
            description = "查询词汇学练列表",
            method = "POST"
    )
    public Response<WordPracticeSearchListDTO> getPageList(@RequestBody SearchWordPracticeParam param) {
        WordPracticeSearchList wordPracticeSearchList = wordPracticeService.searchList(param.getChapterId(), 0, 0);
        return Response.success(new WordPracticeSearchListDTO(wordPracticeSearchList));
    }

    @GetMapping("/getEditStatusDetail")
    @Operation(summary = "词汇学练编辑态详情", description = "词汇学练编辑态详情")
    public Response<WordPracticeDTO> getEditStatusDetail(@RequestParam("id") Long id) {
        return Response.success(wordPracticeService.getEditStatusWordPracticeById(id).toDTO());
    }

    @GetMapping("/getPublishStatusDetail")
    @Operation(summary = "词汇学练发布态详情", description = "词汇学练发布态详情")
    public Response<WordPracticeDTO> getPublishStatusDetail(@RequestParam("id") String bizId) {
        return Response.success(wordPracticeService.getPublishStatusWordPracticeById(bizId).toDTO());
    }

    @PostMapping("/add")
    @Operation(summary = "新增词汇学练",
            description = "新增词汇学练，返回词汇学练Id",
            method = "POST"
    )
    public Response<String> addWordPractice(@RequestBody SaveWordPracticeParam param) {
        return Response.success(wordPracticeService.addWordPractice(param.toEntity()));
    }

    @PostMapping("/edit")
    @Operation(summary = "更新词汇学练",
            description = "更新词汇学练",
            method = "POST"
    )
    public Response<Boolean> editWordPractice(@RequestBody SaveWordPracticeParam param) {
        return Response.success(wordPracticeService.editWordPractice(param.toEntity()));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除词汇学练",
            description = "删除词汇学练",
            method = "POST"
    )
    public Response<Boolean> deleteWordPractice(@RequestBody SaveWordPracticeParam param) {
        return Response.success(wordPracticeService.deleteWordPractice(param.getId()));
    }

    @PostMapping("/batchPublish")
    @Operation(summary = "发布词汇学练",
            description = "发布词汇学练",
            method = "POST"
    )
    public Response<Boolean> publishBatch(@RequestBody SaveWordPracticeParam param) {
        return Response.success(wordPracticeService.publishBatch(param.getBookId(), param.getChapterIds(), param.getLogId()));
    }

    @GetMapping("/getBookAllWordPractice")
    @Operation(summary = "根据教材ID获取教材所有词汇学练", description = "根据教材ID获取教材所有词汇学练")
    public Response<WordPracticeListDTO> getBookAllWordPractice(@RequestParam("bookId") String bookId) {
        return Response.success(new WordPracticeListDTO(wordPracticeService.getBookAllWordPractice(bookId)));
    }

    @GetMapping("/getApiToken")
    @Operation(summary = "获取词汇学练api请求token", description = "获取词汇学练api请求token")
    public Response<String> generateWordPracticeApiServiceToken() {
        return Response.success(wordPracticeService.generateWordPracticeApiServiceToken());
    }
}
