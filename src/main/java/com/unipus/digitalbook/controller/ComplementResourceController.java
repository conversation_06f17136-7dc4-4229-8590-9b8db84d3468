package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.complement.ComplementResourceListDTO;
import com.unipus.digitalbook.model.dto.complement.ComplementResourceReferenceListDTO;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceList;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.model.params.VisibleStatusParam;
import com.unipus.digitalbook.model.params.complement.*;
import com.unipus.digitalbook.service.ComplementResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 配套资源相关功能
 */
@RestController
@RequestMapping("/complementResource")
@Tag(name = "配套资源相关功能", description = "配套资源相关功能")
public class ComplementResourceController extends BaseController{

    @Resource
    private ComplementResourceService complementResourceService;

    @PostMapping("/add")
    @Operation(summary = "新增配套资源", description = "新增配套资源")
    public Response<IdDTO<String>> add(@RequestBody AddResourceParam addResourceParam) {
        String id = complementResourceService.addResource(addResourceParam.toEntity(getCurrentUserId()));
        return Response.success("上传成功", new IdDTO<>(id));
    }

    @PostMapping("/updateName")
    @Operation(summary = "更新配套资源的名字", description = "更新配套资源的名字")
    public Response<Boolean> updateName(@RequestBody UpdateResourceNameParam updateNameParam) {
        Boolean updated = complementResourceService.updateName(updateNameParam.getId(), updateNameParam.getName(), getCurrentUserId());
        return Response.success("名称更新成功", updated);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除配套资源", description = "删除配套资源")
    public Response<Boolean> delete(@RequestBody IdParam<String> param) {
        return Response.success("删除成功",
                complementResourceService.delete(param.getId(), getCurrentUserId()));
    }

    @PostMapping("/updateVisibleStatus")
    @Operation(summary = "更新配套资源的可见状态", description = "更新配套资源的可见状态，0全部可见 1仅教师可见")
    public Response<Boolean> updateVisibleStatus(@RequestBody VisibleStatusParam<String> param) {
        return Response.success("设置成功",
                complementResourceService.updateVisibleStatus(param.getId(), param.getVisibleStatus(), getCurrentUserId()));
    }

    @PostMapping("/search")
    @Operation(summary = "配套资源列表", description = "查询配套资源列表")
    public Response<ComplementResourceListDTO> search(@RequestBody ComplementSearchParam param) {
        ComplementResourceList search = complementResourceService.search(param.getBookId(), param.getResourceType(), param.getVisibleStatus(), param.getPageParams());
        return Response.success(new ComplementResourceListDTO(search.getComplementResourceList(), search.getTotalCount()));
    }

    @PostMapping("/addReference")
    @Operation(summary = "添加配套资源引用", description = "添加配套资源引用(添加成功返回引用ID)")
    public Response<IdDTO<Long>> addReference(@RequestBody AddComplementResourceReferenceParam param) {
        return Response.success(new IdDTO<>(complementResourceService.addReference(param.toEntity(), getCurrentUserId())));
    }

    @PostMapping("/updateReference")
    @Operation(summary = "更新配套资源引用", description = "更新配套资源引用")
    public Response<Boolean> updateReference(@RequestBody UpdateComplementResourceParam param) {
        return Response.success(complementResourceService.updateReference(param.toEntity(), getCurrentUserId()));
    }

    @PostMapping("/deleteReference")
    @Operation(summary = "删除配套资源引用", description = "删除配套资源引用")
    public Response<Boolean> deleteReference(@RequestParam("referenceId") Long referenceId) {
        return Response.success(complementResourceService.removeReference(referenceId, getCurrentUserId()));
    }

    @PostMapping("/getReference")
    @Operation(summary = "查询配套资源引用", description = "查询配套资源引用")
    public Response<ComplementResourceReferenceListDTO> getMediaReference(@RequestBody SearchComplementResourceReferenceParam param) {
        return Response.success(new ComplementResourceReferenceListDTO(complementResourceService.getReference(param.toEntity())));
    }
}
