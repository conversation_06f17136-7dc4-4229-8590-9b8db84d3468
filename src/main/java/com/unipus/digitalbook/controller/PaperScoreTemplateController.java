package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.template.PaperScoreTemplateDTO;
import com.unipus.digitalbook.model.dto.template.PaperScoreTemplateListDTO;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateDEPTypeEnum;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.model.params.template.*;
import com.unipus.digitalbook.service.PaperScoreTemplateRelationService;
import com.unipus.digitalbook.service.PaperScoreTemplateService;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/paperScoreTemplate/")
@Tag(name = "模板相关功能", description = "模板相关接口")
public class PaperScoreTemplateController extends BaseController {

    @Resource
    private PaperScoreTemplateService paperScoreTemplateService;

    @Resource
    private UserService userService;

    @Resource
    private PaperScoreTemplateRelationService paperScoreTemplateRelationService;

    @PostMapping("addTemplate")
    @Operation(summary = "添加模板", description = "添加模板，返回模板ID")
    public Response<IdDTO<Long>> addTemplate(@RequestBody AddPaperScoreTemplateParam param) {
        return Response.success(new IdDTO<>(paperScoreTemplateService.addTemplate(param.toEntity(), getCurrentUserId())));
    }

    @PostMapping("editTemplate")
    @Operation(summary = "编辑模板", description = "编辑模板，返回是否成功")
    public Response<Boolean> editTemplate(@RequestBody EditPaperScoreTemplateParam param) {
        return Response.success("编辑成功", paperScoreTemplateService.editTemplate(param.toEntity(), getCurrentUserId()));
    }

    @Operation(summary = "发布模板", description = "发布模板, 返回是否成功")
    @PostMapping("publishTemplate")
    public Response<BooleanDTO> publishTemplate(@RequestBody IdParam<Long> idParam) {
        boolean status = paperScoreTemplateService.publishTemplate(getCurrentUserId(),
                idParam.getId());
        return Response.success("发布成功", new BooleanDTO(status));
    }

    @Operation(summary = "预览模板", description = "预览模板，返回模板信息及详情")
    @PostMapping("getTemplateDetail")
    public Response<PaperScoreTemplateDTO> getTemplateDetail(@RequestBody IdParam<Long> idParam) {
        PaperScoreTemplate paperScoreTemplate = paperScoreTemplateService.getTemplateDetail(idParam.getId(), false);
        return Response.success(PaperScoreTemplateDTO.assemblyPaperScoreTemplateDTO(paperScoreTemplate, null));
    }

    @Operation(summary = "查询模板列表", description = "查询模板列表，返回模板列表信息")
    @PostMapping("getTemplateList")
    public Response<PaperScoreTemplateListDTO> getTemplateList(@RequestBody PaperScoreTemplateSearchParam param) {
        PaperScoreTemplateList paperScoreTemplateList = paperScoreTemplateService.getTemplateList(param.getName(), param.getType(), param.getStatus(), param.getPageParams());
        return Response.success(PaperScoreTemplateListDTO.assemblyPaperScoreTemplateDTO(paperScoreTemplateList, userService));
    }

    @Operation(summary = "查询评价短语类型", description = "查询评价短语类型，返回查询评价短语类型列表信息")
    @GetMapping("getTemplateDEPList")
    public Response<String> getTemplateDEPList() {
        return Response.success(PaperScoreTemplateDEPTypeEnum.toJsonArrayString());
    }

    @Operation(summary = "查询教材关联的模板", description = "查询教材关联的模板，返回模板信息及详情")
    @PostMapping("getScoreTemplateRelation")
    public Response<PaperScoreTemplateListDTO> getScoreTemplateRelation(@RequestBody IdParam<String> idParam) {
        PaperScoreTemplateList templateRelationList = paperScoreTemplateRelationService.getTemplateRelationList(idParam.getId());
        return Response.success(PaperScoreTemplateListDTO.assemblyPaperScoreTemplateDTO(templateRelationList, null));
    }

    @Operation(summary = "教材新增关联模板", description = "新增教材与模板的关联关系")
    @PostMapping("addScoreTemplateRelation")
    public Response<Boolean> addScoreTemplateRelation(@RequestBody List<AddPaperScoreTemplateRelationParam> param) {
        return Response.success(paperScoreTemplateRelationService.addTemplateRelation(param.stream().map(AddPaperScoreTemplateRelationParam::toEntity).toList(), getCurrentUserId()));
    }

    @Operation(summary = "教材替换关联模板", description = "修改教材与模板的关联关系")
    @PostMapping("editScoreTemplateRelation")
    public Response<Boolean> editScoreTemplateRelation(@RequestBody List<EditPaperScoreTemplateRelationParam> param) {
        return Response.success("编辑成功", paperScoreTemplateRelationService.editTemplateRelation(param.stream().map(EditPaperScoreTemplateRelationParam::toEntity).toList(), getCurrentUserId()));
    }
}
