package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.menu.MenuListDTO;
import com.unipus.digitalbook.model.entity.menu.Menu;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.model.params.menu.AddMenuParam;
import com.unipus.digitalbook.model.params.menu.ChangePositionParam;
import com.unipus.digitalbook.model.params.menu.EditMenuParam;
import com.unipus.digitalbook.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/menu")
@Tag(name = "菜单相关功能", description = "菜单查询、增、删、改相关接口")
public class MenuController extends BaseController {

    @Resource
    private MenuService menuService;

    @Operation(summary = "获取全部菜单", description = "如果roleId > 0，则回返回当前角色菜单的分配状态")
    @GetMapping("all")
    public Response<MenuListDTO> all(@RequestParam(required = false)Long roleId) {
        List<Menu> all = menuService.all(roleId);
        return Response.success(new MenuListDTO(all));
    }

    @Operation(summary = "获取用户可见菜单", description = "获取用户可见菜单,用于初始化用户菜单页面")
    @GetMapping("list")
    public Response<MenuListDTO> list() {
        Long currentUserId = getCurrentUserId();
        List<Menu> tree = menuService.list(currentUserId, getCurrentOrgId());
        return Response.success(new MenuListDTO(tree));
    }

    @Operation(summary = "新增菜单", description = "用户新增菜单")
    @PostMapping("add")
    public Response<IdDTO<Long>> add(@RequestBody AddMenuParam param) {
        Long menuId = menuService.save(param.toMenu(getCurrentUserId()));
        return Response.success("新增成功", new IdDTO<>(menuId));
    }

    @Operation(summary = "修改菜单", description = "用户修改菜单")
    @PostMapping("edit")
    public Response<IdDTO<Long>> edit(@RequestBody EditMenuParam param) {
        Long menuId = menuService.save(param.toMenu(getCurrentUserId()));
        return Response.success("编辑成功", new IdDTO<>(menuId));
    }

    @Operation(summary = "删除菜单", description = "删除菜单")
    @PostMapping("delete")
    public Response<BooleanDTO> delete(@RequestBody IdParam<Long> idParam) {
        boolean delete = menuService.delete(getCurrentUserId(), idParam.getId());
        return Response.success("删除成功", new BooleanDTO(delete));
    }
    @Operation(summary = "改变菜单位置", description = "改变菜单位置")
    @PostMapping("changePosition")
    public Response<BooleanDTO> changePosition(@RequestBody ChangePositionParam param) {
        boolean sorted = menuService.changePosition(param.getMenuIds());
        return Response.success(new BooleanDTO(sorted));
    }
}
