package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.ApiListDTO;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.role.RoleDTO;
import com.unipus.digitalbook.model.dto.role.RoleListDTO;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.entity.role.RoleSearchList;
import com.unipus.digitalbook.model.enums.RolePermissionTypeEnum;
import com.unipus.digitalbook.model.enums.StatusEnum;
import com.unipus.digitalbook.model.params.IdParam;
import com.unipus.digitalbook.model.params.StatusParam;
import com.unipus.digitalbook.model.params.organization.AssignRoleParam;
import com.unipus.digitalbook.model.params.role.*;
import com.unipus.digitalbook.model.params.user.AssignUserRoleParam;
import com.unipus.digitalbook.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/role")
@Tag(name = "角色相关功能", description = "角色相关接口")
public class RoleController extends BaseController {
    @Resource
    private RoleService roleService;
    @Operation(summary = "新增角色", description = "新增角色, 返回角色ID")
    @PostMapping("add")
    public Response<IdDTO<Long>> add(@RequestBody AddRoleParam roleParam) {
        Long roleId = roleService.save(roleParam.toRole(getCurrentUserId()));
        return Response.success("新增成功", new IdDTO<>(roleId));
    }

    @Operation(summary = "编辑角色", description = "编辑角色, 返回角色ID")
    @PostMapping("edit")
    public Response<IdDTO<Long>> add(@RequestBody EditRoleParam roleParam) {
        Long roleId = roleService.save(roleParam.toRole(getCurrentUserId()));
        return Response.success("编辑成功", new IdDTO<>(roleId));
    }

    @Operation(summary = "删除角色", description = "删除角色, 返回是否删除成功")
    @PostMapping("delete")
    public Response<BooleanDTO> delete(@RequestBody IdParam<Long> idParam) {
        boolean delete = roleService.delete(getCurrentUserId(), idParam.getId());
        return Response.success("删除成功", new BooleanDTO(delete));
    }

    @Operation(summary = "启用/禁用角色", description = "启用/禁用角色, 返回是否成功")
    @PostMapping("updateStatus")
    public Response<BooleanDTO> updateStatus(@RequestBody StatusParam statusParam) {
        boolean status = roleService.updateStatus(getCurrentUserId(),
                statusParam.getId(),
                statusParam.getStatus());
        if (StatusEnum.ENABLE.getCode().equals(statusParam.getStatus())) {
            return Response.success("启用成功", new BooleanDTO(status));
        }
        return Response.success("禁用成功", new BooleanDTO(status));
    }

    @Operation(summary = "获取单个角色信息", description = "根据角色ID获取角色信息")
    @GetMapping("info")
    public Response<RoleDTO> info(Long id) {
        Role role = roleService.getRoleById(id);
        return Response.success(RoleDTO.toRoleDTO(role));
    }

    @Operation(summary = "通过组织ID获取角色列表", description = "获取当前组织可查看的角色列表")
    @GetMapping("orgRoleList")
    public Response<RoleListDTO> orgAssignedRoleList(@RequestParam(required = false) Long orgId) {
        List<Role> roles = roleService.orgAssignedRoleList(orgId == null ? getCurrentOrgId(): orgId);
        return Response.success(new RoleListDTO(roles));
    }

    @Operation(summary = "通过用户id获取用户的角色", description = "通过用户id获取用户的角色")
    @GetMapping("userRoleList")
    public Response<RoleListDTO> userAssignedRoleList(Long userId, @RequestParam(required = false) Long orgId) {
        List<Role> roles = roleService.userAssignedRoleList(userId, orgId == null ? getCurrentOrgId() : orgId);
        return Response.success(new RoleListDTO(roles));
    }

    @Operation(summary = "search", description = "根据条件搜索角色")
    @PostMapping("search")
    public Response<RoleListDTO> search(@RequestBody RoleSearchParam param) {
        PageParams pageParams = param.getPageParams();
        RoleSearchList search = roleService.search(param.getName(), param.getStatus(), pageParams.getOffset(), pageParams.getLimit());
        return Response.success(new RoleListDTO(search));
    }

    @Operation(summary = "分配菜单权限", description = "给角色分配菜单权限")
    @PostMapping("assignMenu")
    public Response<BooleanDTO> assignMenu(@RequestBody AssignMenuParam assignPermissionParam) {
        boolean assigned = roleService.assignRolePermission(assignPermissionParam.toEntity(),
                RolePermissionTypeEnum.MENU, getCurrentUserId());
        return Response.success("菜单权限设置成功", new BooleanDTO(assigned));
    }

    @Operation(summary = "分配API接口权限", description = "给角色分配API接口权限")
    @PostMapping("assignInterface")
    public Response<BooleanDTO> assignInterface(@RequestBody AssignInterfaceParam assignPermissionParam) {
        boolean assigned = roleService.assignRolePermission(assignPermissionParam.toEntity(),
                RolePermissionTypeEnum.INTERFACE, getCurrentUserId());
        return Response.success("接口权限设置成功", new BooleanDTO(assigned));
    }

    @Operation(summary = "新增API接口权限", description = "给角色新增API接口权限")
    @PostMapping("addInterface")
    public Response<BooleanDTO> addInterface(@RequestBody AddInterfaceParam addInterfaceParam) {
        boolean assigned = roleService.assignRolePermission(addInterfaceParam.toEntity(),
                RolePermissionTypeEnum.INTERFACE, getCurrentUserId());
        return Response.success("添加成功", new BooleanDTO(assigned));
    }

    @Operation(summary = "删除API接口权限", description = "给角色新增API接口权限")
    @PostMapping("deleteInterface")
    public Response<BooleanDTO> deleteInterface(@RequestBody DeleteInterfaceParam deleteInterfaceParam) {
        boolean assigned = roleService.assignRolePermission(deleteInterfaceParam.toEntity(),
                RolePermissionTypeEnum.INTERFACE, getCurrentUserId());
        return Response.success("删除成功", new BooleanDTO(assigned));
    }

    @PostMapping("assignUserRoles")
    @Operation(summary = "给用户分配角色", description = "给用户分配角色")
    public Response<Boolean> assignUserRoles(@RequestBody AssignUserRoleParam param) {
        boolean addRole = roleService.bathAssignRole2User(param.toEntities(), getCurrentUserId());
        return Response.success("更新角色成功", addRole);
    }

    @PostMapping("assignOrgRoles")
    @Operation(summary = "给机构分配可用角色", description = "超管的系统管理中，给机构分配可用角色")
    public Response<Boolean> assignOrgRoles(@RequestBody AssignRoleParam param) {
        boolean assigned = roleService.bathAssignRole2Org(param.getRoleIds(), param.getOrgId(), getCurrentUserId());
        return Response.success("机构角色设置成功", assigned);
    }

    @Operation(summary = "根据角色获取接口列表", description = "根据角色获取接口列表, 如果有角色则返回角色的分配状态")
    @GetMapping("getInterface")
    public Response<ApiListDTO> getInterface(@RequestParam(required = false) Long roleId) {
        List<Long> roleIds = (roleId == null || roleId <= 0) ? Collections.emptyList() :List.of(roleId);
        return Response.success(roleService.getInterfaceList(roleIds));
    }
}
