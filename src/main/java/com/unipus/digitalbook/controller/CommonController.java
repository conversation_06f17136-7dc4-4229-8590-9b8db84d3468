package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "通用相关功能", description = "通用相关功能")
public class CommonController {
    @GetMapping("/systemTime")
    public Response<Long> systemTime() {
        return Response.success(System.currentTimeMillis());
    }

}
