package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.AssistantDTO;
import com.unipus.digitalbook.model.dto.assistant.AssistantInfoListDTO;
import com.unipus.digitalbook.model.params.assistant.*;
import com.unipus.digitalbook.model.params.book.BookGreetingParam;
import com.unipus.digitalbook.model.po.assistant.AssistantPO;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.BookSettingService;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTemplateResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTemplateSearchResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTypeListResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@RequestMapping("/assistant")
@Tag(name = "数字人相关功能", description = "数字人相关功能")
public class AssistantController extends BaseController {

    @Resource
    private BookSettingService bookSettingService;

    @Resource
    private AssistantService assistantService;

    @GetMapping("/greeting")
    @Operation(summary = "教材获取全局问候语",
            description = "教材获取全局问候语",
            method = "GET"
    )
    public Response<String> greeting(@RequestParam String bookId) {
        return Response.success(bookSettingService.greeting(bookId));
    }

    @PostMapping("/setGreeting")
    @Operation(summary = "教材设置全局问候语",
            description = "教材设置全局问候语",
            method = "POST"
    )
    public Response<String> setGreeting(@RequestBody BookGreetingParam param) {
        bookSettingService.upsertGreeting(param.getBookId(), param.getGreeting() ,getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/add")
    @Operation(summary = "教材添加数字人",
            description = "教材添加数字人",
            method = "POST"
    )
    public Response<String> add(@Valid @RequestBody AssistantAddParam param) {
        assistantService.addAssistant(param, getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/update")
    @Operation(summary = "教材修改数字人",
            description = "教材修改数字人",
            method = "POST"
    )
    public Response<String> update(@Valid @RequestBody AssistantUpdateParam param) {
        String id = param.getId();
        AssistantPO assistantPO = assistantService.selectById(id);
        Objects.requireNonNull(assistantPO, "AI标注不存在");
        assistantService.updateAssistant(assistantPO.fromUpdateParam(param, getCurrentUserId()));
        return Response.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "教材删除数字人",
            description = "教材删除数字人",
            method = "POST"
    )
    public Response<String> delete(@Valid @RequestBody AssistantDeleteParam param) {
        assistantService.disableById(param.getId());
        return Response.success();
    }

    @PostMapping("/template/list")
    @Operation(summary = "查找数字人模板列表",
            description = "查找数字人模板列表",
            method = "POST"
    )
    public Response<AssistantTemplateSearchResponse> templateList(@RequestBody AssistantTemplateListParam param) {
        return assistantService
                .templateList(getCurrentUserId(), param.getAssistantType());
    }

    @GetMapping("/template/query")
    @Operation(summary = "获取数字人模板信息",
            description = "获取数字人模板信息",
            method = "GET"
    )
    public Response<AssistantTemplateResponse> queryTemplate(@RequestParam("templateId") String templateId) {
        return assistantService.queryTemplate(getCurrentUserId(), templateId);
    }

    @GetMapping("/query")
    @Operation(summary = "获取数字人信息",
            description = "获取数字人信息",
            method = "GET"
    )
    public Response<AssistantDTO> query(@RequestParam("assistantId") String assistantId) {
        return assistantService.queryAssistant(getCurrentUserId(), assistantId);
    }

    @GetMapping("/list")
    @Operation(summary = "获取数字人列表",
            description = "获取数字人列表",
            method = "GET"
    )
    public Response<AssistantInfoListDTO> list(@RequestParam("bookId") String bookId,
                                               @RequestParam("chapterId") String chapterId) {
        return assistantService.instanceList(getCurrentUserId(), bookId, chapterId);
    }

    @GetMapping("/typeList")
    @Operation(summary = "获取助教类型列表",
            description = "获取助教类型列表",
            method = "GET"
    )
    public Response<AssistantTypeListResponse> typeList() {
        return assistantService.assistantTypeList(getCurrentUserId());
    }

    @PostMapping("/selectBlock")
    @Operation(summary = "内容块是否可点击",
            description = "内容块是否可点击",
            method = "POST"
    )
    public Response<String> selectBlocks(@Valid @RequestBody AssistantSelectBlocksParam param) {
        return assistantService.selectBlocks(param);
    }

}
