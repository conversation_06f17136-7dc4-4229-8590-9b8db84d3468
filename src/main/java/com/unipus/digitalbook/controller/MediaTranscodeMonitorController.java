package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.response.Response;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeMessage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 媒体转码监控控制器
 * 提供转码任务的监控和管理功能
 * 
 * <AUTHOR>
 * @date 2024/12/28
 */
@Slf4j
@RestController
@RequestMapping("/media-transcode/monitor")
@Tag(name = "媒体转码监控", description = "媒体转码任务监控和管理相关接口")
public class MediaTranscodeMonitorController {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${kafka.topic.mediaTranscode}")
    private String topicMediaTranscode;

    @Value("${app.env}")
    private String env;

    private static final String REDIS_PROCESSING_KEY_PREFIX = "media:transcode:processing:";

    /**
     * 获取当前处理中的任务列表
     */
    @GetMapping("/processing-tasks")
    @Operation(summary = "获取处理中任务", description = "获取当前正在处理中的转码任务列表")
    public Response<Map<String, Object>> getProcessingTasks() {
        try {
            String pattern = env + ":" + REDIS_PROCESSING_KEY_PREFIX + "*";
            Set<String> keys = stringRedisTemplate.keys(pattern);
            
            Map<String, Object> result = new HashMap<>();
            result.put("count", keys != null ? keys.size() : 0);
            result.put("tasks", keys);
            
            log.info("当前处理中的转码任务数量: {}", keys != null ? keys.size() : 0);
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("获取处理中任务列表失败", e);
            return Response.error("获取处理中任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 清除指定的处理标识
     */
    @DeleteMapping("/processing-flag")
    @Operation(summary = "清除处理标识", description = "手动清除指定的处理中标识")
    public Response<String> clearProcessingFlag(@RequestParam String processingKey) {
        try {
            if (!processingKey.startsWith(env + ":" + REDIS_PROCESSING_KEY_PREFIX)) {
                return Response.error("无效的处理标识Key格式");
            }
            
            Boolean deleted = stringRedisTemplate.delete(processingKey);
            if (Boolean.TRUE.equals(deleted)) {
                log.info("成功清除处理标识: {}", processingKey);
                return Response.success("处理标识已清除");
            } else {
                return Response.error("处理标识不存在或清除失败");
            }
            
        } catch (Exception e) {
            log.error("清除处理标识失败: {}", processingKey, e);
            return Response.error("清除处理标识失败: " + e.getMessage());
        }
    }

    /**
     * 清除所有处理标识
     */
    @DeleteMapping("/processing-flags/all")
    @Operation(summary = "清除所有处理标识", description = "清除所有处理中的标识（慎用）")
    public Response<String> clearAllProcessingFlags() {
        try {
            String pattern = env + ":" + REDIS_PROCESSING_KEY_PREFIX + "*";
            Set<String> keys = stringRedisTemplate.keys(pattern);
            
            if (keys != null && !keys.isEmpty()) {
                Long deletedCount = stringRedisTemplate.delete(keys);
                log.warn("清除了 {} 个处理标识", deletedCount);
                return Response.success("已清除 " + deletedCount + " 个处理标识");
            } else {
                return Response.success("没有找到需要清除的处理标识");
            }
            
        } catch (Exception e) {
            log.error("清除所有处理标识失败", e);
            return Response.error("清除所有处理标识失败: " + e.getMessage());
        }
    }

    /**
     * 手动发送转码消息
     */
    @PostMapping("/send-message")
    @Operation(summary = "发送转码消息", description = "手动发送转码消息到Kafka队列")
    public Response<String> sendTranscodeMessage(@RequestBody MediaTranscodeMessage message) {
        try {
            if (message.getUrl() == null || message.getUrl().trim().isEmpty()) {
                return Response.error("URL不能为空");
            }
            
            // 设置默认重试次数
            if (message.getRetryCount() == null) {
                message.setRetryCount(0);
            }
            
            String messageJson = JsonUtil.toJsonString(message);
            kafkaTemplate.send(topicMediaTranscode, messageJson);
            
            log.info("手动发送转码消息: {}", messageJson);
            return Response.success("转码消息发送成功");
            
        } catch (Exception e) {
            log.error("发送转码消息失败: {}", message, e);
            return Response.error("发送转码消息失败: " + e.getMessage());
        }
    }

    /**
     * 检查特定任务的处理状态
     */
    @GetMapping("/task-status")
    @Operation(summary = "检查任务状态", description = "检查特定转码任务的处理状态")
    public Response<Map<String, Object>> checkTaskStatus(@RequestParam String url, 
                                                        @RequestParam(required = false) String hash) {
        try {
            // 生成处理标识key（模拟MediaTranscodeListener中的逻辑）
            String key = url + ":" + (hash != null ? hash : "");
            String processingKey = env + ":" + REDIS_PROCESSING_KEY_PREFIX + key.hashCode();
            
            Boolean isProcessing = stringRedisTemplate.hasKey(processingKey);
            Long ttl = stringRedisTemplate.getExpire(processingKey, TimeUnit.SECONDS);
            
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("hash", hash);
            result.put("processingKey", processingKey);
            result.put("isProcessing", Boolean.TRUE.equals(isProcessing));
            result.put("ttlSeconds", ttl);
            
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("检查任务状态失败: url={}, hash={}", url, hash, e);
            return Response.error("检查任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取Redis连接状态
     */
    @GetMapping("/redis-status")
    @Operation(summary = "Redis状态", description = "检查Redis连接状态")
    public Response<Map<String, Object>> getRedisStatus() {
        try {
            // 测试Redis连接
            stringRedisTemplate.opsForValue().set("test:connection", "ok", 10, TimeUnit.SECONDS);
            String value = stringRedisTemplate.opsForValue().get("test:connection");
            stringRedisTemplate.delete("test:connection");
            
            Map<String, Object> result = new HashMap<>();
            result.put("connected", "ok".equals(value));
            result.put("timestamp", System.currentTimeMillis());
            
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("检查Redis状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("connected", false);
            result.put("error", e.getMessage());
            return Response.success(result);
        }
    }
}
