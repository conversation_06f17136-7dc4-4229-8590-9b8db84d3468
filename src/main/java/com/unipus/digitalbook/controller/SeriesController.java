package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.SeriesDTO;
import com.unipus.digitalbook.model.entity.Series;
import com.unipus.digitalbook.service.SeriesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/series")
@Tag(name = "系列相关接口", description = "系列相关接口")
public class SeriesController {
    @Resource
    private SeriesService seriesService;

    @Operation(summary = "获取全部系列", description = "获取全部有效系列")
    @GetMapping("all")
    public Response<DataListDTO<SeriesDTO>> all() {
        List<Series> seriesList = seriesService.getAllSeries();
        return Response.success(new DataListDTO<>(seriesList.stream().map(SeriesDTO::new).toList()));
    }
}
