package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.knowledge.BookKnowledgeInfoGetDTO;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeAddParam;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeInfoPO;
import com.unipus.digitalbook.service.KnowledgeService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.Knowledge;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgePrimaryIdRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeTag;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceNodeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeVersionListResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/knowledge")
@Tag(name = "知识图谱相关功能", description = "知识图谱相关功能")
public class KnowledgeController extends BaseController {
    @Resource
    KnowledgeService knowledgeService;


    @GetMapping("/course/get")
    @Operation(summary = "查询教材的知识图谱",
            description = "查询知识图谱",
            method = "Get"
    )
    public Response<BookKnowledgeInfoGetDTO> courseKnowledgeInfoGet(@RequestParam("courseIdStr") String bookId,
                                                                    @RequestParam(required = false, defaultValue = "") Integer status) {
        return Response.success(knowledgeService.courseKnowledgeInfoGet(bookId, status));
    }

    @PostMapping("/add")
    @Operation(summary = "新增知识图谱",
            description = "新增知识图谱，返回知识图谱Id",
            method = "Post"
    )
    public Response<BookKnowledgeInfoPO> knowledgeAdd(@RequestBody KnowledgeAddParam param) {
        return Response.success(knowledgeService.knowledgeAdd(param, getCurrentUserId()));
    }

    @PostMapping("/update")
    @Operation(summary = "更新知识图谱",
            description = "更新知识图谱",
            method = "Post"
    )
    public Response<String> knowledgeUpdate(@RequestBody KnowledgeUpdateRequest request) {
        knowledgeService.knowledgeUpdate(request, getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识图谱",
            description = "删除知识图谱",
            method = "Post"
    )
    public Response knowledgeDelete(@RequestBody KnowledgePrimaryIdRequest request) {
        knowledgeService.knowledgeDelete(request, getCurrentUserId());
        return Response.success();
    }

    @GetMapping("/get")
    @Operation(summary = "查询知识图谱",
            description = "查询知识图谱",
            method = "Get"
    )
    public Response<Knowledge> knowledgeGet(@RequestParam("knowledgeId") String knowledgeId) {
        return Response.success(knowledgeService.knowledgeGet(knowledgeId));
    }

    @PostMapping("/publish")
    @Operation(summary = "发布知识图谱",
            description = "发布知识图谱",
            method = "Post"
    )
    public Response<String> knowledgePublish(@RequestBody KnowledgePrimaryIdRequest request) {
        knowledgeService.knowledgePublish(request, getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/upload")
    @Operation(summary = "上传知识图谱封面",
            description = "上传知识图谱封面",
            method = "Post"
    )
    public Response<String> knowledgeUpload(@RequestParam("file") MultipartFile file) {
        return Response.success(knowledgeService.knowledgeFileUpload(file));
    }

    @GetMapping("/list")
    @Operation(summary = "查询知识图谱列表",
            description = "查询知识图谱列表",
            method = "Get"
    )
    public Response<DataListDTO<Knowledge>> knowledgeList(@RequestParam(required = false,defaultValue = "") String keyword) {
        return Response.success(knowledgeService.knowledgeList(keyword));
    }

    /**
     * 搜索图谱
     *
     * @param query 搜索条件
     * @return 图谱列表响应
     */
    @GetMapping("/search")
    @Operation(summary = "搜索图谱列表", description = "搜索图谱列表")
    public Response<DataListDTO<Knowledge>> knowledgeGraphSearch(@RequestParam(required = false, defaultValue = "") String query) {
        return Response.success(knowledgeService.knowledgeGraphSearch(query));
    }

    /**
     * 获取图谱的知识点信息列表
     *
     * @param knowledgeId
     * @return 获取图谱的知识点信息列表
     */
    @GetMapping("/nodeList")
    @Operation(summary = "图谱的知识点信息列表", description = "图谱的知识点信息列表")
    public Response<DataListDTO<KnowledgeResourceNodeResponse.ResourceNode>> getKnowledgeResourceNodeDetail(@RequestParam String knowledgeId) {
        return Response.success(new DataListDTO<>(knowledgeService.getKnowledgeResourceNodeDetail(knowledgeId)));
    }

    /**
     * 获取资源标签
     *
     * @return 资源标签列表
     */
    @GetMapping("/resourceLabels")
    @Operation(summary = "资源标签列表", description = "资源标签列表")
    public Response<DataListDTO<KnowledgeTag>> getResourceLabelDetail() {
        return Response.success(new DataListDTO<>(knowledgeService.getResourceLabelDetail()));
    }

    /**
     * 生成图谱检测版本
     *
     * @return 生成图谱检测版本
     */
    @GetMapping("/check")
    @Operation(summary = "生成图谱检测版本", description = "生成图谱检测版本")
    public Response knowledgeCheck(@RequestParam String knowledgeId) {
        knowledgeService.knowledgeCheck(knowledgeId);
        return Response.success();
    }

    /**
     * 查看图谱的历史版本
     *
     * @return 查看图谱的历史版本
     */
    @GetMapping("/versionList")
    @Operation(summary = "查看图谱的历史版本", description = "查看图谱的历史版本")
    public Response<DataListDTO<KnowledgeVersionListResponse>> knowledgeVersionList(@RequestParam String knowledgeId) {
        return Response.success(new DataListDTO<>(knowledgeService.knowledgeVersionList(knowledgeId)));
    }
}
