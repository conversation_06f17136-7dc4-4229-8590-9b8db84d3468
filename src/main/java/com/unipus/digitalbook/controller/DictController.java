package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.enums.BusinessTypeEnum;
import com.unipus.digitalbook.model.enums.CourseNatureEnum;
import com.unipus.digitalbook.model.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/dict")
@Tag(name = "获取字典数据相关操作", description = "获取字段数据")
public class DictController {
    @Operation(summary = "获取所有支持的语言")
    @GetMapping("language")
    public Response<DataListDTO<LanguageEnum>> language() {
        return Response.success(new DataListDTO<>(LanguageEnum.getAllLanguages()));
    }

    @Operation(summary = "获取课程性质")
    @GetMapping("courseNature")
    public Response<DataListDTO<CourseNatureEnum>> courseNature() {
        return Response.success(new DataListDTO<>(CourseNatureEnum.getAllCourseNature()));
    }

    @Operation(summary = "获取业务性质")
    @GetMapping("businessType")
    public Response<DataListDTO<BusinessTypeEnum>> businessType() {
        return Response.success(new DataListDTO<>(BusinessTypeEnum.getAllBusinessTypes()));
    }
}
