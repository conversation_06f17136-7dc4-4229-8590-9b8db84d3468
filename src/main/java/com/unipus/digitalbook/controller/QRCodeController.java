package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.qrcode.QrCodeDTO;
import com.unipus.digitalbook.model.dto.qrcode.QrCodeListDTO;
import com.unipus.digitalbook.model.entity.qrcode.QrCode;
import com.unipus.digitalbook.model.entity.qrcode.QrCodeList;
import com.unipus.digitalbook.model.params.IdListParams;
import com.unipus.digitalbook.model.params.qrcode.CreateQrCodeParam;
import com.unipus.digitalbook.model.params.qrcode.QrCodeVerificationParam;
import com.unipus.digitalbook.model.params.qrcode.SearchQrCodeParam;
import com.unipus.digitalbook.model.params.qrcode.UpdateQrCodeParam;
import com.unipus.digitalbook.service.Cms2QrCodeService;
import com.unipus.digitalbook.service.QrCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/qrcode")
@Tag(name = "二维码相关功能", description = "二维码相关接口")
public class QRCodeController extends BaseController {

    @Resource
    private QrCodeService qrCodeService;
    @Resource
    private Cms2QrCodeService cms2QrCodeService;

    /**
     * 创建二维码
     */
    @PostMapping("/create")
    @Operation(summary = "创建二维码", description = "创建二维码", method = "POST")
    public Response<Boolean> createQrCode(@RequestBody CreateQrCodeParam param) {
        Boolean qrCodes = qrCodeService.createQrCodes(
                param.getBookName(),
                param.getBookId(),
                param.getCount(),
                param.getSize(),
                getCurrentUserId()
        );
        if (Boolean.FALSE.equals(qrCodes)) {
            return Response.fail("新建失败");
        }
        return Response.success("新建成功", true);
    }

    /**
     * 更新二维码
     */
    @PostMapping("/update")
    @Operation(summary = "更新二维码", description = "更新二维码", method = "POST")
    public Response<Boolean> updateQrCode(@RequestBody UpdateQrCodeParam param) {
        QrCode qrCode = param.toEntity(getCurrentUserId());
        Boolean result = qrCodeService.updateQrCode(qrCode);
        return Boolean.TRUE.equals(result) ? Response.success("编辑成功", true) : Response.fail("编辑失败");
    }

    /**
     * 获取二维码详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取二维码详情", description = "获取二维码详情，根据二维码id查询", method = "GET")
    public Response<QrCodeDTO> getQrCode(@PathVariable Long id) {
        QrCode qrCode = qrCodeService.getQrCodeById(id);
        if (qrCode == null) {
            return Response.fail("二维码不存在。");
        }
        return Response.success(new QrCodeDTO(qrCode));
    }

    /**
     * 分页查询二维码列表
     */
    @PostMapping("/list")
    @Operation(summary = "分页查询二维码列表", description = "分页查询二维码列表", method = "POST")
    public Response<QrCodeListDTO> listQrCodes(@RequestBody SearchQrCodeParam param) {
        QrCodeList qrCodeList = qrCodeService.searchQrCodes(param);
        return Response.success(new QrCodeListDTO(qrCodeList));
    }

    /**
     * 保存二维码验证结果
     */
    @PostMapping("/verify/saveResult")
    @Operation(summary = "保存二维码验证结果", description = "保存二维码验证结果", method = "POST")
    public Response<Boolean> saveVerificationResults(@RequestBody QrCodeVerificationParam param) {
        Boolean result = qrCodeService.verifyQrCodeLink(param.getVerificationResults(), getCurrentUserId());
        return Response.success(result);
    }

    @GetMapping("/display")
    @Operation(summary = "查看二维码", description = "查看二维码，根据二维码id查询", method = "GET")
    public void displayQrCode(@RequestParam("id") Long id, HttpServletResponse response) {
        QrCode qrCode = qrCodeService.getQrCodeById(id);
        if (qrCode != null) {
            response.setHeader("QrCodeSize", qrCode.getQrCodeSize());
        }
        cms2QrCodeService.displayQrCode(id, response);
    }

    @GetMapping("/download")
    @Operation(summary = "下载二维码", description = "下载二维码，根据二维码id查询", method = "GET")
    public void downloadQrCode(@RequestParam("id") Long id, HttpServletResponse response) {
        cms2QrCodeService.downloadQrCode(id, response);
    }

    @PostMapping("/downloadZip")
    @Operation(summary = "打包下载二维码", description = "打包下载二维码，根据二维码id查询", method = "POST")
    public void downloadQrCodeZip(@RequestBody IdListParams param, HttpServletResponse response) {
        cms2QrCodeService.downloadQrCodeZip(param.getIdList(), response);
    }

    @PostMapping("/exportRelational")
    @Operation(summary = "导出二维码关系数据", description = "导出二维码关系数据，根据二维码id查询", method = "POST")
    public void exportQrCodeRelational(@RequestBody IdListParams param, HttpServletResponse response) {
        qrCodeService.exportQrCodeRelational(param.getIdList(), getCurrentUserId(), response);
    }
}
