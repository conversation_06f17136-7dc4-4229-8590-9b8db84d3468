package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.chapter.*;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterTemplate;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.params.book.AddChapterTemplateParam;
import com.unipus.digitalbook.model.params.book.AddChapterVersionParam;
import com.unipus.digitalbook.model.params.book.EditChapterInfoParam;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.ChapterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/chapter")
@Tag(name = "教材章节相关功能", description = "教材章节相关接口")
public class ChapterController extends BaseController {
    @Resource
    private ChapterService chapterService;
    @Resource
    private BookService bookService;


    @GetMapping("/list")
    @Operation(summary = "根据教材ID查询章节列表", description = "根据教材ID查询对应的章节信息")
    public Response<ChapterListDTO> getChaptersByBookId(String bookId) {
        Long editorId = bookService.getEditorId(bookId);
        if (editorId == null) {
            return Response.fail("教材编辑者信息不存在");
        }
        List<Chapter> chapterList = chapterService.getChaptersByBookId(bookId).getChapterList();
        if (CollectionUtils.isEmpty(chapterList)) {
            return Response.success(new ChapterListDTO());
        }
        List<String> chapterIds = chapterList.stream().map(Chapter::getId).toList();
        Map<String, List<ResourceUser>> chapterEditorMap = bookService.getChapterCollaborators(chapterIds, getCurrentOrgId());
        return Response.success(new ChapterListDTO(editorId, chapterList, chapterEditorMap, getCurrentUserId()));
    }

    @GetMapping("/latestList")
    @Operation(summary = "根据教材ID查询最新上架的章节列表", description = "根据教材ID查询最新上架的章节列表信息")
    public Response<ChapterLatestListDTO> getLastChaptersByBookId(@RequestParam String bookId,
                                                                  @RequestParam(required = false,defaultValue = "") String versionNum) {
        Book book = bookService.getBookByBookIdAndVersion(bookId, versionNum);
        if (book == null || CollectionUtils.isEmpty(book.getChapterList())) {
            return Response.success(new ChapterLatestListDTO());
        }
        List<Long> chapterVersionIdList = book.getChapterList().stream().map(Chapter::getChapterVersion).map(ChapterVersion::getId).toList();
        Map<String, Chapter> chapterMap = book.getChapterList().stream().collect(Collectors.toMap(Chapter::getId, Function.identity()));
        if (org.springframework.util.CollectionUtils.isEmpty(chapterVersionIdList)) {
            return Response.success(new ChapterLatestListDTO());
        }
        List<ChapterVersion> chapterVersions = chapterService.getChapterVersionByVersionIdList(chapterVersionIdList);
        if (CollectionUtils.isEmpty(chapterVersions)) {
            return Response.success(new ChapterLatestListDTO());
        }
        ChapterLatestListDTO result = new ChapterLatestListDTO();
        List<ChapterLatestListDTO.ChapterLatestDTO> chapterLatestDTOS = new ArrayList<>();
        result.setChapterLatestList(chapterLatestDTOS);
        for (ChapterVersion chapterVersion : chapterVersions) {
            Chapter chapter = chapterMap.get(chapterVersion.getChapterId());
            ChapterLatestListDTO.ChapterLatestDTO chapterLatestDTO = new ChapterLatestListDTO.ChapterLatestDTO();
            chapterLatestDTO.setId(chapterVersion.getChapterId());
            chapterLatestDTO.setBookId(bookId);
            chapterLatestDTO.setChapterNumber(chapter.getChapterNumber());
            chapterLatestDTO.setName(chapter.getName());
            chapterLatestDTO.setVersionNumber(chapterVersion.getVersionNumber());
            chapterLatestDTOS.add(chapterLatestDTO);
        }
        return Response.success(result);
    }

    @PostMapping("/editChapter")
    @Operation(summary = "编辑教材信息的章节", description = "编辑教材信息的章节(当前只有章节名称)")
    public Response<IdDTO<String>> addChapter(@RequestBody EditChapterInfoParam param) {
        Boolean res = chapterService.editChapterInfo(param.toEntity(), getCurrentUserId());
        if (Boolean.FALSE.equals(res)) {
            return Response.fail("编辑章节信息失败。");
        }
        return Response.success("章节名称编辑成功", null);
    }

    @PostMapping("/addChapterVersion")
    @Operation(summary = "添加教材的章节版本", description = "添加教材的章节版本（包含正文内容）")
    public Response<String> addChapterVersion(@RequestBody AddChapterVersionParam param) {
        String chapterVersionId = chapterService.addChapterVersion(param.toEntity(getCurrentUserId()));
        if (StringUtils.isEmpty(chapterVersionId)) {
            return Response.fail("章节内容保存失败。");
        }
        return Response.success("章节保存成功",chapterVersionId);
    }

    @GetMapping("/batchAddChapterContentUploadUrl")
    @Operation(summary = "为所有章节内容设置上传URL", description = "为所有章节内容设置上传URL")
    public Response<Boolean> batchAddChapterContentUploadUrl(@RequestParam(required = false) String chapterId) {
        chapterService.batchAddChapterContentUploadUrl(chapterId);
        return Response.success(true);
    }

    @GetMapping("/getLatestChapterVersionByChapterId")
    @Operation(summary = "获取章节的最后保存版本", description = "获取章节的最后保存版本（包含正文内容）")
    public Response<ChapterWithContentDTO> getLatestVersionByChapterId(String chapterId) {
        Chapter chapter = chapterService.getLatestVersionByChapterId(chapterId);
        if (chapter == null) {
            return Response.fail("未查询到章节的最新内容");
        }
        ChapterWithContentDTO chapterVersion = new ChapterWithContentDTO();
        chapterVersion.fromEntity(chapter);
        return Response.success(chapterVersion);
    }

    @GetMapping("/getChapterWithVersionByIdAndVersionNumber")
    @Operation(summary = "根据章节ID和章节版本号,获取章节信息", description = "根据章节ID和章节版本号,获取章节信息（包含正文内容）")
    public Response<ChapterWithContentDTO> getChapterWithVersionByIdAndVersionNumber(@RequestParam String chapterId, @RequestParam String versionNumber) {
        Chapter chapter = chapterService.getChapterWithVersionByIdAndVersionNumber(chapterId, versionNumber);
        if (chapter == null) {
            return Response.fail("未查询到章节的版本内容");
        }
        ChapterWithContentDTO chapterVersion = new ChapterWithContentDTO();
        chapterVersion.fromEntity(chapter);
        return Response.success(chapterVersion);
    }

    @GetMapping("/getVersionListByChapterId")
    @Operation(summary = "获取章节的所有保存版本列表", description = "获取章节的所有保存版本列表（不包含章节内容）")
    public Response<VersionListDTO> getVersionListByChapterId(String chapterId) {
        List<ChapterVersion> chapterVersion = chapterService.getVersionListByChapterId(chapterId);
        if (CollectionUtils.isEmpty(chapterVersion)){
            return Response.success(new VersionListDTO());
        }
        VersionListDTO versionListDTO = new VersionListDTO();
        for (ChapterVersion version : chapterVersion) {
            ChapterVersionSimpleInfoDTO chapterVersionSimpleInfoDTO = new ChapterVersionSimpleInfoDTO();
            chapterVersionSimpleInfoDTO.fromEntity(version);
            versionListDTO.getChapterVersionList().add(chapterVersionSimpleInfoDTO);
        }
        return Response.success(versionListDTO);
    }

    @PostMapping("/deleteChapter")
    @Operation(summary = "删除教材章节", description = "删除教材章节")
    public Response<Boolean> deleteChapter(@RequestParam(value="chapterId") String chapterId) {
        return Response.success("章节删除成功", chapterService.deleteChapter(chapterId, getCurrentUserId()));
    }


    @GetMapping("/getCatalogByBookId")
    @Operation(summary = "根据教材ID获取教材目录", description = "根据教材ID获取教材目录")
    public Response<ChapterCatalogListDTO> getCatalogByBookId(@RequestParam("bookId") String bookId) {
        List<ChapterVersion> latestCatalogList = chapterService.getLatestCatalogByBookId(bookId);
        if (CollectionUtils.isEmpty(latestCatalogList)) {
            return Response.success();
        }
        return Response.success(new ChapterCatalogListDTO(bookId, latestCatalogList));
    }


    @GetMapping("/getResourceByBookId")
    @Operation(summary = "根据教材ID获取教材目录", description = "根据教材ID获取教材目录")
    public Response<ChapterResourceListDTO> getResourceByBookId(@RequestParam("bookId") String bookId) {
        List<ChapterVersion> latestResourceList = chapterService.getLatestResourceByBookId(bookId);
        if (CollectionUtils.isEmpty(latestResourceList)) {
            return Response.success();
        }
        return Response.success(new ChapterResourceListDTO(bookId, latestResourceList));
    }

    @PostMapping("/addChapterTemplate")
    @Operation(summary = "添加教材的章节版本", description = "添加教材的章节版本（包含正文内容）")
    public Response<ChapterTemplateDTO> addChapterTemplate(@RequestBody AddChapterTemplateParam param) {
        ChapterTemplate chapterTemplate = chapterService.addChapterTemplate(param.toEntity(getCurrentUserId()));
        if (chapterTemplate == null) {
            return Response.fail("章节模版保存失败。");
        }
        ChapterTemplateDTO chapterTemplateDTO = new ChapterTemplateDTO();
        chapterTemplateDTO.fromEntity(chapterTemplate);
        return Response.success("章节模版保存成功", chapterTemplateDTO);
    }

    @GetMapping("/getChapterTemplateListByChapterId")
    @Operation(summary = "根据章节ID获取章节模版", description = "根据教材ID获取教材目录")
    public Response<ArrayList<ChapterTemplateDTO>> getChapterTemplateByChapterId(@RequestParam("chapterId") String chapterId) {
        List<ChapterTemplate> chapterTemplateList = chapterService.getChapterTemplateListByChapterIdList(Collections.singletonList(chapterId));
        return getTemplateListResponse(chapterTemplateList);
    }

    @GetMapping("/getChapterTemplateListByBookId")
    @Operation(summary = "根据章节ID获取章节模版", description = "根据教材ID获取教材目录")
    public Response<ArrayList<ChapterTemplateDTO>> getChapterTemplateListByBookId(@RequestParam("bookId") String bookId) {
        List<ChapterTemplate> chapterTemplateList = chapterService.getChapterTemplateListByBookId(bookId);
        return getTemplateListResponse(chapterTemplateList);
    }

    private Response<ArrayList<ChapterTemplateDTO>> getTemplateListResponse(List<ChapterTemplate> chapterTemplateList) {
        if (CollectionUtils.isEmpty(chapterTemplateList)) {
            return Response.success();
        }
        ArrayList<ChapterTemplateDTO> chapterTemplateDTOList = new ArrayList<>();
        for (ChapterTemplate chapterTemplate : chapterTemplateList) {
            ChapterTemplateDTO chapterTemplateDTO = new ChapterTemplateDTO();
            chapterTemplateDTO.fromEntity(chapterTemplate);
            chapterTemplateDTOList.add(chapterTemplateDTO);
        }
        return Response.success(chapterTemplateDTOList);
    }

}
