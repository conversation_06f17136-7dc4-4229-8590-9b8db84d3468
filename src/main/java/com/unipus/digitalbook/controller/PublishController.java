package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.publish.*;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookChangeItemEntity;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceList;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.params.publish.BookPublishParam;
import com.unipus.digitalbook.model.params.publish.BookPublishedVersionParam;
import com.unipus.digitalbook.model.params.publish.ChapterCheckParam;
import com.unipus.digitalbook.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/publish")
@Tag(name = "教材上架功能", description = "教材上架功能相关接口")
public class PublishController extends BaseController {

    @Resource
    private PublishService publishService;
    @Resource
    private BookVersionService bookVersionService;
    @Resource
    private BookService bookService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private ComplementResourceService complementResourceService;
    @Resource
    private UserService userService;
    @Resource
    private PaperVersionService paperVersionService;

    @GetMapping("/getCheckInfo")
    @Operation(summary = "取得教材上架检测初始化数据", description = "取得教材上架检测初始化数据")
    public Response<BookPublishCheckDTO> getCheckInfo(@RequestParam("bookId") String bookId) {
        Book book = bookService.getBookById(bookId);
        if (book == null) {
            throw new IllegalArgumentException("未找到教材信息，教材ID: " + bookId);
        }
        UserInfo userInfo = userService.getUserInfo(book.getEditorId());
        if (userInfo == null) {
            throw new IllegalArgumentException("未找到教材编辑人信息，教材ID: " + bookId);
        }
        // 处理教材下的章节信息
        List<Chapter> chapterList = chapterService.getLatestChapterByBookId(bookId);
        book.setChapterList(chapterList);
        // 处理配套资源信息
        ComplementResourceList complementResourceList = complementResourceService.getResourceByBookId(bookId);
        if (complementResourceList != null && !CollectionUtils.isEmpty(complementResourceList.getComplementResourceList())) {
            book.fillComplementResourceList(complementResourceList.getComplementResourceList());
        }
        // 取得教材已上架版本的次数
        Integer publishCount = bookVersionService.getBookPublishedVersionCount(bookId);
        // 取得教材最新上架版本信息
        BookVersion bookLastPublishedVersion = bookVersionService.getBookLastPublishedVersion(bookId);
        Book publishedBook = null;
        if (bookLastPublishedVersion != null) {
            publishedBook = bookService.getBookByVersionId(bookLastPublishedVersion.getId());
        }
        return Response.success(new BookPublishCheckDTO(book, publishedBook, userInfo.getName(), publishCount));
    }

    @PostMapping("/getChapterCheckInfo")
    @Operation(summary = "获取章节上架检测版本列表", description = "获取章节上架检测版本列表")
    public Response<ChapterPublishCheckListDTO> getChapterCheckInfo(@RequestBody ChapterCheckParam param) {
        String chapterId = param.getChapterId();
        // 查询总数量
        Integer totalCount = chapterService.getVersionCountByChapterId(chapterId);
        if (totalCount == null || totalCount == 0) {
            return Response.success(new ChapterPublishCheckListDTO(Collections.emptyList(), 0));
        }
        // 分页查询章节版本列表
        List<ChapterVersion> chapterVersionList = chapterService.getVersionPageListByChapterId(chapterId, param.getPageParams());
        List<Long> chapterVersionIds = chapterVersionList.stream().map(ChapterVersion::getId).toList();
        // 建立章节版本与试卷的映射
        Map<Long, List<Paper>> paperChapterVersionMapping = paperVersionService.getPaperChapterVersionMapping(chapterVersionIds, null, null);
        // 对象转换
        List<ChapterPublishCheckDTO> chapterPublishCheckList = chapterVersionList.stream()
                .map(chapterVersion -> {
                    List<Paper> currentChapterPaperList = paperChapterVersionMapping.get(chapterVersion.getId());
                    Date publishedTime = chapterService.getFirstPublishedTimeByChapterVersionId(chapterVersion.getId());
                    List<Paper> publishedChapterPaperList = null;
                    if (!CollectionUtils.isEmpty(currentChapterPaperList)) {
                        publishedChapterPaperList = paperVersionService.getLastPublishedPaperList(currentChapterPaperList.stream().map(Paper::getPaperId).toList());
                    }
                    return new ChapterPublishCheckDTO(chapterVersion, publishedTime, currentChapterPaperList, publishedChapterPaperList);
                })
                .toList();
        return Response.success(new ChapterPublishCheckListDTO(chapterPublishCheckList, totalCount));
    }

    @PostMapping("/publish")
    @Operation(summary = "教材上架到指定平台", description = "教材上架到指定平台")
    public Response<HashMap<String, String>> publish(@RequestBody BookPublishParam params) {
        return Response.success(publishService.publish(params.toEntity(), getCurrentUserId()));
    }

    @PostMapping("/getPublishedBookVersion")
    @Operation(summary = "根据条件查询教材所有已上架版版本信息", description = "根据条件查询教材所有已上架版版本信息")
    public Response<BookPublishedVersionListDTO> getBookPublishedVersion(@RequestBody BookPublishedVersionParam param) {
        return Response.success(new BookPublishedVersionListDTO(bookVersionService.getBookPublishedVersion(param)));
    }

    @GetMapping("/getPublishedBookVersionDetail")
    @Operation(summary = "获取上架版本详情，比较当前版本和前一版本的信息", description = "获取上架版本详情，比较当前版本和前一版本的信息")
    public Response<BookPublishedDetailDTO> getBookPublishedVersionDetail(@RequestParam Long bookVersionId) {
        Book currentBook = bookService.getBookByVersionId(bookVersionId);
        if (currentBook == null) {
            throw new IllegalArgumentException("未找到教材信息，教材版本ID: " + bookVersionId);
        }
        // 获取当前版本的上架包列表
        List<BookChangeItemEntity> publishedItemList = bookService.getBookChangeListByVersionId(bookVersionId);
        // 获取前一版本信息
        BookVersion previousBookVersion = bookVersionService.getBookPreviousVersionById(bookVersionId);
        Book previousBook = null;
        if (previousBookVersion != null) {
            previousBook = bookService.getBookByVersionId(previousBookVersion.getId());
        }
        return Response.success(new BookPublishedDetailDTO(currentBook, previousBook, publishedItemList));
    }
}
