package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookFileListDTO;
import com.unipus.digitalbook.model.entity.book.BookFile;
import com.unipus.digitalbook.model.params.IdListParams;
import com.unipus.digitalbook.model.params.book.AddBookFileParam;
import com.unipus.digitalbook.model.params.book.BatchAddBookFileParam;
import com.unipus.digitalbook.service.BookFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/bookFile")
@Tag(name = "教材文件相关功能", description = "单纯为了编辑老师的提效工具，提供了这些教材文件相关接口")
public class BookFileController extends BaseController{
    @Resource
    private BookFileService bookFileService;

    @PostMapping("/batchAddBookFile")
    @Operation(summary = "批量添加教材文件", description = "批量添加教材文件")
    Response<Boolean> batchAddBookFile(@RequestBody BatchAddBookFileParam param){
        if (param.getBookFileList() == null){
            return Response.fail("参数错误");
        }
        List<BookFile> bookFiles = param.getBookFileList().stream().map(AddBookFileParam::toEntity).toList();
        return Boolean.TRUE.equals(bookFileService.addBookFiles(bookFiles,getCurrentUserId())) ?
                Response.success() : Response.fail();
    }

    @PostMapping("/deleteBookFile")
    @Operation(summary = "删除教材文件", description = "删除教材文件")
    Response<Boolean> deleteBookFile(@RequestBody IdListParams param){
        return Boolean.TRUE.equals(bookFileService.deleteBookFiles(param.getIdList())) ?
                Response.success() : Response.fail();
    }

    @GetMapping("/getAllBookFileListByBookId")
    @Operation(summary = "获取教材所有文件列表", description = "获取教材所有文件列表")
    Response<BookFileListDTO> getAllBookFileListByBookId(String bookId){
        List<BookFile> bookFileList = bookFileService.getAllBookFileListByBookId(bookId);
        if (bookFileList == null){
            return Response.success(new  BookFileListDTO());
        }
        return Response.success(new BookFileListDTO(bookFileList));
    }
}
