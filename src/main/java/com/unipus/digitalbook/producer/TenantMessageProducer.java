package com.unipus.digitalbook.producer;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.model.entity.tenant.TenantMessage;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.TenantSubscribeService;
import com.unipus.digitalbook.task.TenantMessageProducerTask;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class TenantMessageProducer {

    @Resource(name = "virtualThreadExecutor")
    private ExecutorService virtualThreadExecutor;

    @Resource
    private TenantMessageProducerTask tenantMessageProducerTask;

    @Resource
    private TenantSubscribeService tenantSubscribeService;

    private <S, T> CompletableFuture<T> produce(Long tenantId, MessageTopicEnum messageTopicEnum, TypeReference<T> targetType,
                                             boolean order, boolean async, S message, TypeReference<S> sourceType) {

        checkMessage(messageTopicEnum, targetType, message, sourceType);
        String messageTopic = messageTopicEnum.name();
        String messageUuid = UUID.randomUUID().toString();
        TenantMessage<S> tenantMessage = new TenantMessage<>(tenantId, messageTopic, messageUuid, async, message);

        if (order) {
            tenantMessageProducerTask.saveMessage(tenantMessage);
            return CompletableFuture.completedFuture(null);
        }

        if (async) {
            return CompletableFuture.supplyAsync(() ->
                    tenantMessageProducerTask.sendMessage(tenantMessage, targetType), virtualThreadExecutor)
                .exceptionally(e -> {
                    tenantMessageProducerTask.saveMessage(tenantMessage);
                    if (e instanceof TenantMessageException e1) {
                        throw e1;
                    }
                    throw new TenantMessageException(e);
                });
        }

        try {
            T response = tenantMessageProducerTask.sendMessage(tenantMessage, targetType);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            tenantMessageProducerTask.saveMessage(tenantMessage);
            return CompletableFuture.failedFuture(e instanceof TenantMessageException ? e : new TenantMessageException(e));
        }
    }

    private <S, T> void checkMessage(MessageTopicEnum messageTopicEnum, TypeReference<T> targetType, S message, TypeReference<S> sourceType) {

        if (message == null) {
            throw new TenantMessageException(ProduceResultEnum.MESSAGE_IS_EMPTY.getMessage());
        }

         if (sourceType == null || !sourceType.getType().equals(messageTopicEnum.getSourceType().getType())) {
             throw new TenantMessageException(ProduceResultEnum.PARAM_TYPE_ERROR.getMessage());
         }

        if (targetType == null || !targetType.getType().equals(messageTopicEnum.getTargetType().getType())) {
            log.error("messageTopic: {} 源类型: {} 目标类型: {}", messageTopicEnum.name(), messageTopicEnum.getSourceType(), targetType);
            throw new TenantMessageException(ProduceResultEnum.RETURN_TYPE_ERROR.getMessage());
        }

    }

    public <S, T> T produceSyncMessage(Long tenantId, MessageTopicEnum messageTopicEnum,
                                       TypeReference<T> targetType, S message, TypeReference<S> sourceType) {

        return produce(tenantId, messageTopicEnum, targetType, false, false, message, sourceType).join();
    }

    public <S, T> CompletableFuture<T> produceAsyncMessage(Long tenantId, MessageTopicEnum messageTopicEnum,
                                                           TypeReference<T> targetType, S message, TypeReference<S> sourceType) {
        return produce(tenantId, messageTopicEnum, targetType,false, true, message, sourceType);
    }

    @Deprecated
    public <S> void produceAsyncMessageInOrder(Long tenantId, MessageTopicEnum messageTopicEnum, S message, TypeReference<S> sourceType) {
        produce(tenantId, messageTopicEnum, new TypeReference<Void>() {},true, true, message, sourceType);
    }

    public TenantSubscribePO getTenantSubscribe(Long tenantId, MessageTopicEnum messageTopicEnum) {
        return tenantSubscribeService.selectByTenantIdAndMessageTopic(tenantId, messageTopicEnum.name());
    }

}
