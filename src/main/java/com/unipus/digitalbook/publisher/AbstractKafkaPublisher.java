package com.unipus.digitalbook.publisher;

import com.unipus.digitalbook.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class AbstractKafkaPublisher<T> {

    private final KafkaTemplate<String, String> kafkaTemplate;

    protected AbstractKafkaPublisher(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * 获取 Kafka Topic 名称（由子类指定）
     */
    protected abstract String getTopic();

    protected abstract String getMessageKey(T message);

    /**
     * 异步发送消息
     */
    public void send(T message) {
        String topic = getTopic();
        String key = getMessageKey(message);
        if (!StringUtils.hasText(topic)) {
            log.warn("Kafka Topic 为空，跳过发送: {}", message);
            return;
        }
        CompletableFuture<SendResult<String, String>> sendFuture;
        if (key != null) {
            sendFuture = kafkaTemplate.send(topic, key, JsonUtil.toJsonString(message));
        } else {
            sendFuture = kafkaTemplate.send(topic, JsonUtil.toJsonString(message));
        }
        sendFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                log.error("Kafka消息发送失败，topic={}, key={}, message={}, error={}", topic, key, message, ex.getMessage(), ex);
            }
        });
    }
}