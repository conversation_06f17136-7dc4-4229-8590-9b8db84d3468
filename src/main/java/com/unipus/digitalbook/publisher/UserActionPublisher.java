package com.unipus.digitalbook.publisher;
import com.unipus.digitalbook.model.events.UserActionEvent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class UserActionPublisher extends AbstractKafkaPublisher<UserActionEvent>{
    @Value("${kafka.topic.userAction}")
    private String topic;

    public UserActionPublisher(KafkaTemplate<String, String> kafkaTemplate) {
        super(kafkaTemplate);
    }
    @Override
    protected String getTopic() {
        return topic;
    }

    @Override
    protected String getMessageKey(UserActionEvent message) {
        return message.getTenantId() + ":" + message.getOpenId();
    }
}
