package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.QuestionEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class QuestionEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public QuestionEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void questionEventPublisher(BigQuestionGroup questionGroup, EventTypeEnum eventType, Long opsUserId) {
        eventPublisher.publishEvent(new QuestionEvent(this, questionGroup, eventType, opsUserId));
    }
}
