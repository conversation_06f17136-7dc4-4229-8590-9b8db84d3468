package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.BookEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class BookEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public BookEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void bookEventPublisher(String bookId, EventTypeEnum eventType, Long opsUserId) {
        eventPublisher.publishEvent(new BookEvent(this, bookId, eventType, opsUserId));
    }
}
