package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.events.TableChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class TableChangeEventPublisher {

    private static final List<String> IGNAL_TABLE_LIST = List.of("async_fail_log");

    private final ApplicationEventPublisher eventPublisher;

    public TableChangeEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void tableChangeEventPublisher(Object source, String tableName, String operationType,
                                          Object param, String sql, String operator) {
        if(IGNAL_TABLE_LIST.contains(tableName)){
            log.debug("忽略表变更事件: {}", tableName);
            return;
        }
        log.debug("Event Source: {}", source);
        log.debug("Table: {}", tableName);
        log.debug("Operation: {}", operationType);
        log.debug("Param: {}", param);
        log.debug("Change Time: {}", DateUtil.dateTimeFormat(Date.from(Instant.now())));
        log.debug("Operator: {}", operator);

        // 构造事件
        TableChangeEvent tableChangeEvent = new TableChangeEvent(source, tableName, operationType, param, sql, operator);
        // 虚拟线程发布事件
        Thread.startVirtualThread(() -> eventPublisher.publishEvent(tableChangeEvent));
    }
}
