package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.UserAnswerEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserAnswerEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public UserAnswerEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void userAnswerEventPublisher(EventTypeEnum eventType, BigQuestionGroup question, List<UserAnswer> userAnswers, SubmitAnswerContext context) {
        eventPublisher.publishEvent(new UserAnswerEvent(this, eventType, question, userAnswers, context));
    }
}
