package com.unipus.digitalbook.publisher.standalone;


import com.unipus.digitalbook.model.events.UserDeleteEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public UserEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void publishUserDeleteEvent(List<Long> userIds, Long opUserId) {
        UserDeleteEvent event = new UserDeleteEvent(this, userIds,opUserId);
        eventPublisher.publishEvent(event);
    }
}