package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.events.ResourcePermitChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 发布资源权限变更事件
 */
@Service
@Slf4j
public class ResourcePermitEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public ResourcePermitEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    /**
     * 发布资源权限变更事件
     * @param oldPermissions 旧的数据权限实体列表
     * @param newPermissions 新的数据权限实体列表
     * @param eventSource 事件来源
     * @param opUserId 操作用户id
     */
    public void publishPermitChangeEvent(List<ResourcePermission> oldPermissions, List<ResourcePermission> newPermissions,
                                         String eventSource, Long opUserId){
        // 参数验证
        if (CollectionUtils.isEmpty(newPermissions) || !StringUtils.hasText(eventSource) || opUserId == null) {
            throw new IllegalArgumentException("Invalid parameters");
        }
        List<ResourcePermission> oldList = CollectionUtils.isEmpty(oldPermissions) ? new ArrayList<>() : oldPermissions;

        // 设置新的数据权限
        List<ResourcePermission> permissions = new ArrayList<>();
        newPermissions.forEach(np -> {
            // 获取旧的权限类型
            Optional<ResourcePermission> target = oldList.stream().filter(p -> p.compareResourceAndUser(np)).findFirst();
            Integer oldPermissionType = target.map(ResourcePermission::getPermissionType).orElse(null);
            // 设置新的权限类型
            ResourcePermission permission = new ResourcePermission(
                    np.getResourceId(),
                    np.getResourceType(),
                    np.getUserId(),
                    np.getPermissionType(), oldPermissionType);
            // 追加到权限列表中
            permissions.add(permission);
        });

        // 构造事件
        ResourcePermitChangeEvent event = new ResourcePermitChangeEvent(eventSource, permissions, opUserId);

        // 广播事件（虚拟线程发送更新事件，保证订阅者异步处理）
        Thread.startVirtualThread(() -> eventPublisher.publishEvent(event));
        log.debug("广播资源权限变更事件: {}", JsonUtil.toJsonString(event));
    }
}