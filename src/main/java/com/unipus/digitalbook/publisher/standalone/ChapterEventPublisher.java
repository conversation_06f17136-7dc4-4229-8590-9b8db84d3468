package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.ChapterEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class ChapterEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public ChapterEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void chapterEventPublisher(String bookId, String chapterId, EventTypeEnum eventType, Long opsUserId) {
        eventPublisher.publishEvent(new ChapterEvent(this, bookId, chapterId, eventType, opsUserId));
    }
}
