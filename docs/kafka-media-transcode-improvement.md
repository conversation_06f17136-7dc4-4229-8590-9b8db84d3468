# Kafka媒体转码处理改进方案

## 概述

基于最小修改原则，对现有的Kafka媒体转码处理进行改进，解决消息重复消费问题，同时保持系统的稳定性和可靠性。

## 问题分析

### 原有问题
1. **消费者Poll超时**：转码任务耗时过长，超过`max.poll.interval.ms`配置
2. **消息重复消费**：消费者被踢出消费者组后，消息被重新分配给其他消费者
3. **处理重复**：同一个转码任务可能被多个消费者同时处理

### 解决思路
1. **异步处理**：将耗时任务放到异步线程中，主线程最多等待3分钟
2. **Redis标识**：使用Redis标识正在处理的任务，避免重复处理
3. **手动ACK**：精确控制消息确认时机，确保处理完成后才ACK

## 核心改进

### 1. 异步处理机制

```java
// 异步处理转码任务，最多等待3分钟
CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
    processTranscodeTask(mediaTranscodeMessage, formats);
});

// 等待异步任务完成，最多3分钟
future.get(ASYNC_TIMEOUT_MINUTES, TimeUnit.MINUTES);
```

**优势**：
- 主线程不会长时间阻塞
- 避免消费者Poll超时
- 保持消费者组稳定性

### 2. Redis处理标识

```java
// 检查是否正在处理中
if (isProcessing(processingKey)) {
    log.info("任务正在处理中，放弃消费并重新发送到队列");
    return; // 不ACK，让消息重新回到队列
}

// 设置处理中标识
setProcessing(processingKey);
```

**标识规则**：
- Key格式：`{env}:media:transcode:processing:{url:hash的hashCode}`
- 过期时间：10分钟（防止异常情况下标识永久存在）
- 处理完成后自动清除

### 3. 手动ACK控制

```java
try {
    // 处理转码任务
    future.get(ASYNC_TIMEOUT_MINUTES, TimeUnit.MINUTES);
    ack.acknowledge(); // 处理成功，手动ACK
} catch (Exception e) {
    // 处理失败，根据重试次数决定
    if (retryCount < MAX_RETRY_COUNT) {
        // 重新发送到队列
        kafkaTemplate.send(topicMediaTranscode, retryMessage);
    }
    ack.acknowledge(); // 无论成功失败都ACK，避免无限重复
} finally {
    clearProcessing(processingKey); // 清除处理标识
}
```

## 处理流程

### 消息处理流程

```mermaid
graph TD
    A[接收Kafka消息] --> B[检查消息格式]
    B --> C{格式有效?}
    C -->|否| D[ACK并返回]
    C -->|是| E[生成处理标识Key]
    E --> F{Redis中存在标识?}
    F -->|是| G[放弃处理，不ACK]
    F -->|否| H[设置处理标识]
    H --> I[检查转码格式]
    I --> J{需要转码?}
    J -->|否| K[ACK并返回]
    J -->|是| L[异步处理转码]
    L --> M{3分钟内完成?}
    M -->|是| N[ACK成功]
    M -->|否| O{重试次数<最大值?}
    O -->|是| P[发送重试消息]
    O -->|否| Q[记录最终失败]
    P --> R[ACK避免重复]
    Q --> R
    N --> S[清除处理标识]
    R --> S
```

### 重试机制

1. **任务级重试**：单个转码格式失败时重试（保持原有逻辑）
2. **全局重试**：整个任务失败时重试，最多2次
3. **超时处理**：3分钟超时后进入重试流程

## 配置说明

### Kafka配置
```yaml
spring:
  kafka:
    consumer:
      max-poll-interval-ms: 600000  # 10分钟
      session-timeout-ms: 60000     # 1分钟
      heartbeat-interval-ms: 20000  # 20秒
```

### 应用配置
```java
private static final int ASYNC_TIMEOUT_MINUTES = 3; // 异步任务超时
private static final String REDIS_PROCESSING_KEY_PREFIX = "media:transcode:processing:";
private static final int REDIS_PROCESSING_EXPIRE_SECONDS = 600; // 10分钟过期
```

## 监控和管理

### REST API接口

1. **获取处理中任务**
   ```http
   GET /api/media-transcode/monitor/processing-tasks
   ```

2. **检查任务状态**
   ```http
   GET /api/media-transcode/monitor/task-status?url=xxx&hash=xxx
   ```

3. **清除处理标识**
   ```http
   DELETE /api/media-transcode/monitor/processing-flag?processingKey=xxx
   ```

4. **手动发送消息**
   ```http
   POST /api/media-transcode/monitor/send-message
   ```

### 日志监控

- **INFO**：正常处理流程
- **WARN**：重复消息、Redis异常等
- **ERROR**：处理失败、超时等

## 异常处理

### Redis异常处理
```java
private boolean isProcessing(String processingKey) {
    try {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(processingKey));
    } catch (Exception e) {
        log.warn("检查Redis处理标识失败，默认允许处理", e);
        return false; // Redis异常时默认允许处理
    }
}
```

### 超时处理
- 3分钟超时后自动进入重试流程
- 保持消费者响应性
- 避免长时间占用消费者线程

## 测试验证

### 单元测试
- `MediaTranscodeListenerTest` - 完整的功能测试
- Redis标识测试
- 重复消息处理测试
- 超时处理测试

### 集成测试
```bash
mvn test -Dtest=MediaTranscodeListenerTest
```

## 部署注意事项

1. **Redis可用性**：确保Redis集群稳定运行
2. **监控告警**：监控处理中任务数量和处理时间
3. **日志分析**：关注重复消息和超时日志
4. **性能调优**：根据实际情况调整超时时间

## 优势总结

1. **最小修改**：基于现有代码结构，改动最小
2. **向后兼容**：保持原有接口和行为不变
3. **可靠性高**：多重保障机制，避免消息丢失
4. **可监控性**：提供完整的监控和管理接口
5. **故障恢复**：Redis异常时自动降级处理

这个改进方案在保持系统稳定性的同时，有效解决了消息重复消费问题，提供了更好的可观测性和可维护性。
