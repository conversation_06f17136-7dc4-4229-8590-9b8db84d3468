#!/usr/bin/env bash
cd builds/
# JOB_NAME is the name of the project of this build. This is the name you gave your job. It is set up by <PERSON>
COMPOSE_ID=${JOB_NAME:-local}
echo "composeId:"
echo $COMPOSE_ID
# Remove Previous Stack

docker-compose -f docker-compose-build.yml -p $COMPOSE_ID down
docker-compose -f docker-compose-build.yml -p $COMPOSE_ID rm -f

# Function to check if a network exists
network_exists() {
  docker network ls --filter name=$1 --quiet | grep -q .
}

# Check if the network exists, and create it if not
if ! network_exists "jenkins_build_ipublish"; then
  echo "Network 'jenkins_build_ipublish' does not exist, creating now..."
  docker network create jenkins_build_ipublish
else
  echo "Network 'jenkins_build_ipublish' already exists."
fi

# Starting new stack environment
docker-compose -f docker-compose-build.yml -p $COMPOSE_ID up -d

echo "wait for start 5s"
sleep 5

docker-compose -f docker-compose-build.yml -p $COMPOSE_ID exec --privileged -T main sh builds/build.sh
