# Write docker-compose here. eg:
version: '3'

services:
  main:
    image: "swr.cn-north-4.myhuaweicloud.com/unipus/unipus-base:maven-3.9.9-eclipse-temurin-21"
    volumes:
      - ../:/opt/ipublish
      - /data/.m2/repository:/opt/maven_local_repository
    working_dir: /opt/ipublish
    command: ["sh", "-c",  "pwd && ls && cp builds/dockerfile/settings.xml /usr/share/maven/conf/ && while true; do sleep 1; done"]
    networks:
      - jenkins_build_ipublish
networks:
  jenkins_build_ipublish:
    external: true
