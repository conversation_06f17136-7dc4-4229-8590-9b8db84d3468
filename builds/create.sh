#!/bin/bash
VERSION=$1
NAMESPACE=$2
CONFIGPATH=$3

LOG=$( git log --pretty=format:%s -1 )
rm -rf /opt/jellyfish
<NAME_EMAIL>:ocean/jellyfish.git /opt/jellyfish
cd /opt/jellyfish
git checkout ipublish
git pull

cd /opt/jellyfish/${CONFIGPATH}/${NAMESPACE}
/var/kustomize edit set image swr.cn-north-4.myhuaweicloud.com/unipus/ipublish/service:${VERSION}
sed  -i "s/resources/bases/g" kustomization.yaml
git add .
git commit -m "${LOG}"
git push
rm -rf /opt/jellyfish
echo 'deploy success!'

