pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                sh 'git checkout uat'
                sh 'git pull'
            }
        }
        stage('Build') {
            steps {
                sh 'git checkout uat'
                sh 'sh ./builds/run_build.sh'
                sh 'docker build -f ./builds/dockerfile/Dockerfile -t swr.cn-north-4.myhuaweicloud.com/unipus/ipublish/service:uat_ipublish-service_v${BUILD_NUMBER} .'
                sh 'docker push swr.cn-north-4.myhuaweicloud.com/unipus/ipublish/service:uat_ipublish-service_v${BUILD_NUMBER}'
            }
        }
        stage('Deploy') {
            steps {
                dir('builds') {
                    sh 'sh ./create.sh uat_ipublish-service_v${BUILD_NUMBER} uat ipublish-service'
                }
            }
        }
    }
    post {
        success {
            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'xterm']) {
                sh 'sh ./builds/success.sh'
            }
        }
        failure {
            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'xterm']) {
                sh 'sh ./builds/failure.sh'
            }
        }
    }
}

