pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                sh '$(aws ecr get-login --no-include-email)'
                sh 'git checkout main'
                sh 'git pull'
            }
        }
        stage('Build') {
            steps {
                sh 'git checkout main'
                sh 'sh ./builds/run_build.sh'
                sh 'docker build -f ./builds/dockerfile/Dockerfile -t swr.cn-north-4.myhuaweicloud.com/unipus/ipublish/service:prod_ipublish-service_v${BUILD_NUMBER} .'
                sh 'docker push swr.cn-north-4.myhuaweicloud.com/unipus/ipublish/service:prod_ipublish-service_v${BUILD_NUMBER}'
            }
        }
    }
    post {
        success {
            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'xterm']) {
                sh 'sh ./builds/success.sh'
            }
        }
        failure {
            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'xterm']) {
                sh 'sh ./builds/failure.sh'
            }
        }
    }
}

