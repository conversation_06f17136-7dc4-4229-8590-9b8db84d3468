# Write Dockerfile here. eg:
FROM swr.cn-north-4.myhuaweicloud.com/unipus/unipus-base:21.0.5_11-jdk-noble-tini

# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./target/DigitalBook.jar /opt
COPY ./target/用户导入模板.xlsx /opt
COPY ./target/single-choice.md /opt

WORKDIR /opt/config
COPY ./builds/dockerfile/entrypoint.sh /opt/config

ENTRYPOINT ["/sbin/tini", "--", "sh", "entrypoint.sh"]