<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <pluginGroups>
        <pluginGroup>org.sonarsource.scanner.maven</pluginGroup>
    </pluginGroups>
    <localRepository>/opt/maven_local_repository</localRepository>

    <servers>
        <server>
            <id>nexus-releases</id>
            <username>deployment</username>
            <password>deployment123</password>
        </server>
        <server>
            <id>nexus-snapshots</id>
            <username>deployment</username>
            <password>deployment123</password>
        </server>
    </servers>

    <mirrors>
        <mirror>
            <id>nexus-http</id>
            <mirrorOf>*</mirrorOf>
            <url>http://nexus-hw.unipus.cn:8081/nexus/content/groups/public/</url>
            <name>Nexus HTTP Repository</name>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>edu</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.8</jdk>
            </activation>
            <repositories>
                <!-- 私有库地址,公网地址 ***************，局域网地址 ********** -->
                <repository>
                    <id>nexus</id>
                    <url>http://nexus-hw.unipus.cn:8081/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <!--插件库地址-->
                <pluginRepository>
                    <id>nexus</id>
                    <url>http://nexus-hw.unipus.cn:8081/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- Example for MySQL-->
                <sonar.jdbc.url>
                    *********************************************************************************
                </sonar.jdbc.url>
                <sonar.jdbc.username>root</sonar.jdbc.username>
                <sonar.jdbc.password>root</sonar.jdbc.password>

                <!-- Optional URL to server. Default value is http://localhost:9000 -->
                <sonar.host.url>
                    http://**********:8080/sonarqube
                </sonar.host.url>
            </properties>
        </profile>

        <profile>
            <id>central-repos</id>
            <repositories>
                <repository>
                    <id>central</id>
                    <name>central</name>
                    <url>http://nexus-hw.unipus.cn:8081/nexus/content/repositories/central/</url>
                    <!--   <url>http://**********:8081/nexus/content/repositories/central/</url>
                                         <url>https://mvnrepository.com/artifact/</url> -->
                    <!-- <url>https://maven.aliyun.com/repository/central</url> -->
                    <!--<url>https://repo1.maven.org/maven2/</url> -->
                    <releases><enabled>true</enabled><updatePolicy>always</updatePolicy></releases>
                    <snapshots><enabled>true</enabled><updatePolicy>always</updatePolicy></snapshots>
                </repository>
            </repositories>
        </profile>

        <profile>
            <id>jdk-1.8</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>

    </profiles>

    <!--激活profile-->
    <activeProfiles>
        <activeProfile>edu</activeProfile>
        <activeProfile>central-repos</activeProfile>
    </activeProfiles>
</settings>