#!/usr/bin/env bash
cd builds/
# JOB_NAME is the name of the project of this build. This is the name you gave your job. It is set up by <PERSON>
COMPOSE_ID=${JOB_NAME:-local}
# Remove Previous Stack

docker-compose -f docker-compose-test.yml -p $COMPOSE_ID down
docker-compose -f docker-compose-test.yml -p $COMPOSE_ID rm -f

# Starting new stack environment
docker-compose -f docker-compose-test.yml -p $COMPOSE_ID up -d

sleep 5

docker-compose -f docker-compose-test.yml -p $COMPOSE_ID exec --privileged -T main sh builds/test.sh

