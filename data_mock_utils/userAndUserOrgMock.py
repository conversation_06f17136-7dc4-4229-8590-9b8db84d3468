import random
import string
from faker import Faker

# 初始化Faker和随机种子
fake = Faker("zh_CN")  # 设置为中文
random.seed(42)

# 性别映射
gender_map = {0: "女", 1: "男", 2: "未知"}

# 状态映射
status_map = {0: "未激活", 1: "已激活"}

def generate_random_user_data(user_count=20):
    users = []

    for user_id in range(1, user_count + 1):
        name = fake.name() + " (测试数据)"
        cell_phone = f"1{random.randint(3, 9)}{random.randint(100000000, 999999999)}"  # 生成中国手机号
        email = fake.email()
        gender = random.choice(list(gender_map.keys()))
        desc = fake.sentence(nb_words=6) + " (测试数据)"
        avatar_url = f"https://i.pravatar.cc/150?img={random.randint(1, 70)}"  # 公共头像地址
        enable = random.choice([True, False])
        create_time = fake.date_time_this_year()
        update_time = fake.date_time_this_year()
        create_by = random.randint(1, 10)
        update_by = random.randint(1, 10)
        active_time = fake.date_time_this_year() if enable else None
        status = random.choice(list(status_map.keys()))

        # 可能不存在的 sso_id
        sso_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8)) if random.random() > 0.5 else None

        users.append({
            "id": user_id,
            "sso_id": sso_id,
            "name": name,
            "cell_phone": cell_phone,
            "email": email,
            "gender": gender,
            "desc": desc,
            "avatar_url": avatar_url,
            "enable": enable,
            "create_time": create_time.strftime('%Y-%m-%d %H:%M:%S'),
            "update_time": update_time.strftime('%Y-%m-%d %H:%M:%S'),
            "create_by": create_by,
            "update_by": update_by,
            "active_time": active_time.strftime('%Y-%m-%d %H:%M:%S') if active_time else None,
            "status": status,
        })

    return users

def generate_org_user_relations(users, org_count=39):
    relations = []
    for user in users:
        org_ids = random.sample(range(1, org_count + 1), random.randint(1, 5))  # 每个用户随机属于1到5个机构
        for org_id in org_ids:
            join_time = fake.date_time_this_year()
            leave_time = fake.date_time_this_year() if random.random() > 0.8 else None  # 20%概率生成离开时间
            status = random.choice([1, 2, 3])
            remark = fake.sentence(nb_words=4) + " (测试数据)"

            relations.append({
                "user_id": user["id"],
                "org_id": org_id,
                "join_time": join_time.strftime('%Y-%m-%d %H:%M:%S'),
                "leave_time": leave_time.strftime('%Y-%m-%d %H:%M:%S') if leave_time else None,
                "status": status,
                "remark": remark,
                "enable": random.choice([0, 1]),
            })
    return relations

def generate_insert_statements(users):
    sql_statements = []
    for user in users:
        sso_id_value = 'NULL' if user['sso_id'] is None else f"'{user['sso_id']}'"
        active_time_value = 'NULL' if user['active_time'] is None else f"'{user['active_time']}'"

        sql_statements.append(f"""
        INSERT INTO user_info (id, sso_id, name, cell_phone, email, gender, `desc`, avatar_url, enable, create_time, update_time, create_by, update_by, active_time, status)
        VALUES (
            {user['id']}, 
            {sso_id_value},
            '{user['name']}', 
            '{user['cell_phone']}', 
            '{user['email']}',
            {user['gender']}, 
            '{user['desc']}', 
            '{user['avatar_url']}', 
            {1 if user['enable'] else 0},
            '{user['create_time']}', 
            '{user['update_time']}', 
            {user['create_by']}, 
            {user['update_by']},
            {active_time_value}, 
            {user['status']}
        );
        """)
    return "\n".join(sql_statements)

def generate_relation_statements(relations):
    sql_statements = []
    for relation in relations:
        leave_time_value = 'NULL' if relation['leave_time'] is None else f"'{relation['leave_time']}'"

        sql_statements.append(f"""
        INSERT INTO org_user_relation (user_id, org_id, join_time, leave_time, status, remark, enable)
        VALUES (
            {relation['user_id']},
            {relation['org_id']},
            '{relation['join_time']}',
            {leave_time_value},
            {relation['status']},
            '{relation['remark']}',
            {relation['enable']}
        );
        """)
    return "\n".join(sql_statements)

# 生成用户数据和关系数据
user_data = generate_random_user_data()
relation_data = generate_org_user_relations(user_data)

# 生成并打印SQL插入语句
insert_user_statements = generate_insert_statements(user_data)
insert_relation_statements = generate_relation_statements(relation_data)

print(insert_user_statements)
print(insert_relation_statements)
